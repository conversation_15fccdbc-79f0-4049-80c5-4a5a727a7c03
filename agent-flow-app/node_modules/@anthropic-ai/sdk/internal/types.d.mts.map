{"version": 3, "file": "types.d.mts", "sourceRoot": "", "sources": ["../src/internal/types.ts"], "names": [], "mappings": "AAEA,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAM,MAAM,UAAU,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,CAAC;AAErE,MAAM,MAAM,QAAQ,CAAC,CAAC,IAAI;KAAG,CAAC,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;CAAE,CAAC;AAE7D,MAAM,MAAM,oBAAoB,GAAG,WAAW,GAAG;IAAE,OAAO,EAAE,OAAO,CAAA;CAAE,CAAC;AAEtE,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAEjD;;GAEG;AACH,KAAK,oBAAoB,CAAC,CAAC,IACzB,CAAC,SAAS,CACR;IACE,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;CAC7B,CACF,GACC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GACb,CAAC,SAAS,CACV;IACE,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;CAC7B,CACF,GACC,CAAC,GAAG,CAAC,GAAG,CAAC,GACT,CAAC,SAAS,CACV;IACE,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAC5B,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;CAC7B,CACF,GACC,CAAC,GAAG,CAAC,GACL,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,KAAK,OAAO,GAAG,CAAC,GAC3C,KAAK,CAAC;AAGV;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,6CAA6C;AAC7C,KAAK,sBAAsB,GAAG,MAAM,CAAC,OAAO,yCAAyC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,4CAA4C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,+CAA+C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,kDAAkD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,qDAAqD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,wDAAwD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,2DAA2D,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,8DAA8D,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,iEAAiE,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,oEAAoE,EAAE,WAAW,CAAC,CAAC;AACn3B,uCAAuC;AACvC,KAAK,iBAAiB,GAAG,MAAM,CAAC,OAAO,mCAAmC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,sCAAsC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,yCAAyC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,4CAA4C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,+CAA+C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,kDAAkD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,qDAAqD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,wDAAwD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,2DAA2D,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,8DAA8D,EAAE,WAAW,CAAC,CAAC;AAClzB,4CAA4C;AAC5C,KAAK,cAAc,GAAG,UAAU,CAAC,gBAAgB,CAAC;AAClD,6CAA6C;AAC7C,KAAK,qBAAqB,GAAG,MAAM,CAAC,OAAO,8CAA8C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,iDAAiD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,oDAAoD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,uDAAuD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,0DAA0D,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,6DAA6D,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,gEAAgE,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,mEAAmE,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,sEAAsE,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,yEAAyE,EAAE,WAAW,CAAC,CAAC;AACp6B,mHAAmH;AACnH,KAAK,qBAAqB,GAAI,MAAM,CAAC,OAAO,4BAA4B,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,+BAA+B,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,kCAAkC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,qCAAqC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,wCAAwC,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,2CAA2C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,8CAA8C,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,iDAAiD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,oDAAoD,EAAE,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,uDAAuD,EAAE,WAAW,CAAC,CAAC;AACjvB,wCAAwC;AACxC,KAAK,gBAAgB,GAAG,WAAW,CAAC,oBAAoB,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAG3E,KAAK,YAAY,GACb,MAAM,CAAC,sBAAsB,CAAC,GAC9B,MAAM,CAAC,iBAAiB,CAAC,GACzB,MAAM,CAAC,cAAc,CAAC,GACtB,MAAM,CAAC,qBAAqB,CAAC,GAC7B,MAAM,CAAC,qBAAqB,CAAC,GAC7B,MAAM,CAAC,WAAW,CAAC,GACnB,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAE7B;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,YAAY;AAC1C,sFAAsF;AACtF,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,GAAG,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC"}