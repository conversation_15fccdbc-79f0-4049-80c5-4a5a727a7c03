{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../src/internal/utils/log.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AA8DtF,8BAsBC;AAlFD,wCAAkC;AAalC,MAAM,YAAY,GAAG;IACnB,GAAG,EAAE,CAAC;IACN,KAAK,EAAE,GAAG;IACV,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,GAAG;CACX,CAAC;AAEK,MAAM,aAAa,GAAG,CAC3B,UAA8B,EAC9B,UAAkB,EAClB,MAAqB,EACC,EAAE;IACxB,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,IAAA,eAAM,EAAC,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC;QACrC,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CACpB,GAAG,UAAU,eAAe,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,qBAAqB,IAAI,CAAC,SAAS,CACvF,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC1B,EAAE,CACJ,CAAC;IACF,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAjBW,QAAA,aAAa,iBAiBxB;AAEF,SAAS,IAAI,KAAI,CAAC;AAElB,SAAS,SAAS,CAAC,OAAqB,EAAE,MAA0B,EAAE,QAAkB;IACtF,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,CAAC;QACN,8DAA8D;QAC9D,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;AACH,CAAC;AAED,MAAM,UAAU,GAAG;IACjB,KAAK,EAAE,IAAI;IACX,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,IAAI,aAAa,GAAG,eAAe,CAAC,IAAI,OAAO,EAA8B,CAAC;AAE9E,SAAgB,SAAS,CAAC,MAAqB;IAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC1C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAI,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;QACjD,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,WAAW,GAAG;QAClB,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;QAC3C,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;KAC5C,CAAC;IAEF,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;IAEnD,OAAO,WAAW,CAAC;AACrB,CAAC;AAEM,MAAM,oBAAoB,GAAG,CAAC,OAWpC,EAAE,EAAE;IACH,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,8BAA8B;IACnE,CAAC;IACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAClC,CAAC,OAAO,CAAC,OAAO,YAAY,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAC/F,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC;YACjB,IAAI;YACJ,CACE,IAAI,CAAC,WAAW,EAAE,KAAK,WAAW;gBAClC,IAAI,CAAC,WAAW,EAAE,KAAK,eAAe;gBACtC,IAAI,CAAC,WAAW,EAAE,KAAK,QAAQ;gBAC/B,IAAI,CAAC,WAAW,EAAE,KAAK,YAAY,CACpC,CAAC,CAAC;gBACD,KAAK;gBACP,CAAC,CAAC,KAAK;SACR,CACF,CACF,CAAC;IACJ,CAAC;IACD,IAAI,qBAAqB,IAAI,OAAO,EAAE,CAAC;QACrC,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,mBAAmB,CAAC;QAChD,CAAC;QACD,OAAO,OAAO,CAAC,mBAAmB,CAAC;IACrC,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAxCW,QAAA,oBAAoB,wBAwC/B"}