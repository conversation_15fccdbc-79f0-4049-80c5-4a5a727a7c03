{"version": 3, "file": "path.mjs", "sourceRoot": "", "sources": ["../../src/internal/utils/path.ts"], "names": [], "mappings": "OAAO,EAAE,cAAc,EAAE;AAEzB;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,OAAO,GAAG,CAAC,OAAO,CAAC,kCAAkC,EAAE,kBAAkB,CAAC,CAAC;AAC7E,CAAC;AAED,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAEjF,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,WAAW,GAAG,aAAa,EAAE,EAAE,CACnE,SAAS,IAAI,CAAC,OAA0B,EAAE,GAAG,MAA0B;IACrE,mDAAmD;IACnD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,OAAO,CAAC,CAAC,CAAE,CAAC;IAE7C,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;QACjE,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC;QACxE,IACE,KAAK,KAAK,MAAM,CAAC,MAAM;YACvB,CAAC,KAAK,IAAI,IAAI;gBACZ,CAAC,OAAO,KAAK,KAAK,QAAQ;oBACxB,kCAAkC;oBAClC,KAAK,CAAC,QAAQ;wBACZ,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAE,KAAa,CAAC,cAAc,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;4BAC3F,EAAE,QAAQ,CAAC,CAAC,EACpB,CAAC;YACD,OAAO,GAAG,KAAK,GAAG,EAAE,CAAC;YACrB,eAAe,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,aAAa,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM;gBACjD,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,iBAAiB,MAAM,CAAC,SAAS,CAAC,QAAQ;qBAC9C,IAAI,CAAC,KAAK,CAAC;qBACX,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;aAChD,CAAC,CAAC;QACL,CAAC;QACD,OAAO,aAAa,GAAG,YAAY,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACjF,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;IAC3C,MAAM,qBAAqB,GAAG,oCAAoC,CAAC;IACnE,IAAI,KAAK,CAAC;IAEV,4BAA4B;IAC5B,OAAO,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC/D,eAAe,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;YACvB,KAAK,EAAE,UAAU,KAAK,CAAC,CAAC,CAAC,+CAA+C;SACzE,CAAC,CAAC;IACL,CAAC;IAED,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAElD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACxD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;YACzC,OAAO,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,MAAM,IAAI,cAAc,CACtB,0DAA0D,eAAe;aACtE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;aACnB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,SAAS,EAAE,CACvC,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEJ;;GAEG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,eAAe,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC"}