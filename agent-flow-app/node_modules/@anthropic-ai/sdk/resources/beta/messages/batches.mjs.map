{"version": 3, "file": "batches.mjs", "sourceRoot": "", "sources": ["../../../src/resources/beta/messages/batches.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OAIf,EAAE,IAAI,EAAgC;OACtC,EAAE,YAAY,EAAE;OAEhB,EAAE,YAAY,EAAE;OAChB,EAAE,cAAc,EAAE;OAClB,EAAE,IAAI,EAAE;AAEf,MAAM,OAAO,OAAQ,SAAQ,WAAW;IACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,MAAM,CAAC,MAAyB,EAAE,OAAwB;QACxD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACzD,IAAI;YACJ,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CACN,cAAsB,EACtB,SAAiD,EAAE,EACnD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAA,wBAAwB,cAAc,YAAY,EAAE;YAC9E,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAI,CACF,SAA6C,EAAE,EAC/C,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,EAAE,CAAA,IAAsB,CAAA,EAAE;YACvF,KAAK;YACL,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,cAAsB,EACtB,SAA+C,EAAE,EACjD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAA,wBAAwB,cAAc,YAAY,EAAE;YACjF,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,MAAM,CACJ,cAAsB,EACtB,SAA+C,EAAE,EACjD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA,wBAAwB,cAAc,mBAAmB,EAAE;YACtF,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,OAAO,CACX,cAAsB,EACtB,SAAyC,EAAE,EAC3C,OAAwB;QAExB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,MAAM,IAAI,cAAc,CACtB,yDAAyD,KAAK,CAAC,iBAAiB,MAAM,KAAK,CAAC,EAAE,EAAE,CACjG,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO;aAChB,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAC;gBACpB;oBACE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;oBAC7E,MAAM,EAAE,oBAAoB;iBAC7B;gBACD,OAAO,EAAE,OAAO;aACjB,CAAC;YACF,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,IAAI;SACvB,CAAC;aACD,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,CAEvF,CAAC;IACJ,CAAC;CACF"}