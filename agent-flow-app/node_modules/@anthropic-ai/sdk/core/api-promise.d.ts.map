{"version": 3, "file": "api-promise.d.ts", "sourceRoot": "", "sources": ["../src/core/api-promise.ts"], "names": [], "mappings": "OAEO,EAAE,KAAK,aAAa,EAAE;OAEtB,EAAE,KAAK,cAAc,EAAE;OACvB,EACL,KAAK,gBAAgB,EACrB,KAAK,aAAa,EAGnB;AAED;;;GAGG;AACH,qBAAa,UAAU,CAAC,CAAC,CAAE,SAAQ,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;;IAMxD,OAAO,CAAC,eAAe;IACvB,OAAO,CAAC,aAAa;IANvB,OAAO,CAAC,aAAa,CAAwC;gBAI3D,MAAM,EAAE,aAAa,EACb,eAAe,EAAE,OAAO,CAAC,gBAAgB,CAAC,EAC1C,aAAa,GAAE,CACrB,MAAM,EAAE,aAAa,EACrB,KAAK,EAAE,gBAAgB,KACpB,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAwB;IAW9D,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IAMjF;;;;;;;;;;OAUG;IACH,UAAU,IAAI,OAAO,CAAC,QAAQ,CAAC;IAI/B;;;;;;;;;;;OAWG;IACG,YAAY,IAAI,OAAO,CAAC;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,QAAQ,EAAE,QAAQ,CAAC;QAAC,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAA;KAAE,CAAC;IAKrG,OAAO,CAAC,KAAK;IASJ,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,KAAK,EACzD,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,EAChG,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAClF,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAItB,KAAK,CAAC,OAAO,GAAG,KAAK,EAC5B,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAChF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;IAI7B,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;CAGzF"}