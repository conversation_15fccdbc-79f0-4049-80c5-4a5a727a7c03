{"version": 3, "file": "api-promise.js", "sourceRoot": "", "sources": ["../src/core/api-promise.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;;;AAKtF,gDAK2B;AAE3B;;;GAGG;AACH,MAAa,UAAc,SAAQ,OAAyB;IAI1D,YACE,MAAqB,EACb,eAA0C,EAC1C,gBAGgC,4BAAoB;QAE5D,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE;YAChB,yEAAyE;YACzE,0EAA0E;YAC1E,wBAAwB;YACxB,OAAO,CAAC,IAAW,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAXK,oBAAe,GAAf,eAAe,CAA2B;QAC1C,kBAAa,GAAb,aAAa,CAGuC;QAR9D,qCAAuB;QAgBrB,+BAAA,IAAI,sBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,WAAW,CAAI,SAAkD;QAC/D,OAAO,IAAI,UAAU,CAAC,+BAAA,IAAI,0BAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAChF,IAAA,oBAAY,EAAC,SAAS,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CACxF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC9E,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;IAC5E,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAC5C,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,+BAAA,IAAI,0BAAQ,EAAE,IAAI,CAAqC,CACrF,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAEQ,IAAI,CACX,WAAgG,EAChG,UAAmF;QAEnF,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;IAEQ,KAAK,CACZ,UAAiF;QAEjF,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAEQ,OAAO,CAAC,SAA2C;QAC1D,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;CACF;AApFD,gCAoFC"}