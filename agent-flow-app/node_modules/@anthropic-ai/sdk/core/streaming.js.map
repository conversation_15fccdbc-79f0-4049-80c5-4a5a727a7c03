{"version": 3, "file": "streaming.js", "sourceRoot": "", "sources": ["../src/core/streaming.ts"], "names": [], "mappings": ";;;;AAsNA,4CAgCC;;AAtPD,sCAAyC;AAEzC,gDAAuD;AACvD,uDAAgF;AAChF,gDAAkE;AAClE,kDAAkD;AAClD,wDAAoD;AACpD,sDAAqD;AACrD,kDAAkD;AAGlD,sCAAmC;AAUnC,MAAa,MAAM;IAIjB,YACU,QAAmC,EAC3C,UAA2B,EAC3B,MAAsB;QAFd,aAAQ,GAAR,QAAQ,CAA2B;QAH7C,iCAAmC;QAOjC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,+BAAA,IAAI,kBAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,eAAe,CACpB,QAAkB,EAClB,UAA2B,EAC3B,MAAsB;QAEtB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAA,eAAS,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAEpD,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,sBAAc,CAAC,0EAA0E,CAAC,CAAC;YACvG,CAAC;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;oBAC/D,IAAI,GAAG,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;wBAC/B,IAAI,CAAC;4BACH,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC7B,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;4BAC7D,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;4BACrC,MAAM,CAAC,CAAC;wBACV,CAAC;oBACH,CAAC;oBAED,IACE,GAAG,CAAC,KAAK,KAAK,eAAe;wBAC7B,GAAG,CAAC,KAAK,KAAK,eAAe;wBAC7B,GAAG,CAAC,KAAK,KAAK,cAAc;wBAC5B,GAAG,CAAC,KAAK,KAAK,qBAAqB;wBACnC,GAAG,CAAC,KAAK,KAAK,qBAAqB;wBACnC,GAAG,CAAC,KAAK,KAAK,oBAAoB,EAClC,CAAC;wBACD,IAAI,CAAC;4BACH,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC7B,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;4BAC7D,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;4BACrC,MAAM,CAAC,CAAC;wBACV,CAAC;oBACH,CAAC;oBAED,IAAI,GAAG,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;wBACzB,SAAS;oBACX,CAAC;oBAED,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;wBAC1B,MAAM,IAAI,gBAAQ,CAAC,SAAS,EAAE,IAAA,iBAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC7F,CAAC;gBACH,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,kFAAkF;gBAClF,IAAI,IAAA,qBAAY,EAAC,CAAC,CAAC;oBAAE,OAAO;gBAC5B,MAAM,CAAC,CAAC;YACV,CAAC;oBAAS,CAAC;gBACT,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB,CACvB,cAA8B,EAC9B,UAA2B,EAC3B,MAAsB;QAEtB,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,SAAS,CAAC,CAAC,SAAS;YACvB,MAAM,WAAW,GAAG,IAAI,kBAAW,EAAE,CAAC;YAEtC,MAAM,IAAI,GAAG,IAAA,qCAA6B,EAAQ,cAAc,CAAC,CAAC;YAClE,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;gBAC/B,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7C,MAAM,IAAI,CAAC;gBACb,CAAC;YACH,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;gBACvC,MAAM,IAAI,CAAC;YACb,CAAC;QACH,CAAC;QAED,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,sBAAc,CAAC,0EAA0E,CAAC,CAAC;YACvG,CAAC;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,SAAS,EAAE,EAAE,CAAC;oBACrC,IAAI,IAAI;wBAAE,SAAS;oBACnB,IAAI,IAAI;wBAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACnC,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,kFAAkF;gBAClF,IAAI,IAAA,qBAAY,EAAC,CAAC,CAAC;oBAAE,OAAO;gBAC5B,MAAM,CAAC,CAAC;YACV,CAAC;oBAAS,CAAC;gBACT,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED,kCAAC,MAAM,CAAC,aAAa,EAAC;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,GAAG;QACD,MAAM,IAAI,GAAyC,EAAE,CAAC;QACtD,MAAM,KAAK,GAAyC,EAAE,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEjC,MAAM,WAAW,GAAG,CAAC,KAA2C,EAAuB,EAAE;YACvF,OAAO;gBACL,IAAI,EAAE,GAAG,EAAE;oBACT,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO,KAAK,CAAC,KAAK,EAAG,CAAC;gBACxB,CAAC;aACF,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,+BAAA,IAAI,sBAAQ,CAAC;YAClE,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,+BAAA,IAAI,sBAAQ,CAAC;SACpE,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,gBAAgB;QACd,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,IAAyB,CAAC;QAE9B,OAAO,IAAA,0BAAkB,EAAC;YACxB,KAAK,CAAC,KAAK;gBACT,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACtC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,IAAS;gBAClB,IAAI,CAAC;oBACH,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC1C,IAAI,IAAI;wBAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE9B,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;oBAEvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC;YACD,KAAK,CAAC,MAAM;gBACV,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACxB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;CACF;AA/LD,wBA+LC;AAEM,KAAK,SAAS,CAAC,CAAC,gBAAgB,CACrC,QAAkB,EAClB,UAA2B;IAE3B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnB,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,IACE,OAAQ,UAAkB,CAAC,SAAS,KAAK,WAAW;YACnD,UAAkB,CAAC,SAAS,CAAC,OAAO,KAAK,aAAa,EACvD,CAAC;YACD,MAAM,IAAI,sBAAc,CACtB,gKAAgK,CACjK,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,sBAAc,CAAC,mDAAmD,CAAC,CAAC;IAChF,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,IAAI,kBAAW,EAAE,CAAC;IAEtC,MAAM,IAAI,GAAG,IAAA,qCAA6B,EAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjE,IAAI,KAAK,EAAE,MAAM,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,GAAG;gBAAE,MAAM,GAAG,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC;QACvC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,GAAG;YAAE,MAAM,GAAG,CAAC;IACrB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,SAAS,CAAC,CAAC,aAAa,CAAC,QAAsC;IAClE,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;IAE5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;QACnC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GACf,KAAK,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;YACpD,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,kBAAU,EAAC,KAAK,CAAC;gBAC/C,CAAC,CAAC,KAAK,CAAC;QAEV,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,GAAG,OAAO,CAAC;QAEf,IAAI,YAAY,CAAC;QACjB,OAAO,CAAC,YAAY,GAAG,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YAClC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,IAAI,CAAC;IACb,CAAC;AACH,CAAC;AAED,MAAM,UAAU;IAKd;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,IAAY;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,6DAA6D;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBAAE,OAAO,IAAI,CAAC;YAElD,MAAM,GAAG,GAAoB;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1B,GAAG,EAAE,IAAI,CAAC,MAAM;aACjB,CAAC;YAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YAEjB,OAAO,GAAG,CAAC;QACb,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAEjD,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,CAAC;aAAM,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,SAAiB;IAC/C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvB,CAAC"}