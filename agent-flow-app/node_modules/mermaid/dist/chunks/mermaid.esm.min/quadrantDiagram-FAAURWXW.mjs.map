{"version": 3, "sources": ["../../../src/diagrams/quadrant-chart/parser/quadrant.jison", "../../../src/diagrams/quadrant-chart/quadrantBuilder.ts", "../../../src/diagrams/quadrant-chart/utils.ts", "../../../src/diagrams/quadrant-chart/quadrantDb.ts", "../../../src/diagrams/quadrant-chart/quadrantRenderer.ts", "../../../src/diagrams/quadrant-chart/quadrantDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,3],$V1=[1,4],$V2=[1,5],$V3=[1,6],$V4=[1,7],$V5=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],$V6=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],$V7=[55,56,57],$V8=[2,36],$V9=[1,37],$Va=[1,36],$Vb=[1,38],$Vc=[1,35],$Vd=[1,43],$Ve=[1,41],$Vf=[1,14],$Vg=[1,23],$Vh=[1,18],$Vi=[1,19],$Vj=[1,20],$Vk=[1,21],$Vl=[1,22],$Vm=[1,24],$Vn=[1,25],$Vo=[1,26],$Vp=[1,27],$Vq=[1,28],$Vr=[1,29],$Vs=[1,32],$Vt=[1,33],$Vu=[1,34],$Vv=[1,39],$Vw=[1,40],$Vx=[1,42],$Vy=[1,44],$Vz=[1,62],$VA=[1,61],$VB=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],$VC=[1,65],$VD=[1,66],$VE=[1,67],$VF=[1,68],$VG=[1,69],$VH=[1,70],$VI=[1,71],$VJ=[1,72],$VK=[1,73],$VL=[1,74],$VM=[1,75],$VN=[1,76],$VO=[4,5,6,7,8,9,10,11,12,13,14,15,18],$VP=[1,90],$VQ=[1,91],$VR=[1,92],$VS=[1,99],$VT=[1,93],$VU=[1,96],$VV=[1,94],$VW=[1,95],$VX=[1,97],$VY=[1,98],$VZ=[1,102],$V_=[10,55,56,57],$V$=[4,5,6,8,10,11,13,17,18,19,20,55,56,57];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"idStringToken\":3,\"ALPHA\":4,\"NUM\":5,\"NODE_STRING\":6,\"DOWN\":7,\"MINUS\":8,\"DEFAULT\":9,\"COMMA\":10,\"COLON\":11,\"AMP\":12,\"BRKT\":13,\"MULT\":14,\"UNICODE_TEXT\":15,\"styleComponent\":16,\"UNIT\":17,\"SPACE\":18,\"STYLE\":19,\"PCT\":20,\"idString\":21,\"style\":22,\"stylesOpt\":23,\"classDefStatement\":24,\"CLASSDEF\":25,\"start\":26,\"eol\":27,\"QUADRANT\":28,\"document\":29,\"line\":30,\"statement\":31,\"axisDetails\":32,\"quadrantDetails\":33,\"points\":34,\"title\":35,\"title_value\":36,\"acc_title\":37,\"acc_title_value\":38,\"acc_descr\":39,\"acc_descr_value\":40,\"acc_descr_multiline_value\":41,\"section\":42,\"text\":43,\"point_start\":44,\"point_x\":45,\"point_y\":46,\"class_name\":47,\"X-AXIS\":48,\"AXIS-TEXT-DELIMITER\":49,\"Y-AXIS\":50,\"QUADRANT_1\":51,\"QUADRANT_2\":52,\"QUADRANT_3\":53,\"QUADRANT_4\":54,\"NEWLINE\":55,\"SEMI\":56,\"EOF\":57,\"alphaNumToken\":58,\"textNoTagsToken\":59,\"STR\":60,\"MD_STR\":61,\"alphaNum\":62,\"PUNCTUATION\":63,\"PLUS\":64,\"EQUALS\":65,\"DOT\":66,\"UNDERSCORE\":67,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"ALPHA\",5:\"NUM\",6:\"NODE_STRING\",7:\"DOWN\",8:\"MINUS\",9:\"DEFAULT\",10:\"COMMA\",11:\"COLON\",12:\"AMP\",13:\"BRKT\",14:\"MULT\",15:\"UNICODE_TEXT\",17:\"UNIT\",18:\"SPACE\",19:\"STYLE\",20:\"PCT\",25:\"CLASSDEF\",28:\"QUADRANT\",35:\"title\",36:\"title_value\",37:\"acc_title\",38:\"acc_title_value\",39:\"acc_descr\",40:\"acc_descr_value\",41:\"acc_descr_multiline_value\",42:\"section\",44:\"point_start\",45:\"point_x\",46:\"point_y\",47:\"class_name\",48:\"X-AXIS\",49:\"AXIS-TEXT-DELIMITER\",50:\"Y-AXIS\",51:\"QUADRANT_1\",52:\"QUADRANT_2\",53:\"QUADRANT_3\",54:\"QUADRANT_4\",55:\"NEWLINE\",56:\"SEMI\",57:\"EOF\",60:\"STR\",61:\"MD_STR\",63:\"PUNCTUATION\",64:\"PLUS\",65:\"EQUALS\",66:\"DOT\",67:\"UNDERSCORE\"},\nproductions_: [0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 23:\nthis.$=$$[$0]\nbreak;\ncase 24:\nthis.$=$$[$0-1]+''+$$[$0]\nbreak;\ncase 26:\nthis.$ = $$[$0-1] + $$[$0];\nbreak;\ncase 27:\nthis.$ = [$$[$0].trim()]\nbreak;\ncase 28:\n$$[$0-2].push($$[$0].trim());this.$ = $$[$0-2];\nbreak;\ncase 29:\nthis.$ = $$[$0-4];yy.addClass($$[$0-2],$$[$0]);\nbreak;\ncase 37:\nthis.$=[];\nbreak;\ncase 42:\n this.$=$$[$0].trim();yy.setDiagramTitle(this.$); \nbreak;\ncase 43:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 44: case 45:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 46:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 47:\nyy.addPoint($$[$0-3], \"\", $$[$0-1], $$[$0], []);\nbreak;\ncase 48:\nyy.addPoint($$[$0-4], $$[$0-3], $$[$0-1], $$[$0], []);\nbreak;\ncase 49:\nyy.addPoint($$[$0-4], \"\", $$[$0-2], $$[$0-1], $$[$0]);\nbreak;\ncase 50:\nyy.addPoint($$[$0-5], $$[$0-4], $$[$0-2], $$[$0-1], $$[$0]);\nbreak;\ncase 51:\nyy.setXAxisLeftText($$[$0-2]); yy.setXAxisRightText($$[$0]);\nbreak;\ncase 52:\n$$[$0-1].text += \" ⟶ \"; yy.setXAxisLeftText($$[$0-1]);\nbreak;\ncase 53:\nyy.setXAxisLeftText($$[$0]);\nbreak;\ncase 54:\nyy.setYAxisBottomText($$[$0-2]); yy.setYAxisTopText($$[$0]);\nbreak;\ncase 55:\n$$[$0-1].text += \" ⟶ \"; yy.setYAxisBottomText($$[$0-1]);\nbreak;\ncase 56:\nyy.setYAxisBottomText($$[$0]);\nbreak;\ncase 57:\nyy.setQuadrant1Text($$[$0])\nbreak;\ncase 58:\nyy.setQuadrant2Text($$[$0])\nbreak;\ncase 59:\nyy.setQuadrant3Text($$[$0])\nbreak;\ncase 60:\nyy.setQuadrant4Text($$[$0])\nbreak;\ncase 64:\n this.$={text:$$[$0], type: 'text'};\nbreak;\ncase 65:\n this.$={text:$$[$0-1].text+''+$$[$0], type: $$[$0-1].type};\nbreak;\ncase 66:\n this.$={text: $$[$0], type: 'text'};\nbreak;\ncase 67:\n this.$={text: $$[$0], type: 'markdown'};\nbreak;\ncase 68:\nthis.$=$$[$0];\nbreak;\ncase 69:\nthis.$=$$[$0-1]+''+$$[$0];\nbreak;\n}\n},\ntable: [{18:$V0,26:1,27:2,28:$V1,55:$V2,56:$V3,57:$V4},{1:[3]},{18:$V0,26:8,27:2,28:$V1,55:$V2,56:$V3,57:$V4},{18:$V0,26:9,27:2,28:$V1,55:$V2,56:$V3,57:$V4},o($V5,[2,33],{29:10}),o($V6,[2,61]),o($V6,[2,62]),o($V6,[2,63]),{1:[2,30]},{1:[2,31]},o($V7,$V8,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$Vf,25:$Vg,35:$Vh,37:$Vi,39:$Vj,41:$Vk,42:$Vl,48:$Vm,50:$Vn,51:$Vo,52:$Vp,53:$Vq,54:$Vr,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V5,[2,34]),{27:45,55:$V2,56:$V3,57:$V4},o($V7,[2,37]),o($V7,$V8,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$Vf,25:$Vg,35:$Vh,37:$Vi,39:$Vj,41:$Vk,42:$Vl,48:$Vm,50:$Vn,51:$Vo,52:$Vp,53:$Vq,54:$Vr,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,39]),o($V7,[2,40]),o($V7,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},o($V7,[2,45]),o($V7,[2,46]),{18:[1,50]},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:51,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:52,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:53,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:54,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:55,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:56,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,44:[1,57],47:[1,58],58:60,59:59,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},o($VB,[2,64]),o($VB,[2,66]),o($VB,[2,67]),o($VB,[2,70]),o($VB,[2,71]),o($VB,[2,72]),o($VB,[2,73]),o($VB,[2,74]),o($VB,[2,75]),o($VB,[2,76]),o($VB,[2,77]),o($VB,[2,78]),o($VB,[2,79]),o($VB,[2,80]),o($V5,[2,35]),o($V7,[2,38]),o($V7,[2,42]),o($V7,[2,43]),o($V7,[2,44]),{3:64,4:$VC,5:$VD,6:$VE,7:$VF,8:$VG,9:$VH,10:$VI,11:$VJ,12:$VK,13:$VL,14:$VM,15:$VN,21:63},o($V7,[2,53],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,49:[1,77],63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,56],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,49:[1,78],63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,57],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,58],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,59],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,60],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),{45:[1,79]},{44:[1,80]},o($VB,[2,65]),o($VB,[2,81]),o($VB,[2,82]),o($VB,[2,83]),{3:82,4:$VC,5:$VD,6:$VE,7:$VF,8:$VG,9:$VH,10:$VI,11:$VJ,12:$VK,13:$VL,14:$VM,15:$VN,18:[1,81]},o($VO,[2,23]),o($VO,[2,1]),o($VO,[2,2]),o($VO,[2,3]),o($VO,[2,4]),o($VO,[2,5]),o($VO,[2,6]),o($VO,[2,7]),o($VO,[2,8]),o($VO,[2,9]),o($VO,[2,10]),o($VO,[2,11]),o($VO,[2,12]),o($V7,[2,52],{58:31,43:83,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,55],{58:31,43:84,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),{46:[1,85]},{45:[1,86]},{4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,16:89,17:$VV,18:$VW,19:$VX,20:$VY,22:88,23:87},o($VO,[2,24]),o($V7,[2,51],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,54],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,47],{22:88,16:89,23:100,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),{46:[1,101]},o($V7,[2,29],{10:$VZ}),o($V_,[2,27],{16:103,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),o($V$,[2,25]),o($V$,[2,13]),o($V$,[2,14]),o($V$,[2,15]),o($V$,[2,16]),o($V$,[2,17]),o($V$,[2,18]),o($V$,[2,19]),o($V$,[2,20]),o($V$,[2,21]),o($V$,[2,22]),o($V7,[2,49],{10:$VZ}),o($V7,[2,48],{22:88,16:89,23:104,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),{4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,16:89,17:$VV,18:$VW,19:$VX,20:$VY,22:105},o($V$,[2,26]),o($V7,[2,50],{10:$VZ}),o($V_,[2,28],{16:103,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY})],\ndefaultActions: {8:[2,30],9:[2,31]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 55;\nbreak;\ncase 3:/* do nothing */\nbreak;\ncase 4: this.begin(\"title\");return 35; \nbreak;\ncase 5: this.popState(); return \"title_value\"; \nbreak;\ncase 6: this.begin(\"acc_title\");return 37; \nbreak;\ncase 7: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 8: this.begin(\"acc_descr\");return 39; \nbreak;\ncase 9: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 10: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 11: this.popState(); \nbreak;\ncase 12:return \"acc_descr_multiline_value\";\nbreak;\ncase 13:return 48;\nbreak;\ncase 14:return 50;\nbreak;\ncase 15:return 49\nbreak;\ncase 16:return 51;\nbreak;\ncase 17:return 52;\nbreak;\ncase 18:return 53;\nbreak;\ncase 19:return 54;\nbreak;\ncase 20:return 25;\nbreak;\ncase 21: this.begin(\"md_string\");\nbreak;\ncase 22: return \"MD_STR\";\nbreak;\ncase 23: this.popState();\nbreak;\ncase 24:this.begin(\"string\");\nbreak;\ncase 25:this.popState();\nbreak;\ncase 26:return \"STR\";\nbreak;\ncase 27:this.begin('class_name')\nbreak;\ncase 28:this.popState(); return 47;\nbreak;\ncase 29:this.begin(\"point_start\"); return 44;\nbreak;\ncase 30:this.begin('point_x'); return 45;\nbreak;\ncase 31:this.popState();\nbreak;\ncase 32:this.popState(); this.begin('point_y');\nbreak;\ncase 33:this.popState(); return 46;\nbreak;\ncase 34:return 28;\nbreak;\ncase 35:return 4;\nbreak;\ncase 36:return 11;\nbreak;\ncase 37:return 64;\nbreak;\ncase 38:return 10;\nbreak;\ncase 39:return 65;\nbreak;\ncase 40:return 65;\nbreak;\ncase 41:return 14;\nbreak;\ncase 42:return 13;\nbreak;\ncase 43:return 67;\nbreak;\ncase 44:return 66;\nbreak;\ncase 45:return 12;\nbreak;\ncase 46:return 8;\nbreak;\ncase 47:return 5;\nbreak;\ncase 48:return 18;\nbreak;\ncase 49:return 56;\nbreak;\ncase 50:return 63;\nbreak;\ncase 51:return 57;\nbreak;\n}\n},\nrules: [/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n\\r]+)/i,/^(?:%%[^\\n]*)/i,/^(?:title\\b)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\\b)/i,/^(?:[\"][`])/i,/^(?:[^`\"]+)/i,/^(?:[`][\"])/i,/^(?:[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?::::)/i,/^(?:^\\w+)/i,/^(?:\\s*:\\s*\\[\\s*)/i,/^(?:(1)|(0(.\\d+)?))/i,/^(?:\\s*\\] *)/i,/^(?:\\s*,\\s*)/i,/^(?:(1)|(0(.\\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\\*)/i,/^(?:#)/i,/^(?:[\\_])/i,/^(?:\\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\\s)/i,/^(?:;)/i,/^(?:[!\"#$%&'*+,-.`?\\\\_/])/i,/^(?:$)/i],\nconditions: {\"class_name\":{\"rules\":[28],\"inclusive\":false},\"point_y\":{\"rules\":[33],\"inclusive\":false},\"point_x\":{\"rules\":[32],\"inclusive\":false},\"point_start\":{\"rules\":[30,31],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[11,12],\"inclusive\":false},\"acc_descr\":{\"rules\":[9],\"inclusive\":false},\"acc_title\":{\"rules\":[7],\"inclusive\":false},\"title\":{\"rules\":[5],\"inclusive\":false},\"md_string\":{\"rules\":[22,23],\"inclusive\":false},\"string\":{\"rules\":[25,26],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { scaleLinear } from 'd3';\nimport type { BaseDiagramConfig, QuadrantChartConfig } from '../../config.type.js';\nimport defaultConfig from '../../defaultConfig.js';\nimport { log } from '../../logger.js';\nimport { getThemeVariables } from '../../themes/theme-default.js';\nimport type { Point } from '../../types.js';\n\nconst defaultThemeVariables = getThemeVariables();\n\nexport type TextVerticalPos = 'left' | 'center' | 'right';\nexport type TextHorizontalPos = 'top' | 'middle' | 'bottom';\n\nexport interface StylesObject {\n  className?: string;\n  radius?: number;\n  color?: string;\n  strokeColor?: string;\n  strokeWidth?: string;\n}\n\nexport interface QuadrantPointInputType extends Point, StylesObject {\n  text: string;\n}\n\nexport interface QuadrantTextType extends Point {\n  text: string;\n  fill: string;\n  verticalPos: TextVerticalPos;\n  horizontalPos: TextHorizontalPos;\n  fontSize: number;\n  rotation: number;\n}\n\nexport interface QuadrantPointType\n  extends Point,\n    Pick<StylesObject, 'strokeColor' | 'strokeWidth'> {\n  fill: string;\n  radius: number;\n  text: QuadrantTextType;\n}\n\nexport interface QuadrantQuadrantsType extends Point {\n  text: QuadrantTextType;\n  width: number;\n  height: number;\n  fill: string;\n}\n\nexport interface QuadrantLineType {\n  strokeWidth: number;\n  strokeFill: string;\n  x1: number;\n  y1: number;\n  x2: number;\n  y2: number;\n}\n\nexport interface QuadrantBuildType {\n  points: QuadrantPointType[];\n  quadrants: QuadrantQuadrantsType[];\n  axisLabels: QuadrantTextType[];\n  title?: QuadrantTextType;\n  borderLines?: QuadrantLineType[];\n}\n\nexport interface QuadrantBuilderData {\n  titleText: string;\n  quadrant1Text: string;\n  quadrant2Text: string;\n  quadrant3Text: string;\n  quadrant4Text: string;\n  xAxisLeftText: string;\n  xAxisRightText: string;\n  yAxisBottomText: string;\n  yAxisTopText: string;\n  points: QuadrantPointInputType[];\n}\n\nexport interface QuadrantBuilderConfig\n  extends Required<Omit<QuadrantChartConfig, keyof BaseDiagramConfig>> {\n  showXAxis: boolean;\n  showYAxis: boolean;\n  showTitle: boolean;\n}\n\nexport interface QuadrantBuilderThemeConfig {\n  quadrantTitleFill: string;\n  quadrant1Fill: string;\n  quadrant2Fill: string;\n  quadrant3Fill: string;\n  quadrant4Fill: string;\n  quadrant1TextFill: string;\n  quadrant2TextFill: string;\n  quadrant3TextFill: string;\n  quadrant4TextFill: string;\n  quadrantPointFill: string;\n  quadrantPointTextFill: string;\n  quadrantXAxisTextFill: string;\n  quadrantYAxisTextFill: string;\n  quadrantInternalBorderStrokeFill: string;\n  quadrantExternalBorderStrokeFill: string;\n}\n\ninterface CalculateSpaceData {\n  xAxisSpace: {\n    top: number;\n    bottom: number;\n  };\n  yAxisSpace: {\n    left: number;\n    right: number;\n  };\n  titleSpace: {\n    top: number;\n  };\n  quadrantSpace: {\n    quadrantLeft: number;\n    quadrantTop: number;\n    quadrantWidth: number;\n    quadrantHalfWidth: number;\n    quadrantHeight: number;\n    quadrantHalfHeight: number;\n  };\n}\n\nexport class QuadrantBuilder {\n  private config: QuadrantBuilderConfig;\n  private themeConfig: QuadrantBuilderThemeConfig;\n  private data: QuadrantBuilderData;\n  private classes = new Map<string, StylesObject>();\n\n  constructor() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n\n  getDefaultData(): QuadrantBuilderData {\n    return {\n      titleText: '',\n      quadrant1Text: '',\n      quadrant2Text: '',\n      quadrant3Text: '',\n      quadrant4Text: '',\n      xAxisLeftText: '',\n      xAxisRightText: '',\n      yAxisBottomText: '',\n      yAxisTopText: '',\n      points: [],\n    };\n  }\n\n  getDefaultConfig(): QuadrantBuilderConfig {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: defaultConfig.quadrantChart?.chartWidth || 500,\n      chartWidth: defaultConfig.quadrantChart?.chartHeight || 500,\n      titlePadding: defaultConfig.quadrantChart?.titlePadding || 10,\n      titleFontSize: defaultConfig.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: defaultConfig.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: defaultConfig.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: defaultConfig.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: defaultConfig.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: defaultConfig.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: defaultConfig.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: defaultConfig.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: defaultConfig.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: defaultConfig.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: defaultConfig.quadrantChart?.pointRadius || 5,\n      xAxisPosition: defaultConfig.quadrantChart?.xAxisPosition || 'top',\n      yAxisPosition: defaultConfig.quadrantChart?.yAxisPosition || 'left',\n      quadrantInternalBorderStrokeWidth:\n        defaultConfig.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth:\n        defaultConfig.quadrantChart?.quadrantExternalBorderStrokeWidth || 2,\n    };\n  }\n\n  getDefaultThemeConfig(): QuadrantBuilderThemeConfig {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill,\n    };\n  }\n\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = new Map();\n    log.info('clear called');\n  }\n\n  setData(data: Partial<QuadrantBuilderData>) {\n    this.data = { ...this.data, ...data };\n  }\n\n  addPoints(points: QuadrantPointInputType[]) {\n    this.data.points = [...points, ...this.data.points];\n  }\n\n  addClass(className: string, styles: StylesObject) {\n    this.classes.set(className, styles);\n  }\n\n  setConfig(config: Partial<QuadrantBuilderConfig>) {\n    log.trace('setConfig called with: ', config);\n    this.config = { ...this.config, ...config };\n  }\n\n  setThemeConfig(themeConfig: Partial<QuadrantBuilderThemeConfig>) {\n    log.trace('setThemeConfig called with: ', themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n\n  calculateSpace(\n    xAxisPosition: typeof this.config.xAxisPosition,\n    showXAxis: boolean,\n    showYAxis: boolean,\n    showTitle: boolean\n  ): CalculateSpaceData {\n    const xAxisSpaceCalculation =\n      this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === 'top' && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === 'bottom' && showXAxis ? xAxisSpaceCalculation : 0,\n    };\n\n    const yAxisSpaceCalculation =\n      this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === 'left' && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === 'right' && showYAxis ? yAxisSpaceCalculation : 0,\n    };\n\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0,\n    };\n\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth =\n      this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight =\n      this.config.chartHeight -\n      this.config.quadrantPadding * 2 -\n      xAxisSpace.top -\n      xAxisSpace.bottom -\n      titleSpace.top;\n\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight,\n    };\n\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace,\n    };\n  }\n\n  getAxisLabels(\n    xAxisPosition: typeof this.config.xAxisPosition,\n    showXAxis: boolean,\n    showYAxis: boolean,\n    spaceData: CalculateSpaceData\n  ): QuadrantTextType[] {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth,\n    } = quadrantSpace;\n\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n\n    const axisLabels: QuadrantTextType[] = [];\n\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y:\n          xAxisPosition === 'top'\n            ? this.config.xAxisLabelPadding + titleSpace.top\n            : this.config.xAxisLabelPadding +\n              quadrantTop +\n              quadrantHeight +\n              this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: 0,\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y:\n          xAxisPosition === 'top'\n            ? this.config.xAxisLabelPadding + titleSpace.top\n            : this.config.xAxisLabelPadding +\n              quadrantTop +\n              quadrantHeight +\n              this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: 0,\n      });\n    }\n\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x:\n          this.config.yAxisPosition === 'left'\n            ? this.config.yAxisLabelPadding\n            : this.config.yAxisLabelPadding +\n              quadrantLeft +\n              quadrantWidth +\n              this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: -90,\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x:\n          this.config.yAxisPosition === 'left'\n            ? this.config.yAxisLabelPadding\n            : this.config.yAxisLabelPadding +\n              quadrantLeft +\n              quadrantWidth +\n              this.config.quadrantPadding,\n        y:\n          quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: -90,\n      });\n    }\n    return axisLabels;\n  }\n\n  getQuadrants(spaceData: CalculateSpaceData): QuadrantQuadrantsType[] {\n    const { quadrantSpace } = spaceData;\n\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n\n    const quadrants: QuadrantQuadrantsType[] = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill,\n      },\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      // place the text in the center of the box\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = 'middle';\n        // place the text top of the quadrant square\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = 'top';\n      }\n    }\n\n    return quadrants;\n  }\n\n  getQuadrantPoints(spaceData: CalculateSpaceData): QuadrantPointType[] {\n    const { quadrantSpace } = spaceData;\n\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n\n    const xAxis = scaleLinear()\n      .domain([0, 1])\n      .range([quadrantLeft, quadrantWidth + quadrantLeft]);\n\n    const yAxis = scaleLinear()\n      .domain([0, 1])\n      .range([quadrantHeight + quadrantTop, quadrantTop]);\n\n    const points: QuadrantPointType[] = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className!);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props: QuadrantPointType = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: 'center',\n          horizontalPos: 'top',\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0,\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? '0px',\n      };\n      return props;\n    });\n    return points;\n  }\n\n  getBorders(spaceData: CalculateSpaceData): QuadrantLineType[] {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth,\n    } = quadrantSpace;\n\n    const borderLines: QuadrantLineType[] = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop,\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight,\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight,\n      },\n    ];\n    return borderLines;\n  }\n\n  getTitle(showTitle: boolean): QuadrantTextType | undefined {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: 'top',\n        verticalPos: 'center',\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2,\n      };\n    }\n    return;\n  }\n\n  build(): QuadrantBuildType {\n    const showXAxis =\n      this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis =\n      this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n\n    const xAxisPosition = this.data.points.length > 0 ? 'bottom' : this.config.xAxisPosition;\n\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle),\n    };\n  }\n}\n", "class InvalidStyleError extends Error {\n  constructor(style: string, value: string, type: string) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = 'InvalidStyleError';\n  }\n}\n\nfunction validateHexCode(value: string): boolean {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n\nfunction validateNumber(value: string): boolean {\n  return !/^\\d+$/.test(value);\n}\n\nfunction validateSizeInPixels(value: string): boolean {\n  return !/^\\d+px$/.test(value);\n}\n\nexport { validateHexCode, validateNumber, validateSizeInPixels, InvalidStyleError };\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\nimport type { StylesObject } from './quadrantBuilder.js';\nimport { QuadrantBuilder } from './quadrantBuilder.js';\nimport {\n  validateHexCode,\n  validateSizeInPixels,\n  validateNumber,\n  InvalidStyleError,\n} from './utils.js';\n\nconst config = getConfig();\n\nfunction textSanitizer(text: string) {\n  return sanitizeText(text.trim(), config);\n}\n\ninterface LexTextObj {\n  text: string;\n  type: 'text' | 'markdown';\n}\n\nconst quadrantBuilder = new QuadrantBuilder();\n\nfunction setQuadrant1Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant2Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant3Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant4Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n\nfunction setXAxisLeftText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n\nfunction setXAxisRightText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n\nfunction setYAxisTopText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n\nfunction setYAxisBottomText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n\nfunction parseStyles(styles: string[]): StylesObject {\n  const stylesObject: StylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === 'radius') {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, 'number');\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === 'color') {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, 'hex code');\n      }\n      stylesObject.color = value;\n    } else if (key === 'stroke-color') {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, 'hex code');\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === 'stroke-width') {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, 'number of pixels (eg. 10px)');\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n\nfunction addPoint(textObj: LexTextObj, className: string, x: number, y: number, styles: string[]) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject,\n    },\n  ]);\n}\n\nfunction addClass(className: string, styles: string[]) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n\nfunction setWidth(width: number) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n\nfunction setHeight(height: number) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n\nfunction getQuadrantData() {\n  const config = getConfig();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill,\n  });\n  quadrantBuilder.setData({ titleText: getDiagramTitle() });\n  return quadrantBuilder.build();\n}\n\nconst clear = function () {\n  quadrantBuilder.clear();\n  commonClear();\n};\n\nexport default {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n};\n", "// @ts-nocheck - don't check until handle it\nimport { select } from 'd3';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\nimport type {\n  QuadrantBuildType,\n  QuadrantLineType,\n  QuadrantPointType,\n  QuadrantQuadrantsType,\n  QuadrantTextType,\n  TextHorizontalPos,\n  TextVerticalPos,\n} from './quadrantBuilder.js';\n\nexport const draw = (txt: string, id: string, _version: string, diagObj: Diagram) => {\n  function getDominantBaseLine(horizontalPos: TextHorizontalPos) {\n    return horizontalPos === 'top' ? 'hanging' : 'middle';\n  }\n\n  function getTextAnchor(verticalPos: TextVerticalPos) {\n    return verticalPos === 'left' ? 'start' : 'middle';\n  }\n\n  function getTransformation(data: { x: number; y: number; rotation: number }) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n\n  const conf = getConfig();\n\n  log.debug('Rendering quadrant chart\\n' + txt);\n\n  const securityLevel = conf.securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  const group = svg.append('g').attr('class', 'main');\n\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n\n  configureSvgSize(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n\n  svg.attr('viewBox', '0 0 ' + width + ' ' + height);\n\n  // @ts-ignore: TODO Fix ts errors\n  diagObj.db.setHeight(height);\n  // @ts-ignore: TODO Fix ts errors\n  diagObj.db.setWidth(width);\n\n  // @ts-ignore: TODO Fix ts errors\n  const quadrantData: QuadrantBuildType = diagObj.db.getQuadrantData();\n\n  const quadrantsGroup = group.append('g').attr('class', 'quadrants');\n  const borderGroup = group.append('g').attr('class', 'border');\n  const dataPointGroup = group.append('g').attr('class', 'data-points');\n  const labelGroup = group.append('g').attr('class', 'labels');\n  const titleGroup = group.append('g').attr('class', 'title');\n\n  if (quadrantData.title) {\n    titleGroup\n      .append('text')\n      .attr('x', 0)\n      .attr('y', 0)\n      .attr('fill', quadrantData.title.fill)\n      .attr('font-size', quadrantData.title.fontSize)\n      .attr('dominant-baseline', getDominantBaseLine(quadrantData.title.horizontalPos))\n      .attr('text-anchor', getTextAnchor(quadrantData.title.verticalPos))\n      .attr('transform', getTransformation(quadrantData.title))\n      .text(quadrantData.title.text);\n  }\n\n  if (quadrantData.borderLines) {\n    borderGroup\n      .selectAll('line')\n      .data(quadrantData.borderLines)\n      .enter()\n      .append('line')\n      .attr('x1', (data: QuadrantLineType) => data.x1)\n      .attr('y1', (data: QuadrantLineType) => data.y1)\n      .attr('x2', (data: QuadrantLineType) => data.x2)\n      .attr('y2', (data: QuadrantLineType) => data.y2)\n      .style('stroke', (data: QuadrantLineType) => data.strokeFill)\n      .style('stroke-width', (data: QuadrantLineType) => data.strokeWidth);\n  }\n\n  const quadrants = quadrantsGroup\n    .selectAll('g.quadrant')\n    .data(quadrantData.quadrants)\n    .enter()\n    .append('g')\n    .attr('class', 'quadrant');\n\n  quadrants\n    .append('rect')\n    .attr('x', (data: QuadrantQuadrantsType) => data.x)\n    .attr('y', (data: QuadrantQuadrantsType) => data.y)\n    .attr('width', (data: QuadrantQuadrantsType) => data.width)\n    .attr('height', (data: QuadrantQuadrantsType) => data.height)\n    .attr('fill', (data: QuadrantQuadrantsType) => data.fill);\n\n  quadrants\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .attr('fill', (data: QuadrantQuadrantsType) => data.text.fill)\n    .attr('font-size', (data: QuadrantQuadrantsType) => data.text.fontSize)\n    .attr('dominant-baseline', (data: QuadrantQuadrantsType) =>\n      getDominantBaseLine(data.text.horizontalPos)\n    )\n    .attr('text-anchor', (data: QuadrantQuadrantsType) => getTextAnchor(data.text.verticalPos))\n    .attr('transform', (data: QuadrantQuadrantsType) => getTransformation(data.text))\n    .text((data: QuadrantQuadrantsType) => data.text.text);\n\n  const labels = labelGroup\n    .selectAll('g.label')\n    .data(quadrantData.axisLabels)\n    .enter()\n    .append('g')\n    .attr('class', 'label');\n\n  labels\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .text((data: QuadrantTextType) => data.text)\n    .attr('fill', (data: QuadrantTextType) => data.fill)\n    .attr('font-size', (data: QuadrantTextType) => data.fontSize)\n    .attr('dominant-baseline', (data: QuadrantTextType) => getDominantBaseLine(data.horizontalPos))\n    .attr('text-anchor', (data: QuadrantTextType) => getTextAnchor(data.verticalPos))\n    .attr('transform', (data: QuadrantTextType) => getTransformation(data));\n\n  const dataPoints = dataPointGroup\n    .selectAll('g.data-point')\n    .data(quadrantData.points)\n    .enter()\n    .append('g')\n    .attr('class', 'data-point');\n\n  dataPoints\n    .append('circle')\n    .attr('cx', (data: QuadrantPointType) => data.x)\n    .attr('cy', (data: QuadrantPointType) => data.y)\n    .attr('r', (data: QuadrantPointType) => data.radius)\n    .attr('fill', (data: QuadrantPointType) => data.fill)\n    .attr('stroke', (data: QuadrantPointType) => data.strokeColor)\n    .attr('stroke-width', (data: QuadrantPointType) => data.strokeWidth);\n\n  dataPoints\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .text((data: QuadrantPointType) => data.text.text)\n    .attr('fill', (data: QuadrantPointType) => data.text.fill)\n    .attr('font-size', (data: QuadrantPointType) => data.text.fontSize)\n    .attr('dominant-baseline', (data: QuadrantPointType) =>\n      getDominantBaseLine(data.text.horizontalPos)\n    )\n    .attr('text-anchor', (data: QuadrantPointType) => getTextAnchor(data.text.verticalPos))\n    .attr('transform', (data: QuadrantPointType) => getTransformation(data.text));\n};\n\nexport default {\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/quadrant.jison';\nimport db from './quadrantDb.js';\nimport renderer from './quadrantRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles: () => '',\n};\n"], "mappings": "oMAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,<PERSON>G,<PERSON>G,<PERSON>G,<PERSON>G,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,GAAG,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAC9hCrE,GAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,cAAgB,EAAE,MAAQ,EAAE,IAAM,EAAE,YAAc,EAAE,KAAO,EAAE,MAAQ,EAAE,QAAU,EAAE,MAAQ,GAAG,MAAQ,GAAG,IAAM,GAAG,KAAO,GAAG,KAAO,GAAG,aAAe,GAAG,eAAiB,GAAG,KAAO,GAAG,MAAQ,GAAG,MAAQ,GAAG,IAAM,GAAG,SAAW,GAAG,MAAQ,GAAG,UAAY,GAAG,kBAAoB,GAAG,SAAW,GAAG,MAAQ,GAAG,IAAM,GAAG,SAAW,GAAG,SAAW,GAAG,KAAO,GAAG,UAAY,GAAG,YAAc,GAAG,gBAAkB,GAAG,OAAS,GAAG,MAAQ,GAAG,YAAc,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,QAAU,GAAG,KAAO,GAAG,YAAc,GAAG,QAAU,GAAG,QAAU,GAAG,WAAa,GAAG,SAAS,GAAG,sBAAsB,GAAG,SAAS,GAAG,WAAa,GAAG,WAAa,GAAG,WAAa,GAAG,WAAa,GAAG,QAAU,GAAG,KAAO,GAAG,IAAM,GAAG,cAAgB,GAAG,gBAAkB,GAAG,IAAM,GAAG,OAAS,GAAG,SAAW,GAAG,YAAc,GAAG,KAAO,GAAG,OAAS,GAAG,IAAM,GAAG,WAAa,GAAG,QAAU,EAAE,KAAO,CAAC,EACv7B,WAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,eAAe,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,GAAG,WAAW,GAAG,QAAQ,GAAG,cAAc,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,GAAG,SAAS,GAAG,sBAAsB,GAAG,SAAS,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,GAAG,aAAa,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,cAAc,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,YAAY,EAClpB,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACzkB,cAAeA,EAAA,SAAmBoE,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,GAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,IACL,KAAK,EAAEC,EAAGE,CAAE,EACZ,MACA,IAAK,IACL,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAAE,GAAGF,EAAGE,CAAE,EACxB,MACA,IAAK,IACL,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAAIF,EAAGE,CAAE,EACzB,MACA,IAAK,IACL,KAAK,EAAI,CAACF,EAAGE,CAAE,EAAE,KAAK,CAAC,EACvB,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,KAAKF,EAAGE,CAAE,EAAE,KAAK,CAAC,EAAE,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAC7C,MACA,IAAK,IACL,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAAEJ,EAAG,SAASE,EAAGE,EAAG,CAAC,EAAEF,EAAGE,CAAE,CAAC,EAC7C,MACA,IAAK,IACL,KAAK,EAAE,CAAC,EACR,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,gBAAgB,KAAK,CAAC,EAC/C,MACA,IAAK,IACJ,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,KAAK,CAAC,EAC3C,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IACLA,EAAG,WAAWE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACtD,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,EAAG,CAAC,EAAG,GAAIF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,EAAG,CAAC,CAAC,EAC9C,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,EAAG,CAAC,CAAC,EACpD,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,EAAG,CAAC,EAAG,GAAIF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EACpD,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC1D,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,EAAG,CAAC,CAAC,EAAGJ,EAAG,kBAAkBE,EAAGE,CAAE,CAAC,EAC1D,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,MAAQ,WAAOJ,EAAG,iBAAiBE,EAAGE,EAAG,CAAC,CAAC,EACpD,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACA,IAAK,IACLJ,EAAG,mBAAmBE,EAAGE,EAAG,CAAC,CAAC,EAAGJ,EAAG,gBAAgBE,EAAGE,CAAE,CAAC,EAC1D,MACA,IAAK,IACLF,EAAGE,EAAG,CAAC,EAAE,MAAQ,WAAOJ,EAAG,mBAAmBE,EAAGE,EAAG,CAAC,CAAC,EACtD,MACA,IAAK,IACLJ,EAAG,mBAAmBE,EAAGE,CAAE,CAAC,EAC5B,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACA,IAAK,IACLJ,EAAG,iBAAiBE,EAAGE,CAAE,CAAC,EAC1B,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAKF,EAAGE,CAAE,EAAG,KAAM,MAAM,EAClC,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAKF,EAAGE,EAAG,CAAC,EAAE,KAAK,GAAGF,EAAGE,CAAE,EAAG,KAAMF,EAAGE,EAAG,CAAC,EAAE,IAAI,EAC1D,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAMF,EAAGE,CAAE,EAAG,KAAM,MAAM,EACnC,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAMF,EAAGE,CAAE,EAAG,KAAM,UAAU,EACvC,MACA,IAAK,IACL,KAAK,EAAEF,EAAGE,CAAE,EACZ,MACA,IAAK,IACL,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAAE,GAAGF,EAAGE,CAAE,EACxB,KACA,CACA,EAnGe,aAoGf,MAAO,CAAC,CAAC,GAAGvE,EAAI,GAAG,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGJ,EAAI,GAAG,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAGJ,EAAI,GAAG,EAAE,GAAG,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAET,EAAEU,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAEV,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEX,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEX,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAEX,EAAEY,EAAIC,EAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAEC,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEU,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAET,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAIC,EAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAEC,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAEE,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAEzB,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEvC,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAEU,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEV,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEZ,EAAEY,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE+B,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,EAAE,EAAEtD,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAG,CAAC,EAAE,EAAE,EAAE,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEvC,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE1C,EAAE0C,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEtD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGc,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAEiB,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,GAAG,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,GAAG,GAAG,EAAE,EAAEjE,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEE,EAAI,EAAEC,EAAI,EAAEyB,EAAI,GAAGxB,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGsB,EAAI,GAAGN,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvC,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE4C,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEjE,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGsD,EAAG,CAAC,EAAElE,EAAEmE,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,EAAEX,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,EAAEjE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGsD,EAAG,CAAC,EAAElE,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE4C,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,EAAE,CAAC,EAAET,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,GAAG,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAG,GAAG,EAAEjE,EAAEoE,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEpE,EAAEY,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGsD,EAAG,CAAC,EAAElE,EAAEmE,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,EAAEX,EAAI,EAAEC,EAAI,EAAEC,EAAI,EAAEC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,CAAC,EAC5wI,eAAgB,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAClC,WAAYhE,EAAA,SAAqB4E,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAO9E,EAAA,SAAe+E,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,GAAQ,KAAK,MAAOjB,EAAS,GAAIE,GAAW,EAAGD,GAAS,EAAGiB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAAS1F,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/C0F,EAAY,GAAG1F,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrCyF,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJS/F,EAAA8F,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXajG,EAAAgG,GAAA,OAajB,QADIE,EAAQC,GAAgBC,EAAOC,EAAQC,GAAGC,GAAGC,GAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,GAAMe,CAAK,GAAKf,GAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,GAAMe,CAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0BvC,GAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BvC,GAAW,GAAK,iBAAmB4B,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjB9B,GAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,GAAWoB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,GAAa,GACbA,MAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,GAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,GAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,GAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,GAAO,CAChCpC,EACAC,GACAC,GACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,GAAM,CAAC,EACnBpB,EAAO,KAAKoB,GAAM,EAAE,EACpBG,GAAWtB,GAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,GAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAW1F,EAAA,SAAoB4E,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAAS5E,EAAA,SAAU+E,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAM/E,EAAA,UAAY,CACV,IAAI8G,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAM9G,EAAA,SAAU8G,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAK1G,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAU+F,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAU/F,EAAA,UAAY,CACd,IAAIiH,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAcjH,EAAA,UAAY,CAClB,IAAIkH,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAalH,EAAA,UAAY,CACjB,IAAImH,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAWpH,EAAA,SAASqH,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAAShG,KAAKsH,EACV,KAAKtH,CAAC,EAAIsH,EAAOtH,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIiG,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIjG,EAAA,UAAgB,CACZ,IAAIuG,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAMvG,EAAA,SAAgB4H,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAAS5H,EAAA,UAAqB,CACtB,IAAI+F,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAc/F,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmB+F,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAU/F,EAAA,SAAoB4H,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAe5H,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBuE,EAAGsD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,IAEd,IAAK,GACL,MACA,IAAK,GAAG,YAAK,MAAM,OAAO,EAAS,GACnC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,cAChC,MACA,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,KAAK,MAAM,WAAW,EAC/B,MACA,IAAK,IAAI,MAAO,SAEhB,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,KAAK,MAAM,QAAQ,EAC3B,MACA,IAAK,IAAG,KAAK,SAAS,EACtB,MACA,IAAK,IAAG,MAAO,MAEf,IAAK,IAAG,KAAK,MAAM,YAAY,EAC/B,MACA,IAAK,IAAG,YAAK,SAAS,EAAU,GAChC,MACA,IAAK,IAAG,YAAK,MAAM,aAAa,EAAU,GAC1C,MACA,IAAK,IAAG,YAAK,MAAM,SAAS,EAAU,GACtC,MACA,IAAK,IAAG,KAAK,SAAS,EACtB,MACA,IAAK,IAAG,KAAK,SAAS,EAAG,KAAK,MAAM,SAAS,EAC7C,MACA,IAAK,IAAG,YAAK,SAAS,EAAU,GAChC,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,CACA,EA5Ge,aA6Gf,MAAO,CAAC,uBAAuB,sBAAsB,gBAAgB,iBAAiB,gBAAgB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,mBAAmB,mBAAmB,iBAAiB,uBAAuB,uBAAuB,uBAAuB,uBAAuB,mBAAmB,eAAe,eAAe,eAAe,YAAY,YAAY,cAAc,YAAY,aAAa,qBAAqB,uBAAuB,gBAAgB,gBAAgB,uBAAuB,0BAA0B,kBAAkB,UAAU,WAAW,UAAU,UAAU,UAAU,WAAW,UAAU,aAAa,WAAW,UAAU,UAAU,eAAe,WAAW,UAAU,6BAA6B,SAAS,EACl3B,WAAY,CAAC,WAAa,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,YAAc,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CAC3mB,EACA,OAAOpC,CACP,EAAG,EACH5F,GAAO,MAAQ4F,GACf,SAASuC,IAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAjI,EAAAiI,GAAA,UAGTA,GAAO,UAAYnI,GAAOA,GAAO,OAASmI,GACnC,IAAIA,EACX,EAAG,EACFnI,GAAO,OAASA,GAEhB,IAAOoI,GAAQC,GCrwBhB,IAAMC,EAAwBC,GAAkB,EAsHnCC,GAAN,KAAsB,CAM3B,aAAc,CAFd,KAAQ,QAAU,IAAI,IAGpB,KAAK,OAAS,KAAK,iBAAiB,EACpC,KAAK,YAAc,KAAK,sBAAsB,EAC9C,KAAK,KAAO,KAAK,eAAe,CAClC,CAvIF,MA6H6B,CAAAC,EAAA,wBAY3B,gBAAsC,CACpC,MAAO,CACL,UAAW,GACX,cAAe,GACf,cAAe,GACf,cAAe,GACf,cAAe,GACf,cAAe,GACf,eAAgB,GAChB,gBAAiB,GACjB,aAAc,GACd,OAAQ,CAAC,CACX,CACF,CAEA,kBAA0C,CACxC,MAAO,CACL,UAAW,GACX,UAAW,GACX,UAAW,GACX,YAAaC,EAAc,eAAe,YAAc,IACxD,WAAYA,EAAc,eAAe,aAAe,IACxD,aAAcA,EAAc,eAAe,cAAgB,GAC3D,cAAeA,EAAc,eAAe,eAAiB,GAC7D,gBAAiBA,EAAc,eAAe,iBAAmB,EACjE,kBAAmBA,EAAc,eAAe,mBAAqB,EACrE,kBAAmBA,EAAc,eAAe,mBAAqB,EACrE,mBAAoBA,EAAc,eAAe,oBAAsB,GACvE,mBAAoBA,EAAc,eAAe,oBAAsB,GACvE,sBAAuBA,EAAc,eAAe,uBAAyB,GAC7E,uBAAwBA,EAAc,eAAe,wBAA0B,EAC/E,iBAAkBA,EAAc,eAAe,kBAAoB,EACnE,mBAAoBA,EAAc,eAAe,oBAAsB,GACvE,YAAaA,EAAc,eAAe,aAAe,EACzD,cAAeA,EAAc,eAAe,eAAiB,MAC7D,cAAeA,EAAc,eAAe,eAAiB,OAC7D,kCACEA,EAAc,eAAe,mCAAqC,EACpE,kCACEA,EAAc,eAAe,mCAAqC,CACtE,CACF,CAEA,uBAAoD,CAClD,MAAO,CACL,cAAeJ,EAAsB,cACrC,cAAeA,EAAsB,cACrC,cAAeA,EAAsB,cACrC,cAAeA,EAAsB,cACrC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,kBAAmBA,EAAsB,kBACzC,sBAAuBA,EAAsB,sBAC7C,sBAAuBA,EAAsB,sBAC7C,sBAAuBA,EAAsB,sBAC7C,kBAAmBA,EAAsB,kBACzC,iCAAkCA,EAAsB,iCACxD,iCAAkCA,EAAsB,gCAC1D,CACF,CAEA,OAAQ,CACN,KAAK,OAAS,KAAK,iBAAiB,EACpC,KAAK,YAAc,KAAK,sBAAsB,EAC9C,KAAK,KAAO,KAAK,eAAe,EAChC,KAAK,QAAU,IAAI,IACnBK,GAAI,KAAK,cAAc,CACzB,CAEA,QAAQC,EAAoC,CAC1C,KAAK,KAAO,CAAE,GAAG,KAAK,KAAM,GAAGA,CAAK,CACtC,CAEA,UAAUC,EAAkC,CAC1C,KAAK,KAAK,OAAS,CAAC,GAAGA,EAAQ,GAAG,KAAK,KAAK,MAAM,CACpD,CAEA,SAASC,EAAmBC,EAAsB,CAChD,KAAK,QAAQ,IAAID,EAAWC,CAAM,CACpC,CAEA,UAAUC,EAAwC,CAChDL,GAAI,MAAM,0BAA2BK,CAAM,EAC3C,KAAK,OAAS,CAAE,GAAG,KAAK,OAAQ,GAAGA,CAAO,CAC5C,CAEA,eAAeC,EAAkD,CAC/DN,GAAI,MAAM,+BAAgCM,CAAW,EACrD,KAAK,YAAc,CAAE,GAAG,KAAK,YAAa,GAAGA,CAAY,CAC3D,CAEA,eACEC,EACAC,EACAC,EACAC,EACoB,CACpB,IAAMC,EACJ,KAAK,OAAO,kBAAoB,EAAI,KAAK,OAAO,mBAC5CC,EAAa,CACjB,IAAKL,IAAkB,OAASC,EAAYG,EAAwB,EACpE,OAAQJ,IAAkB,UAAYC,EAAYG,EAAwB,CAC5E,EAEME,EACJ,KAAK,OAAO,kBAAoB,EAAI,KAAK,OAAO,mBAC5CC,EAAa,CACjB,KAAM,KAAK,OAAO,gBAAkB,QAAUL,EAAYI,EAAwB,EAClF,MAAO,KAAK,OAAO,gBAAkB,SAAWJ,EAAYI,EAAwB,CACtF,EAEME,EAAwB,KAAK,OAAO,cAAgB,KAAK,OAAO,aAAe,EAC/EC,EAAa,CACjB,IAAKN,EAAYK,EAAwB,CAC3C,EAEME,EAAe,KAAK,OAAO,gBAAkBH,EAAW,KACxDI,EAAc,KAAK,OAAO,gBAAkBN,EAAW,IAAMI,EAAW,IACxEG,EACJ,KAAK,OAAO,WAAa,KAAK,OAAO,gBAAkB,EAAIL,EAAW,KAAOA,EAAW,MACpFM,EACJ,KAAK,OAAO,YACZ,KAAK,OAAO,gBAAkB,EAC9BR,EAAW,IACXA,EAAW,OACXI,EAAW,IAEPK,EAAoBF,EAAgB,EACpCG,EAAqBF,EAAiB,EAU5C,MAAO,CACL,WAAAR,EACA,WAAAE,EACA,WAAAE,EACA,cAboB,CACpB,aAAAC,EACA,YAAAC,EACA,cAAAC,EACA,kBAAAE,EACA,eAAAD,EACA,mBAAAE,CACF,CAOA,CACF,CAEA,cACEf,EACAC,EACAC,EACAc,EACoB,CACpB,GAAM,CAAE,cAAAC,EAAe,WAAAR,CAAW,EAAIO,EAChC,CACJ,mBAAAD,EACA,eAAAF,EACA,aAAAH,EACA,kBAAAI,EACA,YAAAH,EACA,cAAAC,CACF,EAAIK,EAEEC,EAA0B,EAAQ,KAAK,KAAK,eAC5CC,EAA0B,EAAQ,KAAK,KAAK,aAE5CC,EAAiC,CAAC,EAExC,OAAI,KAAK,KAAK,eAAiBnB,GAC7BmB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,sBACvB,EAAGV,GAAgBQ,EAA0BJ,EAAoB,EAAI,GACrE,EACEd,IAAkB,MACd,KAAK,OAAO,kBAAoBS,EAAW,IAC3C,KAAK,OAAO,kBACZE,EACAE,EACA,KAAK,OAAO,gBAClB,SAAU,KAAK,OAAO,mBACtB,YAAaK,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,CACZ,CAAC,EAEC,KAAK,KAAK,gBAAkBjB,GAC9BmB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,eAChB,KAAM,KAAK,YAAY,sBACvB,EAAGV,EAAeI,GAAqBI,EAA0BJ,EAAoB,EAAI,GACzF,EACEd,IAAkB,MACd,KAAK,OAAO,kBAAoBS,EAAW,IAC3C,KAAK,OAAO,kBACZE,EACAE,EACA,KAAK,OAAO,gBAClB,SAAU,KAAK,OAAO,mBACtB,YAAaK,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,CACZ,CAAC,EAGC,KAAK,KAAK,iBAAmBhB,GAC/BkB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,gBAChB,KAAM,KAAK,YAAY,sBACvB,EACE,KAAK,OAAO,gBAAkB,OAC1B,KAAK,OAAO,kBACZ,KAAK,OAAO,kBACZV,EACAE,EACA,KAAK,OAAO,gBAClB,EAAGD,EAAcE,GAAkBM,EAA0BJ,EAAqB,EAAI,GACtF,SAAU,KAAK,OAAO,mBACtB,YAAaI,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,GACZ,CAAC,EAEC,KAAK,KAAK,cAAgBjB,GAC5BkB,EAAW,KAAK,CACd,KAAM,KAAK,KAAK,aAChB,KAAM,KAAK,YAAY,sBACvB,EACE,KAAK,OAAO,gBAAkB,OAC1B,KAAK,OAAO,kBACZ,KAAK,OAAO,kBACZV,EACAE,EACA,KAAK,OAAO,gBAClB,EACED,EAAcI,GAAsBI,EAA0BJ,EAAqB,EAAI,GACzF,SAAU,KAAK,OAAO,mBACtB,YAAaI,EAA0B,SAAW,OAClD,cAAe,MACf,SAAU,GACZ,CAAC,EAEIC,CACT,CAEA,aAAaJ,EAAwD,CACnE,GAAM,CAAE,cAAAC,CAAc,EAAID,EAEpB,CAAE,mBAAAD,EAAoB,aAAAL,EAAc,kBAAAI,EAAmB,YAAAH,CAAY,EAAIM,EAEvEI,EAAqC,CACzC,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACZ,EACA,EAAGX,EAAeI,EAClBH,EACA,MAAOG,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACzB,EACA,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACZ,EACA,EAAGL,EACHC,EACA,MAAOG,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACzB,EACA,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACZ,EACA,EAAGL,EACH,EAAGC,EAAcI,EACjB,MAAOD,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACzB,EACA,CACE,KAAM,CACJ,KAAM,KAAK,KAAK,cAChB,KAAM,KAAK,YAAY,kBACvB,EAAG,EACH,EAAG,EACH,SAAU,KAAK,OAAO,sBACtB,YAAa,SACb,cAAe,SACf,SAAU,CACZ,EACA,EAAGL,EAAeI,EAClB,EAAGH,EAAcI,EACjB,MAAOD,EACP,OAAQC,EACR,KAAM,KAAK,YAAY,aACzB,CACF,EACA,QAAWO,KAAYD,EACrBC,EAAS,KAAK,EAAIA,EAAS,EAAIA,EAAS,MAAQ,EAE5C,KAAK,KAAK,OAAO,SAAW,GAC9BA,EAAS,KAAK,EAAIA,EAAS,EAAIA,EAAS,OAAS,EACjDA,EAAS,KAAK,cAAgB,WAG9BA,EAAS,KAAK,EAAIA,EAAS,EAAI,KAAK,OAAO,uBAC3CA,EAAS,KAAK,cAAgB,OAIlC,OAAOD,CACT,CAEA,kBAAkBL,EAAoD,CACpE,GAAM,CAAE,cAAAC,CAAc,EAAID,EAEpB,CAAE,eAAAH,EAAgB,aAAAH,EAAc,YAAAC,EAAa,cAAAC,CAAc,EAAIK,EAE/DM,EAAQC,GAAY,EACvB,OAAO,CAAC,EAAG,CAAC,CAAC,EACb,MAAM,CAACd,EAAcE,EAAgBF,CAAY,CAAC,EAE/Ce,EAAQD,GAAY,EACvB,OAAO,CAAC,EAAG,CAAC,CAAC,EACb,MAAM,CAACX,EAAiBF,EAAaA,CAAW,CAAC,EA2BpD,OAzBoC,KAAK,KAAK,OAAO,IAAKe,GAAU,CAClE,IAAMC,EAAc,KAAK,QAAQ,IAAID,EAAM,SAAU,EACrD,OAAIC,IACFD,EAAQ,CAAE,GAAGC,EAAa,GAAGD,CAAM,GAEJ,CAC/B,EAAGH,EAAMG,EAAM,CAAC,EAChB,EAAGD,EAAMC,EAAM,CAAC,EAChB,KAAMA,EAAM,OAAS,KAAK,YAAY,kBACtC,OAAQA,EAAM,QAAU,KAAK,OAAO,YACpC,KAAM,CACJ,KAAMA,EAAM,KACZ,KAAM,KAAK,YAAY,sBACvB,EAAGH,EAAMG,EAAM,CAAC,EAChB,EAAGD,EAAMC,EAAM,CAAC,EAAI,KAAK,OAAO,iBAChC,YAAa,SACb,cAAe,MACf,SAAU,KAAK,OAAO,mBACtB,SAAU,CACZ,EACA,YAAaA,EAAM,aAAe,KAAK,YAAY,kBACnD,YAAaA,EAAM,aAAe,KACpC,CAEF,CAAC,CAEH,CAEA,WAAWV,EAAmD,CAC5D,IAAMY,EAA0B,KAAK,OAAO,kCAAoC,EAC1E,CAAE,cAAAX,CAAc,EAAID,EAEpB,CACJ,mBAAAD,EACA,eAAAF,EACA,aAAAH,EACA,kBAAAI,EACA,YAAAH,EACA,cAAAC,CACF,EAAIK,EA0DJ,MAxDwC,CAEtC,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIP,EAAekB,EACnB,GAAIjB,EACJ,GAAID,EAAeE,EAAgBgB,EACnC,GAAIjB,CACN,EAEA,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAID,EAAeE,EACnB,GAAID,EAAciB,EAClB,GAAIlB,EAAeE,EACnB,GAAID,EAAcE,EAAiBe,CACrC,EAEA,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAekB,EACnB,GAAIjB,EAAcE,EAClB,GAAIH,EAAeE,EAAgBgB,EACnC,GAAIjB,EAAcE,CACpB,EAEA,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIH,EACJ,GAAIC,EAAciB,EAClB,GAAIlB,EACJ,GAAIC,EAAcE,EAAiBe,CACrC,EAEA,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAeI,EACnB,GAAIH,EAAciB,EAClB,GAAIlB,EAAeI,EACnB,GAAIH,EAAcE,EAAiBe,CACrC,EAEA,CACE,WAAY,KAAK,YAAY,iCAC7B,YAAa,KAAK,OAAO,kCACzB,GAAIlB,EAAekB,EACnB,GAAIjB,EAAcI,EAClB,GAAIL,EAAeE,EAAgBgB,EACnC,GAAIjB,EAAcI,CACpB,CACF,CAEF,CAEA,SAASZ,EAAkD,CACzD,GAAIA,EACF,MAAO,CACL,KAAM,KAAK,KAAK,UAChB,KAAM,KAAK,YAAY,kBACvB,SAAU,KAAK,OAAO,cACtB,cAAe,MACf,YAAa,SACb,SAAU,EACV,EAAG,KAAK,OAAO,aACf,EAAG,KAAK,OAAO,WAAa,CAC9B,CAGJ,CAEA,OAA2B,CACzB,IAAMF,EACJ,KAAK,OAAO,WAAa,CAAC,EAAE,KAAK,KAAK,eAAiB,KAAK,KAAK,gBAC7DC,EACJ,KAAK,OAAO,WAAa,CAAC,EAAE,KAAK,KAAK,cAAgB,KAAK,KAAK,iBAC5DC,EAAY,KAAK,OAAO,WAAa,CAAC,CAAC,KAAK,KAAK,UAEjDH,EAAgB,KAAK,KAAK,OAAO,OAAS,EAAI,SAAW,KAAK,OAAO,cAErE6B,EAAkB,KAAK,eAAe7B,EAAeC,EAAWC,EAAWC,CAAS,EAE1F,MAAO,CACL,OAAQ,KAAK,kBAAkB0B,CAAe,EAC9C,UAAW,KAAK,aAAaA,CAAe,EAC5C,WAAY,KAAK,cAAc7B,EAAeC,EAAWC,EAAW2B,CAAe,EACnF,YAAa,KAAK,WAAWA,CAAe,EAC5C,MAAO,KAAK,SAAS1B,CAAS,CAChC,CACF,CACF,EC9mBA,IAAM2B,EAAN,cAAgC,KAAM,CAAtC,MAAsC,CAAAC,EAAA,0BACpC,YAAYC,EAAeC,EAAeC,EAAc,CACtD,MAAM,aAAaF,CAAK,IAAIC,CAAK,mCAAmCC,CAAI,EAAE,EAC1E,KAAK,KAAO,mBACd,CACF,EAEA,SAASC,GAAgBF,EAAwB,CAC/C,MAAO,CAAC,oCAAoC,KAAKA,CAAK,CACxD,CAFSF,EAAAI,GAAA,mBAIT,SAASC,GAAeH,EAAwB,CAC9C,MAAO,CAAC,QAAQ,KAAKA,CAAK,CAC5B,CAFSF,EAAAK,GAAA,kBAIT,SAASC,GAAqBJ,EAAwB,CACpD,MAAO,CAAC,UAAU,KAAKA,CAAK,CAC9B,CAFSF,EAAAM,GAAA,wBCKT,IAAMC,GAASC,GAAU,EAEzB,SAASC,EAAcC,EAAc,CACnC,OAAOC,GAAaD,EAAK,KAAK,EAAGH,EAAM,CACzC,CAFSK,EAAAH,EAAA,iBAST,IAAMI,EAAkB,IAAIC,GAE5B,SAASC,GAAiBC,EAAqB,CAC7CH,EAAgB,QAAQ,CAAE,cAAeJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACxE,CAFSJ,EAAAG,GAAA,oBAIT,SAASE,GAAiBD,EAAqB,CAC7CH,EAAgB,QAAQ,CAAE,cAAeJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACxE,CAFSJ,EAAAK,GAAA,oBAIT,SAASC,GAAiBF,EAAqB,CAC7CH,EAAgB,QAAQ,CAAE,cAAeJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACxE,CAFSJ,EAAAM,GAAA,oBAIT,SAASC,GAAiBH,EAAqB,CAC7CH,EAAgB,QAAQ,CAAE,cAAeJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACxE,CAFSJ,EAAAO,GAAA,oBAIT,SAASC,GAAiBJ,EAAqB,CAC7CH,EAAgB,QAAQ,CAAE,cAAeJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACxE,CAFSJ,EAAAQ,GAAA,oBAIT,SAASC,GAAkBL,EAAqB,CAC9CH,EAAgB,QAAQ,CAAE,eAAgBJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACzE,CAFSJ,EAAAS,GAAA,qBAIT,SAASC,GAAgBN,EAAqB,CAC5CH,EAAgB,QAAQ,CAAE,aAAcJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CACvE,CAFSJ,EAAAU,GAAA,mBAIT,SAASC,GAAmBP,EAAqB,CAC/CH,EAAgB,QAAQ,CAAE,gBAAiBJ,EAAcO,EAAQ,IAAI,CAAE,CAAC,CAC1E,CAFSJ,EAAAW,GAAA,sBAIT,SAASC,GAAYC,EAAgC,CACnD,IAAMC,EAA6B,CAAC,EACpC,QAAWC,KAASF,EAAQ,CAC1B,GAAM,CAACG,EAAKC,CAAK,EAAIF,EAAM,KAAK,EAAE,MAAM,SAAS,EACjD,GAAIC,IAAQ,SAAU,CACpB,GAAIE,GAAeD,CAAK,EACtB,MAAM,IAAIE,EAAkBH,EAAKC,EAAO,QAAQ,EAElDH,EAAa,OAAS,SAASG,CAAK,CACtC,SAAWD,IAAQ,QAAS,CAC1B,GAAII,GAAgBH,CAAK,EACvB,MAAM,IAAIE,EAAkBH,EAAKC,EAAO,UAAU,EAEpDH,EAAa,MAAQG,CACvB,SAAWD,IAAQ,eAAgB,CACjC,GAAII,GAAgBH,CAAK,EACvB,MAAM,IAAIE,EAAkBH,EAAKC,EAAO,UAAU,EAEpDH,EAAa,YAAcG,CAC7B,SAAWD,IAAQ,eAAgB,CACjC,GAAIK,GAAqBJ,CAAK,EAC5B,MAAM,IAAIE,EAAkBH,EAAKC,EAAO,6BAA6B,EAEvEH,EAAa,YAAcG,CAC7B,KACE,OAAM,IAAI,MAAM,eAAeD,CAAG,oBAAoB,CAE1D,CACA,OAAOF,CACT,CA7BSd,EAAAY,GAAA,eA+BT,SAASU,GAASlB,EAAqBmB,EAAmBC,EAAWC,EAAWZ,EAAkB,CAChG,IAAMC,EAAeF,GAAYC,CAAM,EACvCZ,EAAgB,UAAU,CACxB,CACE,EAAAuB,EACA,EAAAC,EACA,KAAM5B,EAAcO,EAAQ,IAAI,EAChC,UAAAmB,EACA,GAAGT,CACL,CACF,CAAC,CACH,CAXSd,EAAAsB,GAAA,YAaT,SAASI,GAASH,EAAmBV,EAAkB,CACrDZ,EAAgB,SAASsB,EAAWX,GAAYC,CAAM,CAAC,CACzD,CAFSb,EAAA0B,GAAA,YAIT,SAASC,GAASC,EAAe,CAC/B3B,EAAgB,UAAU,CAAE,WAAY2B,CAAM,CAAC,CACjD,CAFS5B,EAAA2B,GAAA,YAIT,SAASE,GAAUC,EAAgB,CACjC7B,EAAgB,UAAU,CAAE,YAAa6B,CAAO,CAAC,CACnD,CAFS9B,EAAA6B,GAAA,aAIT,SAASE,IAAkB,CACzB,IAAMpC,EAASC,GAAU,EACnB,CAAE,eAAAoC,EAAgB,cAAeC,CAAoB,EAAItC,EAC/D,OAAIsC,GACFhC,EAAgB,UAAUgC,CAAmB,EAE/ChC,EAAgB,eAAe,CAC7B,cAAe+B,EAAe,cAC9B,cAAeA,EAAe,cAC9B,cAAeA,EAAe,cAC9B,cAAeA,EAAe,cAC9B,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,kBAAmBA,EAAe,kBAClC,sBAAuBA,EAAe,sBACtC,sBAAuBA,EAAe,sBACtC,sBAAuBA,EAAe,sBACtC,iCAAkCA,EAAe,iCACjD,iCAAkCA,EAAe,iCACjD,kBAAmBA,EAAe,iBACpC,CAAC,EACD/B,EAAgB,QAAQ,CAAE,UAAWiC,GAAgB,CAAE,CAAC,EACjDjC,EAAgB,MAAM,CAC/B,CAzBSD,EAAA+B,GAAA,mBA2BT,IAAMI,GAAQnC,EAAA,UAAY,CACxBC,EAAgB,MAAM,EACtBkC,GAAY,CACd,EAHc,SAKPC,GAAQ,CACb,SAAAT,GACA,UAAAE,GACA,iBAAA1B,GACA,iBAAAE,GACA,iBAAAC,GACA,iBAAAC,GACA,iBAAAC,GACA,kBAAAC,GACA,gBAAAC,GACA,mBAAAC,GACA,YAAAC,GACA,SAAAU,GACA,SAAAI,GACA,gBAAAK,GACA,MAAAI,GACA,YAAAE,GACA,YAAAC,GACA,gBAAAC,GACA,gBAAAL,GACA,kBAAAM,GACA,kBAAAC,EACF,EC/JO,IAAMC,GAAOC,EAAA,CAACC,EAAaC,EAAYC,EAAkBC,IAAqB,CACnF,SAASC,EAAoBC,EAAkC,CAC7D,OAAOA,IAAkB,MAAQ,UAAY,QAC/C,CAFSN,EAAAK,EAAA,uBAIT,SAASE,EAAcC,EAA8B,CACnD,OAAOA,IAAgB,OAAS,QAAU,QAC5C,CAFSR,EAAAO,EAAA,iBAIT,SAASE,EAAkBC,EAAkD,CAC3E,MAAO,aAAaA,EAAK,CAAC,KAAKA,EAAK,CAAC,YAAYA,EAAK,UAAY,CAAC,GACrE,CAFSV,EAAAS,EAAA,qBAIT,IAAME,EAAOC,GAAU,EAEvBC,GAAI,MAAM;AAAA,EAA+BZ,CAAG,EAE5C,IAAMa,EAAgBH,EAAK,cAEvBI,EACAD,IAAkB,YACpBC,EAAiBC,GAAO,KAAOd,CAAE,GAOnC,IAAMe,GAJJH,IAAkB,UACdE,GAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,GAAO,MAAM,GAEF,OAAO,QAAQd,CAAE,IAAI,EAEhCgB,EAAQD,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAE5CE,EAAQR,EAAK,eAAe,YAAc,IAC1CS,EAAST,EAAK,eAAe,aAAe,IAElDU,GAAiBJ,EAAKG,EAAQD,EAAOR,EAAK,eAAe,aAAe,EAAI,EAE5EM,EAAI,KAAK,UAAW,OAASE,EAAQ,IAAMC,CAAM,EAGjDhB,EAAQ,GAAG,UAAUgB,CAAM,EAE3BhB,EAAQ,GAAG,SAASe,CAAK,EAGzB,IAAMG,EAAkClB,EAAQ,GAAG,gBAAgB,EAE7DmB,EAAiBL,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EAC5DM,GAAcN,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EACtDO,GAAiBP,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAC9DQ,GAAaR,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,QAAQ,EACrDS,GAAaT,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAEtDI,EAAa,OACfK,GACG,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,CAAC,EACX,KAAK,OAAQL,EAAa,MAAM,IAAI,EACpC,KAAK,YAAaA,EAAa,MAAM,QAAQ,EAC7C,KAAK,oBAAqBjB,EAAoBiB,EAAa,MAAM,aAAa,CAAC,EAC/E,KAAK,cAAef,EAAce,EAAa,MAAM,WAAW,CAAC,EACjE,KAAK,YAAab,EAAkBa,EAAa,KAAK,CAAC,EACvD,KAAKA,EAAa,MAAM,IAAI,EAG7BA,EAAa,aACfE,GACG,UAAU,MAAM,EAChB,KAAKF,EAAa,WAAW,EAC7B,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAOZ,GAA2BA,EAAK,EAAE,EAC9C,KAAK,KAAOA,GAA2BA,EAAK,EAAE,EAC9C,KAAK,KAAOA,GAA2BA,EAAK,EAAE,EAC9C,KAAK,KAAOA,GAA2BA,EAAK,EAAE,EAC9C,MAAM,SAAWA,GAA2BA,EAAK,UAAU,EAC3D,MAAM,eAAiBA,GAA2BA,EAAK,WAAW,EAGvE,IAAMkB,GAAYL,EACf,UAAU,YAAY,EACtB,KAAKD,EAAa,SAAS,EAC3B,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,UAAU,EAE3BM,GACG,OAAO,MAAM,EACb,KAAK,IAAMlB,GAAgCA,EAAK,CAAC,EACjD,KAAK,IAAMA,GAAgCA,EAAK,CAAC,EACjD,KAAK,QAAUA,GAAgCA,EAAK,KAAK,EACzD,KAAK,SAAWA,GAAgCA,EAAK,MAAM,EAC3D,KAAK,OAASA,GAAgCA,EAAK,IAAI,EAE1DkB,GACG,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,CAAC,EACX,KAAK,OAASlB,GAAgCA,EAAK,KAAK,IAAI,EAC5D,KAAK,YAAcA,GAAgCA,EAAK,KAAK,QAAQ,EACrE,KAAK,oBAAsBA,GAC1BL,EAAoBK,EAAK,KAAK,aAAa,CAC7C,EACC,KAAK,cAAgBA,GAAgCH,EAAcG,EAAK,KAAK,WAAW,CAAC,EACzF,KAAK,YAAcA,GAAgCD,EAAkBC,EAAK,IAAI,CAAC,EAC/E,KAAMA,GAAgCA,EAAK,KAAK,IAAI,EAExCgB,GACZ,UAAU,SAAS,EACnB,KAAKJ,EAAa,UAAU,EAC5B,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,OAAO,EAGrB,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,CAAC,EACX,KAAMZ,GAA2BA,EAAK,IAAI,EAC1C,KAAK,OAASA,GAA2BA,EAAK,IAAI,EAClD,KAAK,YAAcA,GAA2BA,EAAK,QAAQ,EAC3D,KAAK,oBAAsBA,GAA2BL,EAAoBK,EAAK,aAAa,CAAC,EAC7F,KAAK,cAAgBA,GAA2BH,EAAcG,EAAK,WAAW,CAAC,EAC/E,KAAK,YAAcA,GAA2BD,EAAkBC,CAAI,CAAC,EAExE,IAAMmB,GAAaJ,GAChB,UAAU,cAAc,EACxB,KAAKH,EAAa,MAAM,EACxB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,QAAS,YAAY,EAE7BO,GACG,OAAO,QAAQ,EACf,KAAK,KAAOnB,GAA4BA,EAAK,CAAC,EAC9C,KAAK,KAAOA,GAA4BA,EAAK,CAAC,EAC9C,KAAK,IAAMA,GAA4BA,EAAK,MAAM,EAClD,KAAK,OAASA,GAA4BA,EAAK,IAAI,EACnD,KAAK,SAAWA,GAA4BA,EAAK,WAAW,EAC5D,KAAK,eAAiBA,GAA4BA,EAAK,WAAW,EAErEmB,GACG,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,CAAC,EACX,KAAMnB,GAA4BA,EAAK,KAAK,IAAI,EAChD,KAAK,OAASA,GAA4BA,EAAK,KAAK,IAAI,EACxD,KAAK,YAAcA,GAA4BA,EAAK,KAAK,QAAQ,EACjE,KAAK,oBAAsBA,GAC1BL,EAAoBK,EAAK,KAAK,aAAa,CAC7C,EACC,KAAK,cAAgBA,GAA4BH,EAAcG,EAAK,KAAK,WAAW,CAAC,EACrF,KAAK,YAAcA,GAA4BD,EAAkBC,EAAK,IAAI,CAAC,CAChF,EA1JoB,QA4JboB,GAAQ,CACb,KAAA/B,EACF,ECxKO,IAAMgC,GAA6B,CACxC,OAAAC,GACA,GAAAC,GACA,SAAAC,GACA,OAAQC,EAAA,IAAM,GAAN,SACV", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "$VT", "$VU", "$VV", "$VW", "$VX", "$VY", "$VZ", "$V_", "$V$", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "quadrant_default", "parser", "defaultThemeVariables", "getThemeVariables", "QuadrantBuilder", "__name", "defaultConfig_default", "log", "data", "points", "className", "styles", "config", "themeConfig", "xAxisPosition", "showXAxis", "showYAxis", "showTitle", "xAxisSpaceCalculation", "xAxisSpace", "yAxisSpaceCalculation", "yAxisSpace", "titleSpaceCalculation", "titleSpace", "quadrantLeft", "quadrantTop", "quadrantWidth", "quadrantHeight", "quadrantHalfWidth", "quadrantHalfHeight", "spaceData", "quadrantSpace", "drawXAxisLabelsInMiddle", "drawYAxisLabelsInMiddle", "axisLabels", "quadrants", "quadrant", "xAxis", "linear", "yAxis", "point", "classStyles", "halfExternalBorderWidth", "calculatedSpace", "InvalidStyleError", "__name", "style", "value", "type", "validateHexCode", "validateNumber", "validateSizeInPixels", "config", "getConfig", "textSanitizer", "text", "sanitizeText", "__name", "quadrantBuilder", "QuadrantBuilder", "setQuadrant1Text", "textObj", "setQuadrant2Text", "setQuadrant3Text", "setQuadrant4Text", "setXAxisLeftText", "setXAxisRightText", "setYAxisTopText", "setYAxisBottomText", "parseStyles", "styles", "stylesObject", "style", "key", "value", "validateNumber", "InvalidStyleError", "validateHexCode", "validateSizeInPixels", "addPoint", "className", "x", "y", "addClass", "<PERSON><PERSON><PERSON><PERSON>", "width", "setHeight", "height", "getQuadrantData", "themeVariables", "quadrantChartConfig", "getDiagramTitle", "clear", "quadrantDb_default", "setAccTitle", "getAccTitle", "setDiagramTitle", "getAccDescription", "setAccDescription", "draw", "__name", "txt", "id", "_version", "diagObj", "getDominantBaseLine", "horizontalPos", "getTextAnchor", "verticalPos", "getTransformation", "data", "conf", "getConfig", "log", "securityLevel", "sandboxElement", "select_default", "svg", "group", "width", "height", "configureSvgSize", "quadrantData", "quadrantsGroup", "borderGroup", "dataPointGroup", "labelGroup", "titleGroup", "quadrants", "dataPoints", "quadrantRenderer_default", "diagram", "quadrant_default", "quadrantDb_default", "quadrantRenderer_default", "__name"]}