import{Y as c}from"./chunk-U6SPV2NK.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var f=r(t=>{let{handDrawnSeed:e}=c();return{fill:t,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:t,seed:e}},"solidStateFill"),p=r(t=>{let e=h([...t.cssCompiledStyles||[],...t.cssStyles||[]]);return{stylesMap:e,stylesArray:[...e]}},"compileStyles"),h=r(t=>{let e=new Map;return t.forEach(o=>{let[n,i]=o.split(":");e.set(n.trim(),i?.trim())}),e},"styles2Map"),g=r(t=>t==="color"||t==="font-size"||t==="font-family"||t==="font-weight"||t==="font-style"||t==="text-decoration"||t==="text-align"||t==="text-transform"||t==="line-height"||t==="letter-spacing"||t==="word-spacing"||t==="text-shadow"||t==="text-overflow"||t==="white-space"||t==="word-wrap"||t==="word-break"||t==="overflow-wrap"||t==="hyphens","isLabelStyle"),u=r(t=>{let{stylesArray:e}=p(t),o=[],n=[],i=[],l=[];return e.forEach(s=>{let a=s[0];g(a)?o.push(s.join(":")+" !important"):(n.push(s.join(":")+" !important"),a.includes("stroke")&&i.push(s.join(":")+" !important"),a==="fill"&&l.push(s.join(":")+" !important"))}),{labelStyles:o.join(";"),nodeStyles:n.join(";"),stylesArray:e,borderStyles:i,backgroundStyles:l}},"styles2String"),m=r((t,e)=>{let{themeVariables:o,handDrawnSeed:n}=c(),{nodeBorder:i,mainBkg:l}=o,{stylesMap:s}=p(t);return Object.assign({roughness:.7,fill:s.get("fill")||l,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:s.get("stroke")||i,seed:n,strokeWidth:s.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0]},e)},"userNodeOverrides");export{f as a,p as b,g as c,u as d,m as e};
