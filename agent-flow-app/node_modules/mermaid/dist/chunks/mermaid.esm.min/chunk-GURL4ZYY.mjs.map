{"version": 3, "sources": ["../../../src/rendering-util/insertElementsForSize.js"], "sourcesContent": ["import { select } from 'd3';\n\nexport const getDiagramElement = (id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  // Run the renderer. This is what draws the final graph.\n\n  return svg;\n};\n"], "mappings": "mFAEO,IAAMA,EAAoBC,EAAA,CAACC,EAAIC,IAAkB,CACtD,IAAIC,EACJ,OAAID,IAAkB,YACpBC,EAAiBC,EAAO,KAAOH,CAAE,IAGjCC,IAAkB,UACdE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,EAAO,MAAM,GAEF,OAAO,QAAQH,CAAE,IAAI,CAKxC,EAfiC", "names": ["getDiagramElement", "__name", "id", "securityLevel", "sandboxElement", "select_default"]}