import{a as d,b as n,c as t,d as i,e as v,f as e,g as u,m as l,p as s,q as c}from"./chunk-QFDCAKKT.mjs";import{a}from"./chunk-GTKDMUJJ.mjs";var R=class extends c{static{a(this,"RadarTokenBuilder")}static{e(this,"RadarTokenBuilder")}constructor(){super(["radar-beta"])}},M={parser:{TokenBuilder:e(()=>new R,"TokenBuilder"),ValueConverter:e(()=>new s,"ValueConverter")}};function p(m=i){let r=t(n(m),u),o=t(d({shared:r}),l,M);return r.ServiceRegistry.register(o),{shared:r,Radar:o}}a(p,"createRadarServices");e(p,"createRadarServices");export{M as a,p as b};
