import{a as n,b as s,c as o,d as u,e as v,f as e,g as d,j as c,o as l,q as m}from"./chunk-QFDCAKKT.mjs";import{a as t}from"./chunk-GTKDMUJJ.mjs";var C=class extends m{static{t(this,"PieTokenBuilder")}static{e(this,"PieTokenBuilder")}constructor(){super(["pie","showData"])}},P=class extends l{static{t(this,"PieValueConverter")}static{e(this,"PieValueConverter")}runCustomConverter(i,r,a){if(i.name==="PIE_SECTION_LABEL")return r.replace(/"/g,"").trim()}},p={parser:{TokenBuilder:e(()=>new C,"TokenBuilder"),ValueConverter:e(()=>new P,"ValueConverter")}};function M(i=u){let r=o(s(i),d),a=o(n({shared:r}),c,p);return r.ServiceRegistry.register(a),{shared:r,Pie:a}}t(M,"createPieServices");e(M,"createPieServices");export{p as a,M as b};
