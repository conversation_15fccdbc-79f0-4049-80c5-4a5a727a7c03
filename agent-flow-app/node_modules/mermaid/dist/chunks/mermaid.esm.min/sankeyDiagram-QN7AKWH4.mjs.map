{"version": 3, "sources": ["../../../src/diagrams/sankey/parser/sankey.jison", "../../../src/diagrams/sankey/sankeyDB.ts", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/max.js", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/min.js", "../../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/sum.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/align.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/constant.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankey.js", "../../../../../node_modules/.pnpm/d3-path@1.0.9/node_modules/d3-path/src/path.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/constant.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/point.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/array.js", "../../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/link/index.js", "../../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../../src/rendering-util/uid.ts", "../../../src/diagrams/sankey/sankeyRenderer.ts", "../../../src/diagrams/sankey/sankeyUtils.ts", "../../../src/diagrams/sankey/styles.js", "../../../src/diagrams/sankey/sankeyDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,9],$V1=[1,10],$V2=[1,5,10,12];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SANKEY\":4,\"NEWLINE\":5,\"csv\":6,\"opt_eof\":7,\"record\":8,\"csv_tail\":9,\"EOF\":10,\"field[source]\":11,\"COMMA\":12,\"field[target]\":13,\"field[value]\":14,\"field\":15,\"escaped\":16,\"non_escaped\":17,\"DQUOTE\":18,\"ESCAPED_TEXT\":19,\"NON_ESCAPED_TEXT\":20,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SANKEY\",5:\"NEWLINE\",10:\"EOF\",11:\"field[source]\",12:\"COMMA\",13:\"field[target]\",14:\"field[value]\",18:\"DQUOTE\",19:\"ESCAPED_TEXT\",20:\"NON_ESCAPED_TEXT\"},\nproductions_: [0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 7:\n\n      const source = yy.findOrCreateNode($$[$0-4].trim().replaceAll('\"\"', '\"'));\n      const target = yy.findOrCreateNode($$[$0-2].trim().replaceAll('\"\"', '\"'));\n      const value = parseFloat($$[$0].trim());\n      yy.addLink(source,target,value);\n    \nbreak;\ncase 8: case 9: case 11:\n this.$=$$[$0]; \nbreak;\ncase 10:\n this.$=$$[$0-1]; \nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:$V0,20:$V1},{1:[2,6],7:11,10:[1,12]},o($V1,[2,4],{9:13,5:[1,14]}),{12:[1,15]},o($V2,[2,8]),o($V2,[2,9]),{19:[1,16]},o($V2,[2,11]),{1:[2,1]},{1:[2,5]},o($V1,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:$V0,20:$V1},{15:18,16:7,17:8,18:$V0,20:$V1},{18:[1,19]},o($V1,[2,3]),{12:[1,20]},o($V2,[2,10]),{15:21,16:7,17:8,18:$V0,20:$V1},o([1,5,10],[2,7])],\ndefaultActions: {11:[2,1],12:[2,5]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: this.pushState('csv'); return 4; \nbreak;\ncase 1: return 10 \nbreak;\ncase 2: return 5 \nbreak;\ncase 3: return 12 \nbreak;\ncase 4: this.pushState('escaped_text'); return 18; \nbreak;\ncase 5: return 20 \nbreak;\ncase 6:this.popState('escaped_text'); return 18; \nbreak;\ncase 7: return 19; \nbreak;\n}\n},\nrules: [/^(?:sankey-beta\\b)/i,/^(?:$)/i,/^(?:((\\u000D\\u000A)|(\\u000A)))/i,/^(?:(\\u002C))/i,/^(?:(\\u0022))/i,/^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i,/^(?:(\\u0022)(?!(\\u0022)))/i,/^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\nconditions: {\"csv\":{\"rules\":[1,2,3,4,5,6,7],\"inclusive\":false},\"escaped_text\":{\"rules\":[6,7],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport common from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle,\n  clear as commonClear,\n} from '../common/commonDb.js';\n\n// Sankey diagram represented by nodes and links between those nodes\nlet links: SankeyLink[] = [];\n// Array of nodes guarantees their order\nlet nodes: SankeyNode[] = [];\n// We also have to track nodes uniqueness (by ID)\nlet nodesMap = new Map<string, SankeyNode>();\n\nconst clear = (): void => {\n  links = [];\n  nodes = [];\n  nodesMap = new Map();\n  commonClear();\n};\n\nclass SankeyLink {\n  constructor(\n    public source: SankeyNode,\n    public target: SankeyNode,\n    public value = 0\n  ) {}\n}\n\n/**\n * @param source - Node where the link starts\n * @param target - Node where the link ends\n * @param value - Describes the amount to be passed\n */\nconst addLink = (source: SankeyN<PERSON>, target: SankeyNode, value: number): void => {\n  links.push(new SankeyLink(source, target, value));\n};\n\nclass SankeyNode {\n  constructor(public ID: string) {}\n}\n\nconst findOrCreateNode = (ID: string): SankeyNode => {\n  ID = common.sanitizeText(ID, getConfig());\n\n  let node = nodesMap.get(ID);\n  if (node === undefined) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n};\n\nconst getNodes = () => nodes;\nconst getLinks = () => links;\n\nconst getGraph = () => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value,\n  })),\n});\n\nexport default {\n  nodesMap,\n  getConfig: () => getConfig().sankey,\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear,\n};\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "export class Uid {\n  private static count = 0;\n  id: string;\n  href: string;\n\n  public static next(name: string): Uid {\n    return new Uid(name + ++Uid.count);\n  }\n\n  constructor(id: string) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n\n  toString(): string {\n    return 'url(' + this.href + ')';\n  }\n}\n", "import type { Diagram } from '../../Diagram.js';\nimport { getConfig, defaultConfig } from '../../diagram-api/diagramAPI.js';\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10,\n} from 'd3';\nimport type { SankeyNode as d3SankeyNode } from 'd3-sankey';\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify,\n} from 'd3-sankey';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport { Uid } from '../../rendering-util/uid.js';\nimport type { SankeyNodeAlignment } from '../../config.type.js';\n\n// Map config options to alignment functions\nconst alignmentsMap: Record<\n  SankeyNodeAlignment,\n  (node: d3SankeyNode<object, object>, n: number) => number\n> = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify,\n};\n\n/**\n * Draws Sankey diagram.\n *\n * @param text - The text of the diagram\n * @param id - The id of the diagram which will be used as a DOM element id¨\n * @param _version - Mermaid version from package.json\n * @param diagObj - A standard diagram containing the db and the text and type etc of the diagram\n */\nexport const draw = function (text: string, id: string, _version: string, diagObj: Diagram): void {\n  // Get Sankey config\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey!;\n\n  // TODO:\n  // This code repeats for every diagram\n  // Figure out what is happening there, probably it should be separated\n  // The main thing is svg object that is a d3 wrapper for svg operations\n  //\n  let sandboxElement: any;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = d3select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? d3select(sandboxElement.nodes()[0].contentDocument.body)\n      : d3select('body');\n  // @ts-ignore TODO root.select is not callable\n  const svg = securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n\n  // Establish svg dimensions and get width and height\n  //\n  const width = conf?.width ?? defaultSankeyConfig.width!;\n  const height = conf?.height ?? defaultSankeyConfig.width!;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth!;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment!;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix!;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix!;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues!;\n\n  // Prepare data for construction based on diagObj.db\n  // This must be a mutable object with `nodes` and `links` properties:\n  //\n  //    {\n  //      \"nodes\": [ { \"id\": \"Alice\" }, { \"id\": \"Bob\" }, { \"id\": \"Carol\" } ],\n  //      \"links\": [ { \"source\": \"Alice\", \"target\": \"Bob\", \"value\": 23 }, { \"source\": \"Bob\", \"target\": \"Carol\", \"value\": 43 } ]\n  //    }\n  //\n  // @ts-ignore TODO: db should be coerced to sankey DB type\n  const graph = diagObj.db.getGraph();\n\n  // Get alignment function\n  const nodeAlign = alignmentsMap[nodeAlignment];\n\n  // Construct and configure a Sankey generator\n  // That will be a function that calculates nodes and links dimensions\n  //\n  const nodeWidth = 10;\n  const sankey = d3Sankey()\n    .nodeId((d: any) => d.id) // we use 'id' property to identify node\n    .nodeWidth(nodeWidth)\n    .nodePadding(10 + (showValues ? 15 : 0))\n    .nodeAlign(nodeAlign)\n    .extent([\n      [0, 0],\n      [width, height],\n    ]);\n\n  // Compute the Sankey layout: calculate nodes and links positions\n  // Our `graph` object will be mutated by this and enriched with other properties\n  //\n  sankey(graph);\n\n  // Get color scheme for the graph\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n\n  // Create rectangles for nodes\n  svg\n    .append('g')\n    .attr('class', 'nodes')\n    .selectAll('.node')\n    .data(graph.nodes)\n    .join('g')\n    .attr('class', 'node')\n    .attr('id', (d: any) => (d.uid = Uid.next('node-')).id)\n    .attr('transform', function (d: any) {\n      return 'translate(' + d.x0 + ',' + d.y0 + ')';\n    })\n    .attr('x', (d: any) => d.x0)\n    .attr('y', (d: any) => d.y0)\n    .append('rect')\n    .attr('height', (d: any) => {\n      return d.y1 - d.y0;\n    })\n    .attr('width', (d: any) => d.x1 - d.x0)\n    .attr('fill', (d: any) => colorScheme(d.id));\n\n  const getText = ({ id, value }: { id: string; value: number }) => {\n    if (!showValues) {\n      return id;\n    }\n    return `${id}\\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  };\n\n  // Create labels for nodes\n  svg\n    .append('g')\n    .attr('class', 'node-labels')\n    .attr('font-size', 14)\n    .selectAll('text')\n    .data(graph.nodes)\n    .join('text')\n    .attr('x', (d: any) => (d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6))\n    .attr('y', (d: any) => (d.y1 + d.y0) / 2)\n    .attr('dy', `${showValues ? '0' : '0.35'}em`)\n    .attr('text-anchor', (d: any) => (d.x0 < width / 2 ? 'start' : 'end'))\n    .text(getText);\n\n  // Creates the paths that represent the links.\n  const link = svg\n    .append('g')\n    .attr('class', 'links')\n    .attr('fill', 'none')\n    .attr('stroke-opacity', 0.5)\n    .selectAll('.link')\n    .data(graph.links)\n    .join('g')\n    .attr('class', 'link')\n    .style('mix-blend-mode', 'multiply');\n\n  const linkColor = conf?.linkColor ?? 'gradient';\n\n  if (linkColor === 'gradient') {\n    const gradient = link\n      .append('linearGradient')\n      .attr('id', (d: any) => (d.uid = Uid.next('linearGradient-')).id)\n      .attr('gradientUnits', 'userSpaceOnUse')\n      .attr('x1', (d: any) => d.source.x1)\n      .attr('x2', (d: any) => d.target.x0);\n\n    gradient\n      .append('stop')\n      .attr('offset', '0%')\n      .attr('stop-color', (d: any) => colorScheme(d.source.id));\n\n    gradient\n      .append('stop')\n      .attr('offset', '100%')\n      .attr('stop-color', (d: any) => colorScheme(d.target.id));\n  }\n\n  let coloring: any;\n  switch (linkColor) {\n    case 'gradient':\n      coloring = (d: any) => d.uid;\n      break;\n    case 'source':\n      coloring = (d: any) => colorScheme(d.source.id);\n      break;\n    case 'target':\n      coloring = (d: any) => colorScheme(d.target.id);\n      break;\n    default:\n      coloring = linkColor;\n  }\n\n  link\n    .append('path')\n    .attr('d', d3SankeyLinkHorizontal())\n    .attr('stroke', coloring)\n    .attr('stroke-width', (d: any) => Math.max(1, d.width));\n\n  setupGraphViewbox(undefined, svg, 0, useMaxWidth);\n};\n\nexport default {\n  draw,\n};\n", "export const prepareTextForParsing = (text: string): string => {\n  const textToParse = text\n    .replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, '') // remove all trailing spaces for each row\n    .replaceAll(/([\\n\\r])+/g, '\\n') // remove empty lines duplicated\n    .trim();\n\n  return textToParse;\n};\n", "const getStyles = (options) =>\n  `.label {\n      font-family: ${options.fontFamily};\n    }`;\n\nexport default getStyles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: jison doesn't export types\nimport parser from './parser/sankey.jison';\nimport db from './sankeyDB.js';\nimport renderer from './sankeyRenderer.js';\nimport { prepareTextForParsing } from './sankeyUtils.js';\nimport sankeyStyles from './styles.js';\n\nconst originalParse = parser.parse.bind(parser);\nparser.parse = (text: string) => originalParse(prepareTextForParsing(text));\n\nexport const diagram: DiagramDefinition = {\n  styles: sankeyStyles,\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": "4LAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAE,EAAE,CAAC,IAAIA,EAAEA,GAAG,CAAC,EAAE,EAAEE,EAAE,OAAO,IAAIF,EAAEE,EAAE,CAAC,CAAC,EAAEC,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEI,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,EAAE,EACvGP,EAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,OAAS,EAAE,QAAU,EAAE,IAAM,EAAE,QAAU,EAAE,OAAS,EAAE,SAAW,EAAE,IAAM,GAAG,gBAAgB,GAAG,MAAQ,GAAG,gBAAgB,GAAG,eAAe,GAAG,MAAQ,GAAG,QAAU,GAAG,YAAc,GAAG,OAAS,GAAG,aAAe,GAAG,iBAAmB,GAAG,QAAU,EAAE,KAAO,CAAC,EAC/R,WAAY,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,GAAG,MAAM,GAAG,gBAAgB,GAAG,QAAQ,GAAG,gBAAgB,GAAG,eAAe,GAAG,SAAS,GAAG,eAAe,GAAG,kBAAkB,EAC7K,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACtF,cAAeA,EAAA,SAAmBM,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GAEC,IAAMI,EAASL,EAAG,iBAAiBE,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAE,WAAW,KAAM,GAAG,CAAC,EAClEE,EAASN,EAAG,iBAAiBE,EAAGE,EAAG,CAAC,EAAE,KAAK,EAAE,WAAW,KAAM,GAAG,CAAC,EAClEG,EAAQ,WAAWL,EAAGE,CAAE,EAAE,KAAK,CAAC,EACtCJ,EAAG,QAAQK,EAAOC,EAAOC,CAAK,EAEpC,MACA,IAAK,GAAG,IAAK,GAAG,IAAK,IACpB,KAAK,EAAEL,EAAGE,CAAE,EACb,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,EAAG,CAAC,EACf,KACA,CACA,EApBe,aAqBf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGV,EAAI,GAAGC,CAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEM,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEN,EAAEM,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEN,EAAEM,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEN,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGD,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAGD,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEM,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAGF,EAAI,GAAGC,CAAG,EAAEL,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAClZ,eAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAClC,WAAYC,EAAA,SAAqBiB,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOnB,EAAA,SAAeoB,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOpB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGoB,EAAa,EAAGC,EAAS,EAAGC,EAAM,EAClKC,EAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAAS/B,KAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IAC/C+B,EAAY,GAAG/B,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGrC8B,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,EAAQF,EAAM,OAClBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,EAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,EAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSpC,EAAAmC,EAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,EACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXatC,EAAAqC,GAAA,OAajB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,EAAGC,EAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,EAAUC,IAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,EAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,GAC1BqB,EAAS,KAAK,IAAO,KAAK,WAAWH,CAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,EAAS,wBAA0B1C,EAAW,GAAK;AAAA,EAAQuB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,EAAS,wBAA0B1C,EAAW,GAAK,iBAAmB+B,GAAUV,EAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,EAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,EACL,SAAUgB,CACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASDD,EAASC,EACTA,EAAiB,OATjBjC,EAASwB,EAAM,OACfzB,EAASyB,EAAM,OACfvB,EAAWuB,EAAM,SACjBE,EAAQF,EAAM,OACVJ,EAAa,GACbA,KAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,IACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,EAAI,KAAK,cAAc,MAAMC,EAAO,CAChCvC,EACAC,EACAC,EACAwB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,CAAI,CAAC,EACV,OAAOc,EAAM,IACb,OAAOA,EAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,EAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,CAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAEGjB,EAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAW/B,EAAA,SAAoBiB,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASjB,EAAA,SAAUoB,EAAOX,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASW,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMpB,EAAA,UAAY,CACV,IAAImD,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMnD,EAAA,SAAUmD,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAK/C,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUoC,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUpC,EAAA,UAAY,CACd,IAAIsD,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAActD,EAAA,UAAY,CAClB,IAAIuD,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAavD,EAAA,UAAY,CACjB,IAAIwD,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAWzD,EAAA,SAAS0D,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASrC,KAAK2D,EACV,KAAK3D,CAAC,EAAI2D,EAAO3D,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIsC,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAItC,EAAA,UAAgB,CACZ,IAAI4C,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM5C,EAAA,SAAgBiE,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAASjE,EAAA,UAAqB,CACtB,IAAIoC,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcpC,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBoC,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUpC,EAAA,SAAoBiE,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAejE,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBS,EAAGyD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAG,YAAK,UAAU,KAAK,EAAU,EACtC,MACA,IAAK,GAAG,MAAO,IAEf,IAAK,GAAG,MAAO,GAEf,IAAK,GAAG,MAAO,IAEf,IAAK,GAAG,YAAK,UAAU,cAAc,EAAU,GAC/C,MACA,IAAK,GAAG,MAAO,IAEf,IAAK,GAAE,YAAK,SAAS,cAAc,EAAU,GAC7C,MACA,IAAK,GAAG,MAAO,GAEf,CACA,EApBe,aAqBf,MAAO,CAAC,sBAAsB,UAAU,kCAAkC,iBAAiB,iBAAiB,qDAAqD,6BAA6B,kGAAkG,EAChS,WAAY,CAAC,IAAM,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,UAAY,EAAK,EAAE,aAAe,CAAC,MAAQ,CAAC,EAAE,CAAC,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,UAAY,EAAI,CAAC,CACtK,EACA,OAAOpC,CACP,EAAG,EACHjC,EAAO,MAAQiC,EACf,SAASuC,GAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAtE,EAAAsE,EAAA,UAGTA,EAAO,UAAYxE,EAAOA,EAAO,OAASwE,EACnC,IAAIA,CACX,EAAG,EACFxE,GAAO,OAASA,GAEhB,IAAOyE,EAAQC,GCvlBhB,IAAIC,GAAsB,CAAC,EAEvBC,GAAsB,CAAC,EAEvBC,EAAW,IAAI,IAEbC,GAAQC,EAAA,IAAY,CACxBJ,GAAQ,CAAC,EACTC,GAAQ,CAAC,EACTC,EAAW,IAAI,IACfC,GAAY,CACd,EALc,SAORE,GAAN,KAAiB,CACf,YACSC,EACAC,EACAC,EAAQ,EACf,CAHO,YAAAF,EACA,YAAAC,EACA,WAAAC,CACN,CA/BL,MA0BiB,CAAAJ,EAAA,mBAMjB,EAOMK,GAAUL,EAAA,CAACE,EAAoBC,EAAoBC,IAAwB,CAC/ER,GAAM,KAAK,IAAIK,GAAWC,EAAQC,EAAQC,CAAK,CAAC,CAClD,EAFgB,WAIVE,GAAN,KAAiB,CACf,YAAmBC,EAAY,CAAZ,QAAAA,CAAa,CA5ClC,MA2CiB,CAAAP,EAAA,mBAEjB,EAEMQ,GAAmBR,EAACO,GAA2B,CACnDA,EAAKE,GAAO,aAAaF,EAAIG,EAAU,CAAC,EAExC,IAAIC,EAAOb,EAAS,IAAIS,CAAE,EAC1B,OAAII,IAAS,SACXA,EAAO,IAAIL,GAAWC,CAAE,EACxBT,EAAS,IAAIS,EAAII,CAAI,EACrBd,GAAM,KAAKc,CAAI,GAEVA,CACT,EAVyB,oBAYnBC,GAAWZ,EAAA,IAAMH,GAAN,YACXgB,GAAWb,EAAA,IAAMJ,GAAN,YAEXkB,GAAWd,EAAA,KAAO,CACtB,MAAOH,GAAM,IAAKc,IAAU,CAAE,GAAIA,EAAK,EAAG,EAAE,EAC5C,MAAOf,GAAM,IAAKmB,IAAU,CAC1B,OAAQA,EAAK,OAAO,GACpB,OAAQA,EAAK,OAAO,GACpB,MAAOA,EAAK,KACd,EAAE,CACJ,GAPiB,YASVC,GAAQ,CACb,SAAAlB,EACA,UAAWE,EAAA,IAAMU,EAAU,EAAE,OAAlB,aACX,SAAAE,GACA,SAAAC,GACA,SAAAC,GACA,QAAAT,GACA,iBAAAG,GACA,YAAAS,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,gBAAAC,GACA,gBAAAC,GACA,MAAAvB,EACF,ECtFe,SAARwB,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,QAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGZ,CACA,OAAOH,CACT,CAnBwBK,EAAAL,EAAA,OCAT,SAARM,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,QAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGZ,CACA,OAAOH,CACT,CAnBwBK,EAAAL,EAAA,OCAT,SAARM,EAAqBC,EAAQC,EAAS,CAC3C,IAAIF,EAAM,EACV,GAAIE,IAAY,OACd,QAASC,KAASF,GACZE,EAAQ,CAACA,KACXH,GAAOG,OAGN,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACZE,EAAQ,CAACD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,KACzCD,GAAOG,EAGb,CACA,OAAOH,CACT,CAjBwBK,EAAAL,EAAA,OCExB,SAASM,GAAYC,EAAG,CACtB,OAAOA,EAAE,OAAO,KAClB,CAFSC,EAAAF,GAAA,eAIF,SAASG,GAAKC,EAAM,CACzB,OAAOA,EAAK,KACd,CAFgBF,EAAAC,GAAA,QAIT,SAASE,GAAMD,EAAME,EAAG,CAC7B,OAAOA,EAAI,EAAIF,EAAK,MACtB,CAFgBF,EAAAG,GAAA,SAIT,SAASE,EAAQH,EAAME,EAAG,CAC/B,OAAOF,EAAK,YAAY,OAASA,EAAK,MAAQE,EAAI,CACpD,CAFgBJ,EAAAK,EAAA,WAIT,SAASC,GAAOJ,EAAM,CAC3B,OAAOA,EAAK,YAAY,OAASA,EAAK,MAChCA,EAAK,YAAY,OAASK,EAAIL,EAAK,YAAaJ,EAAW,EAAI,EAC/D,CACR,CAJgBE,EAAAM,GAAA,UClBD,SAARE,EAA0BC,EAAG,CAClC,OAAO,UAAW,CAChB,OAAOA,CACT,CACF,CAJwBC,EAAAF,EAAA,YCIxB,SAASG,GAAuBC,EAAGC,EAAG,CACpC,OAAOC,GAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAFSE,EAAAJ,GAAA,0BAIT,SAASK,GAAuBJ,EAAGC,EAAG,CACpC,OAAOC,GAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAFSE,EAAAC,GAAA,0BAIT,SAASF,GAAiBF,EAAGC,EAAG,CAC9B,OAAOD,EAAE,GAAKC,EAAE,EAClB,CAFSE,EAAAD,GAAA,oBAIT,SAASG,GAAMC,EAAG,CAChB,OAAOA,EAAE,KACX,CAFSH,EAAAE,GAAA,SAIT,SAASE,GAAUD,EAAG,CACpB,OAAOA,EAAE,KACX,CAFSH,EAAAI,GAAA,aAIT,SAASC,GAAaC,EAAO,CAC3B,OAAOA,EAAM,KACf,CAFSN,EAAAK,GAAA,gBAIT,SAASE,GAAaD,EAAO,CAC3B,OAAOA,EAAM,KACf,CAFSN,EAAAO,GAAA,gBAIT,SAASC,GAAKC,EAAUC,EAAI,CAC1B,IAAMC,EAAOF,EAAS,IAAIC,CAAE,EAC5B,GAAI,CAACC,EAAM,MAAM,IAAI,MAAM,YAAcD,CAAE,EAC3C,OAAOC,CACT,CAJSX,EAAAQ,GAAA,QAMT,SAASI,GAAoB,CAAC,MAAAC,CAAK,EAAG,CACpC,QAAWF,KAAQE,EAAO,CACxB,IAAIC,EAAKH,EAAK,GACVI,EAAKD,EACT,QAAWE,KAAQL,EAAK,YACtBK,EAAK,GAAKF,EAAKE,EAAK,MAAQ,EAC5BF,GAAME,EAAK,MAEb,QAAWA,KAAQL,EAAK,YACtBK,EAAK,GAAKD,EAAKC,EAAK,MAAQ,EAC5BD,GAAMC,EAAK,KAEf,CACF,CAbShB,EAAAY,GAAA,uBAeM,SAARK,IAA0B,CAC/B,IAAIC,EAAK,EAAGJ,EAAK,EAAGK,EAAK,EAAGJ,EAAK,EAC7BK,EAAK,GACLC,EAAK,EAAGC,EACRZ,EAAKN,GACLmB,EAAQC,EACRC,EACAC,EACAb,EAAQR,GACRsB,EAAQpB,GACRqB,EAAa,EAEjB,SAASC,GAAS,CAChB,IAAMvB,EAAQ,CAAC,MAAOO,EAAM,MAAM,KAAM,SAAS,EAAG,MAAOc,EAAM,MAAM,KAAM,SAAS,CAAC,EACvF,OAAAG,EAAiBxB,CAAK,EACtByB,EAAkBzB,CAAK,EACvB0B,EAAkB1B,CAAK,EACvB2B,EAAmB3B,CAAK,EACxB4B,EAAoB5B,CAAK,EACzBM,GAAoBN,CAAK,EAClBA,CACT,CATSN,EAAA6B,EAAA,UAWTA,EAAO,OAAS,SAASvB,EAAO,CAC9B,OAAAM,GAAoBN,CAAK,EAClBA,CACT,EAEAuB,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUzB,EAAK,OAAOyB,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUnB,CACvF,EAEAmB,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUZ,EAAQ,OAAOY,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUN,CAC1F,EAEAM,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUV,EAAOU,EAAGN,GAAUJ,CACjD,EAEAI,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUf,EAAK,CAACe,EAAGN,GAAUT,CAChD,EAEAS,EAAO,YAAc,SAASM,EAAG,CAC/B,OAAO,UAAU,QAAUd,EAAKC,EAAK,CAACa,EAAGN,GAAUR,CACrD,EAEAQ,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUtB,EAAQ,OAAOsB,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUhB,CAC1F,EAEAgB,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUR,EAAQ,OAAOQ,GAAM,WAAaA,EAAIC,EAASD,CAAC,EAAGN,GAAUF,CAC1F,EAEAE,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUT,EAAWS,EAAGN,GAAUH,CACrD,EAEAG,EAAO,KAAO,SAASM,EAAG,CACxB,OAAO,UAAU,QAAUjB,EAAKJ,EAAK,EAAGK,EAAK,CAACgB,EAAE,CAAC,EAAGpB,EAAK,CAACoB,EAAE,CAAC,EAAGN,GAAU,CAACV,EAAKD,EAAIH,EAAKD,CAAE,CAC7F,EAEAe,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUjB,EAAK,CAACiB,EAAE,CAAC,EAAE,CAAC,EAAGhB,EAAK,CAACgB,EAAE,CAAC,EAAE,CAAC,EAAGrB,EAAK,CAACqB,EAAE,CAAC,EAAE,CAAC,EAAGpB,EAAK,CAACoB,EAAE,CAAC,EAAE,CAAC,EAAGN,GAAU,CAAC,CAACX,EAAIJ,CAAE,EAAG,CAACK,EAAIJ,CAAE,CAAC,CACtH,EAEAc,EAAO,WAAa,SAASM,EAAG,CAC9B,OAAO,UAAU,QAAUP,EAAa,CAACO,EAAGN,GAAUD,CACxD,EAEA,SAASE,EAAiB,CAAC,MAAAjB,EAAO,MAAAc,CAAK,EAAG,CACxC,OAAW,CAACU,EAAG1B,CAAI,IAAKE,EAAM,QAAQ,EACpCF,EAAK,MAAQ0B,EACb1B,EAAK,YAAc,CAAC,EACpBA,EAAK,YAAc,CAAC,EAEtB,IAAMF,EAAW,IAAI,IAAII,EAAM,IAAI,CAAC,EAAGwB,IAAM,CAAC3B,EAAG,EAAG2B,EAAGxB,CAAK,EAAG,CAAC,CAAC,CAAC,EAClE,OAAW,CAACwB,EAAGrB,CAAI,IAAKW,EAAM,QAAQ,EAAG,CACvCX,EAAK,MAAQqB,EACb,GAAI,CAAC,OAAAC,EAAQ,OAAAC,CAAM,EAAIvB,EACnB,OAAOsB,GAAW,WAAUA,EAAStB,EAAK,OAASR,GAAKC,EAAU6B,CAAM,GACxE,OAAOC,GAAW,WAAUA,EAASvB,EAAK,OAASR,GAAKC,EAAU8B,CAAM,GAC5ED,EAAO,YAAY,KAAKtB,CAAI,EAC5BuB,EAAO,YAAY,KAAKvB,CAAI,CAC9B,CACA,GAAIU,GAAY,KACd,OAAW,CAAC,YAAAc,EAAa,YAAAC,CAAW,IAAK5B,EACvC2B,EAAY,KAAKd,CAAQ,EACzBe,EAAY,KAAKf,CAAQ,CAG/B,CArBS1B,EAAA8B,EAAA,oBAuBT,SAASC,EAAkB,CAAC,MAAAlB,CAAK,EAAG,CAClC,QAAWF,KAAQE,EACjBF,EAAK,MAAQA,EAAK,aAAe,OAC3B,KAAK,IAAI+B,EAAI/B,EAAK,YAAaT,EAAK,EAAGwC,EAAI/B,EAAK,YAAaT,EAAK,CAAC,EACnES,EAAK,UAEf,CANSX,EAAA+B,EAAA,qBAQT,SAASC,EAAkB,CAAC,MAAAnB,CAAK,EAAG,CAClC,IAAM8B,EAAI9B,EAAM,OACZ+B,EAAU,IAAI,IAAI/B,CAAK,EACvBgC,EAAO,IAAI,IACXC,EAAI,EACR,KAAOF,EAAQ,MAAM,CACnB,QAAWjC,KAAQiC,EAAS,CAC1BjC,EAAK,MAAQmC,EACb,OAAW,CAAC,OAAAP,CAAM,IAAK5B,EAAK,YAC1BkC,EAAK,IAAIN,CAAM,CAEnB,CACA,GAAI,EAAEO,EAAIH,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACb,CACF,CAhBS7C,EAAAgC,EAAA,qBAkBT,SAASC,EAAmB,CAAC,MAAApB,CAAK,EAAG,CACnC,IAAM8B,EAAI9B,EAAM,OACZ+B,EAAU,IAAI,IAAI/B,CAAK,EACvBgC,EAAO,IAAI,IACXC,EAAI,EACR,KAAOF,EAAQ,MAAM,CACnB,QAAWjC,KAAQiC,EAAS,CAC1BjC,EAAK,OAASmC,EACd,OAAW,CAAC,OAAAR,CAAM,IAAK3B,EAAK,YAC1BkC,EAAK,IAAIP,CAAM,CAEnB,CACA,GAAI,EAAEQ,EAAIH,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACb,CACF,CAhBS7C,EAAAiC,EAAA,sBAkBT,SAASc,EAAkB,CAAC,MAAAlC,CAAK,EAAG,CAClC,IAAMiC,EAAIE,EAAInC,EAAOV,GAAKA,EAAE,KAAK,EAAI,EAC/B8C,GAAM9B,EAAKD,EAAKE,IAAO0B,EAAI,GAC3BI,EAAU,IAAI,MAAMJ,CAAC,EAC3B,QAAWnC,KAAQE,EAAO,CACxB,IAAMwB,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIS,EAAI,EAAG,KAAK,MAAMvB,EAAM,KAAK,KAAMZ,EAAMmC,CAAC,CAAC,CAAC,CAAC,EAC5EnC,EAAK,MAAQ0B,EACb1B,EAAK,GAAKO,EAAKmB,EAAIY,EACnBtC,EAAK,GAAKA,EAAK,GAAKS,EAChB8B,EAAQb,CAAC,EAAGa,EAAQb,CAAC,EAAE,KAAK1B,CAAI,EAC/BuC,EAAQb,CAAC,EAAI,CAAC1B,CAAI,CACzB,CACA,GAAIc,EAAM,QAAW0B,KAAUD,EAC7BC,EAAO,KAAK1B,CAAI,EAElB,OAAOyB,CACT,CAhBSlD,EAAA+C,EAAA,qBAkBT,SAASK,EAAuBF,EAAS,CACvC,IAAMG,EAAKC,EAAIJ,EAASK,IAAMxC,EAAKD,GAAMyC,EAAE,OAAS,GAAKjC,GAAMoB,EAAIa,EAAGrD,EAAK,CAAC,EAC5E,QAAWW,KAASqC,EAAS,CAC3B,IAAIM,EAAI1C,EACR,QAAWH,KAAQE,EAAO,CACxBF,EAAK,GAAK6C,EACV7C,EAAK,GAAK6C,EAAI7C,EAAK,MAAQ0C,EAC3BG,EAAI7C,EAAK,GAAKW,EACd,QAAWN,KAAQL,EAAK,YACtBK,EAAK,MAAQA,EAAK,MAAQqC,CAE9B,CACAG,GAAKzC,EAAKyC,EAAIlC,IAAOT,EAAM,OAAS,GACpC,QAASwB,EAAI,EAAGA,EAAIxB,EAAM,OAAQ,EAAEwB,EAAG,CACrC,IAAM1B,EAAOE,EAAMwB,CAAC,EACpB1B,EAAK,IAAM6C,GAAKnB,EAAI,GACpB1B,EAAK,IAAM6C,GAAKnB,EAAI,EACtB,CACAoB,GAAa5C,CAAK,CACpB,CACF,CApBSb,EAAAoD,EAAA,0BAsBT,SAASlB,EAAoB5B,EAAO,CAClC,IAAM4C,EAAUH,EAAkBzC,CAAK,EACvCgB,EAAK,KAAK,IAAID,GAAKN,EAAKD,IAAOkC,EAAIE,EAASK,GAAKA,EAAE,MAAM,EAAI,EAAE,EAC/DH,EAAuBF,CAAO,EAC9B,QAASb,EAAI,EAAGA,EAAIT,EAAY,EAAES,EAAG,CACnC,IAAMqB,EAAQ,KAAK,IAAI,IAAMrB,CAAC,EACxBsB,EAAO,KAAK,IAAI,EAAID,GAAQrB,EAAI,GAAKT,CAAU,EACrDgC,EAAiBV,EAASQ,EAAOC,CAAI,EACrCE,EAAiBX,EAASQ,EAAOC,CAAI,CACvC,CACF,CAVS3D,EAAAkC,EAAA,uBAaT,SAAS2B,EAAiBX,EAASQ,EAAOC,EAAM,CAC9C,QAAStB,EAAI,EAAGM,EAAIO,EAAQ,OAAQb,EAAIM,EAAG,EAAEN,EAAG,CAC9C,IAAMc,EAASD,EAAQb,CAAC,EACxB,QAAWE,KAAUY,EAAQ,CAC3B,IAAIK,EAAI,EACJM,EAAI,EACR,OAAW,CAAC,OAAAxB,EAAQ,MAAApC,EAAK,IAAKqC,EAAO,YAAa,CAChD,IAAIwB,EAAI7D,IAASqC,EAAO,MAAQD,EAAO,OACvCkB,GAAKQ,EAAU1B,EAAQC,CAAM,EAAIwB,EACjCD,GAAKC,CACP,CACA,GAAI,EAAED,EAAI,GAAI,SACd,IAAIzC,GAAMmC,EAAIM,EAAIvB,EAAO,IAAMmB,EAC/BnB,EAAO,IAAMlB,EACbkB,EAAO,IAAMlB,EACb4C,EAAiB1B,CAAM,CACzB,CACId,IAAS,QAAW0B,EAAO,KAAKpD,EAAgB,EACpDmE,EAAkBf,EAAQQ,CAAI,CAChC,CACF,CApBS3D,EAAA6D,EAAA,oBAuBT,SAASD,EAAiBV,EAASQ,EAAOC,EAAM,CAC9C,QAAShB,EAAIO,EAAQ,OAAQb,EAAIM,EAAI,EAAGN,GAAK,EAAG,EAAEA,EAAG,CACnD,IAAMc,EAASD,EAAQb,CAAC,EACxB,QAAWC,KAAUa,EAAQ,CAC3B,IAAIK,EAAI,EACJM,EAAI,EACR,OAAW,CAAC,OAAAvB,EAAQ,MAAArC,EAAK,IAAKoC,EAAO,YAAa,CAChD,IAAIyB,EAAI7D,IAASqC,EAAO,MAAQD,EAAO,OACvCkB,GAAKW,EAAU7B,EAAQC,CAAM,EAAIwB,EACjCD,GAAKC,CACP,CACA,GAAI,EAAED,EAAI,GAAI,SACd,IAAIzC,GAAMmC,EAAIM,EAAIxB,EAAO,IAAMoB,EAC/BpB,EAAO,IAAMjB,EACbiB,EAAO,IAAMjB,EACb4C,EAAiB3B,CAAM,CACzB,CACIb,IAAS,QAAW0B,EAAO,KAAKpD,EAAgB,EACpDmE,EAAkBf,EAAQQ,CAAI,CAChC,CACF,CApBS3D,EAAA4D,EAAA,oBAsBT,SAASM,EAAkBrD,EAAO6C,EAAO,CACvC,IAAMrB,EAAIxB,EAAM,QAAU,EACpBuD,EAAUvD,EAAMwB,CAAC,EACvBgC,EAA6BxD,EAAOuD,EAAQ,GAAK9C,EAAIe,EAAI,EAAGqB,CAAK,EACjEY,EAA6BzD,EAAOuD,EAAQ,GAAK9C,EAAIe,EAAI,EAAGqB,CAAK,EACjEW,EAA6BxD,EAAOE,EAAIF,EAAM,OAAS,EAAG6C,CAAK,EAC/DY,EAA6BzD,EAAOC,EAAI,EAAG4C,CAAK,CAClD,CAPS1D,EAAAkE,EAAA,qBAUT,SAASI,EAA6BzD,EAAO2C,EAAGnB,EAAGqB,EAAO,CACxD,KAAOrB,EAAIxB,EAAM,OAAQ,EAAEwB,EAAG,CAC5B,IAAM1B,EAAOE,EAAMwB,CAAC,EACdhB,GAAMmC,EAAI7C,EAAK,IAAM+C,EACvBrC,EAAK,OAAMV,EAAK,IAAMU,EAAIV,EAAK,IAAMU,GACzCmC,EAAI7C,EAAK,GAAKW,CAChB,CACF,CAPStB,EAAAsE,EAAA,gCAUT,SAASD,EAA6BxD,EAAO2C,EAAGnB,EAAGqB,EAAO,CACxD,KAAOrB,GAAK,EAAG,EAAEA,EAAG,CAClB,IAAM1B,EAAOE,EAAMwB,CAAC,EACdhB,GAAMV,EAAK,GAAK6C,GAAKE,EACvBrC,EAAK,OAAMV,EAAK,IAAMU,EAAIV,EAAK,IAAMU,GACzCmC,EAAI7C,EAAK,GAAKW,CAChB,CACF,CAPStB,EAAAqE,EAAA,gCAST,SAASJ,EAAiB,CAAC,YAAAzB,EAAa,YAAAC,CAAW,EAAG,CACpD,GAAIf,IAAa,OAAW,CAC1B,OAAW,CAAC,OAAQ,CAAC,YAAAc,CAAW,CAAC,IAAKC,EACpCD,EAAY,KAAKvC,EAAsB,EAEzC,OAAW,CAAC,OAAQ,CAAC,YAAAwC,CAAW,CAAC,IAAKD,EACpCC,EAAY,KAAK7C,EAAsB,CAE3C,CACF,CATSI,EAAAiE,EAAA,oBAWT,SAASR,GAAa5C,EAAO,CAC3B,GAAIa,IAAa,OACf,OAAW,CAAC,YAAAc,EAAa,YAAAC,CAAW,IAAK5B,EACvC2B,EAAY,KAAKvC,EAAsB,EACvCwC,EAAY,KAAK7C,EAAsB,CAG7C,CAPSI,EAAAyD,GAAA,gBAUT,SAASO,EAAU1B,EAAQC,EAAQ,CACjC,IAAIiB,EAAIlB,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKhB,EAAK,EAC3D,OAAW,CAAC,OAAQX,EAAM,MAAA4D,CAAK,IAAKjC,EAAO,YAAa,CACtD,GAAI3B,IAAS4B,EAAQ,MACrBiB,GAAKe,EAAQjD,CACf,CACA,OAAW,CAAC,OAAQX,EAAM,MAAA4D,CAAK,IAAKhC,EAAO,YAAa,CACtD,GAAI5B,IAAS2B,EAAQ,MACrBkB,GAAKe,CACP,CACA,OAAOf,CACT,CAXSxD,EAAAgE,EAAA,aAcT,SAASG,EAAU7B,EAAQC,EAAQ,CACjC,IAAIiB,EAAIjB,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKjB,EAAK,EAC3D,OAAW,CAAC,OAAQX,EAAM,MAAA4D,CAAK,IAAKhC,EAAO,YAAa,CACtD,GAAI5B,IAAS2B,EAAQ,MACrBkB,GAAKe,EAAQjD,CACf,CACA,OAAW,CAAC,OAAQX,EAAM,MAAA4D,CAAK,IAAKjC,EAAO,YAAa,CACtD,GAAI3B,IAAS4B,EAAQ,MACrBiB,GAAKe,CACP,CACA,OAAOf,CACT,CAXS,OAAAxD,EAAAmE,EAAA,aAaFtC,CACT,CA3TwB7B,EAAAiB,GAAA,UCrDxB,IAAIuD,GAAK,KAAK,GACVC,GAAM,EAAID,GACVE,EAAU,KACVC,GAAaF,GAAMC,EAEvB,SAASE,IAAO,CACd,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,EACX,CAJSC,EAAAD,GAAA,QAMT,SAASE,IAAO,CACd,OAAO,IAAIF,EACb,CAFSC,EAAAC,GAAA,QAITF,GAAK,UAAYE,GAAK,UAAY,CAChC,YAAaF,GACb,OAAQC,EAAA,SAASE,EAAGC,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACD,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACC,EAC7E,EAFQ,UAGR,UAAWH,EAAA,UAAW,CAChB,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,GAAK,IAEd,EALW,aAMX,OAAQA,EAAA,SAASE,EAAGC,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,CAACD,GAAK,KAAO,KAAK,IAAM,CAACC,EACvD,EAFQ,UAGR,iBAAkBH,EAAA,SAASI,EAAIC,EAAIH,EAAGC,EAAG,CACvC,KAAK,GAAK,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,KAAK,IAAM,CAACH,GAAK,KAAO,KAAK,IAAM,CAACC,EACnF,EAFkB,oBAGlB,cAAeH,EAAA,SAASI,EAAIC,EAAIC,EAAIC,EAAIL,EAAGC,EAAG,CAC5C,KAAK,GAAK,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,CAACC,EAAM,KAAO,KAAK,IAAM,CAACL,GAAK,KAAO,KAAK,IAAM,CAACC,EAC/G,EAFe,iBAGf,MAAOH,EAAA,SAASI,EAAIC,EAAIC,EAAIC,EAAIC,EAAG,CACjCJ,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAI,CAACA,EAC7C,IAAIC,EAAK,KAAK,IACVC,EAAK,KAAK,IACVC,EAAML,EAAKF,EACXQ,EAAML,EAAKF,EACXQ,EAAMJ,EAAKL,EACXU,EAAMJ,EAAKL,EACXU,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAIN,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAGlD,GAAI,KAAK,MAAQ,KACf,KAAK,GAAK,KAAO,KAAK,IAAMJ,GAAM,KAAO,KAAK,IAAMC,WAI3CU,EAAQlB,EAKd,GAAI,EAAE,KAAK,IAAIiB,EAAMH,EAAMC,EAAMC,CAAG,EAAIhB,IAAY,CAACW,EACxD,KAAK,GAAK,KAAO,KAAK,IAAMJ,GAAM,KAAO,KAAK,IAAMC,OAIjD,CACH,IAAIW,EAAMV,EAAKG,EACXQ,EAAMV,EAAKG,EACXQ,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAM,KAAK,KAAKF,CAAK,EACrBG,EAAM,KAAK,KAAKN,CAAK,EACrBO,EAAId,EAAI,KAAK,KAAKb,GAAK,KAAK,MAAMuB,EAAQH,EAAQI,IAAU,EAAIC,EAAMC,EAAI,GAAK,CAAC,EAChFE,EAAMD,EAAID,EACVG,EAAMF,EAAIF,EAGV,KAAK,IAAIG,EAAM,CAAC,EAAI1B,IACtB,KAAK,GAAK,KAAOO,EAAKmB,EAAMV,GAAO,KAAOR,EAAKkB,EAAMT,IAGvD,KAAK,GAAK,IAAMN,EAAI,IAAMA,EAAI,SAAW,EAAEM,EAAME,EAAMH,EAAMI,GAAQ,KAAO,KAAK,IAAMb,EAAKoB,EAAMb,GAAO,KAAO,KAAK,IAAMN,EAAKmB,EAAMZ,EACxI,CACF,EA/CO,SAgDP,IAAKZ,EAAA,SAASE,EAAGC,EAAGK,EAAGiB,EAAIC,EAAIC,EAAK,CAClCzB,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAGK,EAAI,CAACA,EAAGmB,EAAM,CAAC,CAACA,EAChC,IAAIC,EAAKpB,EAAI,KAAK,IAAIiB,CAAE,EACpBI,EAAKrB,EAAI,KAAK,IAAIiB,CAAE,EACpBhB,EAAKP,EAAI0B,EACTlB,EAAKP,EAAI0B,EACTC,EAAK,EAAIH,EACTI,EAAKJ,EAAMF,EAAKC,EAAKA,EAAKD,EAG9B,GAAIjB,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAG9C,KAAK,MAAQ,KACf,KAAK,GAAK,IAAMC,EAAK,IAAMC,GAIpB,KAAK,IAAI,KAAK,IAAMD,CAAE,EAAIZ,GAAW,KAAK,IAAI,KAAK,IAAMa,CAAE,EAAIb,KACtE,KAAK,GAAK,IAAMY,EAAK,IAAMC,GAIxBF,IAGDuB,EAAK,IAAGA,EAAKA,EAAKnC,GAAMA,IAGxBmC,EAAKjC,GACP,KAAK,GAAK,IAAMU,EAAI,IAAMA,EAAI,QAAUsB,EAAK,KAAO5B,EAAI0B,GAAM,KAAOzB,EAAI0B,GAAM,IAAMrB,EAAI,IAAMA,EAAI,QAAUsB,EAAK,KAAO,KAAK,IAAMrB,GAAM,KAAO,KAAK,IAAMC,GAIrJqB,EAAKlC,IACZ,KAAK,GAAK,IAAMW,EAAI,IAAMA,EAAI,OAAS,EAAEuB,GAAMpC,IAAO,IAAMmC,EAAK,KAAO,KAAK,IAAM5B,EAAIM,EAAI,KAAK,IAAIkB,CAAE,GAAK,KAAO,KAAK,IAAMvB,EAAIK,EAAI,KAAK,IAAIkB,CAAE,IAEpJ,EArCK,OAsCL,KAAM1B,EAAA,SAASE,EAAGC,EAAG6B,EAAGC,EAAG,CACzB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAAC/B,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACC,GAAK,KAAO,CAAC6B,EAAK,KAAO,CAACC,EAAK,IAAO,CAACD,EAAK,GACzH,EAFM,QAGN,SAAUhC,EAAA,UAAW,CACnB,OAAO,KAAK,CACd,EAFU,WAGZ,EAEA,IAAOkC,GAAQjC,GCjIA,SAARkC,GAAiBC,EAAG,CACzB,OAAOC,EAAA,UAAoB,CACzB,OAAOD,CACT,EAFO,WAGT,CAJOC,EAAAF,GAAA,WCAA,SAASG,GAAEC,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAFgBC,EAAAF,GAAA,KAIT,SAASG,GAAEF,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAFgBC,EAAAC,GAAA,KCJT,IAAIC,GAAQ,MAAM,UAAU,MCMnC,SAASC,GAAWC,EAAG,CACrB,OAAOA,EAAE,MACX,CAFSC,EAAAF,GAAA,cAIT,SAASG,GAAWF,EAAG,CACrB,OAAOA,EAAE,MACX,CAFSC,EAAAC,GAAA,cAIT,SAASC,GAAKC,EAAO,CACnB,IAAIC,EAASN,GACTO,EAASJ,GACTK,EAAIA,GACJC,EAAIA,GACJC,EAAU,KAEd,SAASN,GAAO,CACd,IAAIO,EAAQC,EAAOC,GAAM,KAAK,SAAS,EAAGC,EAAIR,EAAO,MAAM,KAAMM,CAAI,EAAGG,EAAIR,EAAO,MAAM,KAAMK,CAAI,EAGnG,GAFKF,IAASA,EAAUC,EAASK,GAAK,GACtCX,EAAMK,EAAS,CAACF,EAAE,MAAM,MAAOI,EAAK,CAAC,EAAIE,EAAGF,EAAK,EAAG,CAACH,EAAE,MAAM,KAAMG,CAAI,EAAG,CAACJ,EAAE,MAAM,MAAOI,EAAK,CAAC,EAAIG,EAAGH,EAAK,EAAG,CAACH,EAAE,MAAM,KAAMG,CAAI,CAAC,EAC/HD,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACpD,CALS,OAAAT,EAAAE,EAAA,QAOTA,EAAK,OAAS,SAASa,EAAG,CACxB,OAAO,UAAU,QAAUX,EAASW,EAAGb,GAAQE,CACjD,EAEAF,EAAK,OAAS,SAASa,EAAG,CACxB,OAAO,UAAU,QAAUV,EAASU,EAAGb,GAAQG,CACjD,EAEAH,EAAK,EAAI,SAASa,EAAG,CACnB,OAAO,UAAU,QAAUT,EAAI,OAAOS,GAAM,WAAaA,EAAIC,GAAS,CAACD,CAAC,EAAGb,GAAQI,CACrF,EAEAJ,EAAK,EAAI,SAASa,EAAG,CACnB,OAAO,UAAU,QAAUR,EAAI,OAAOQ,GAAM,WAAaA,EAAIC,GAAS,CAACD,CAAC,EAAGb,GAAQK,CACrF,EAEAL,EAAK,QAAU,SAASa,EAAG,CACzB,OAAO,UAAU,QAAWP,EAAUO,GAAY,KAAWb,GAAQM,CACvE,EAEON,CACT,CAnCSF,EAAAE,GAAA,QAqCT,SAASe,GAAgBT,EAASU,EAAIC,EAAIC,EAAIC,EAAI,CAChDb,EAAQ,OAAOU,EAAIC,CAAE,EACrBX,EAAQ,cAAcU,GAAMA,EAAKE,GAAM,EAAGD,EAAID,EAAIG,EAAID,EAAIC,CAAE,CAC9D,CAHSrB,EAAAiB,GAAA,mBAmBF,SAASK,IAAiB,CAC/B,OAAOC,GAAKC,EAAe,CAC7B,CAFgBC,EAAAH,GAAA,kBCpEhB,SAASI,GAAiBC,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAFSC,EAAAF,GAAA,oBAIT,SAASG,GAAiBF,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAFSC,EAAAC,GAAA,oBAIM,SAARC,IAAmB,CACxB,OAAOC,GAAe,EACjB,OAAOL,EAAgB,EACvB,OAAOG,EAAgB,CAC9B,CAJOD,EAAAE,GAAA,WCVA,IAAME,EAAN,MAAMC,CAAI,CAAjB,MAAiB,CAAAC,EAAA,YACf,YAAe,MAAQ,EAIvB,OAAc,KAAKC,EAAmB,CACpC,OAAO,IAAIF,EAAIE,GAAO,EAAEF,EAAI,KAAK,CACnC,CAEA,YAAYG,EAAY,CACtB,KAAK,GAAKA,EACV,KAAK,KAAO,IAAIA,CAAE,EACpB,CAEA,UAAmB,CACjB,MAAO,OAAS,KAAK,KAAO,GAC9B,CACF,ECIA,IAAMC,GAGF,CACF,KAAMC,GACN,MAAOC,GACP,OAAQC,GACR,QAASC,CACX,EAUaC,GAAOC,EAAA,SAAUC,EAAcC,EAAYC,EAAkBC,EAAwB,CAEhG,GAAM,CAAE,cAAAC,EAAe,OAAQC,CAAK,EAAIC,EAAU,EAC5CC,EAAsBC,GAAc,OAOtCC,EACAL,IAAkB,YACpBK,EAAiBC,EAAS,KAAOT,CAAE,GAErC,IAAMU,EACJP,IAAkB,UACdM,EAASD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACvDC,EAAS,MAAM,EAEfE,EAAMR,IAAkB,UAAYO,EAAK,OAAO,QAAQV,CAAE,IAAI,EAAIS,EAAS,QAAQT,CAAE,IAAI,EAIzFY,EAAQR,GAAM,OAASE,EAAoB,MAC3CO,EAAST,GAAM,QAAUE,EAAoB,MAC7CQ,EAAcV,GAAM,aAAeE,EAAoB,YACvDS,EAAgBX,GAAM,eAAiBE,EAAoB,cAC3DU,EAASZ,GAAM,QAAUE,EAAoB,OAC7CW,EAASb,GAAM,QAAUE,EAAoB,OAC7CY,EAAad,GAAM,YAAcE,EAAoB,WAWrDa,EAAQjB,EAAQ,GAAG,SAAS,EAG5BkB,EAAY5B,GAAcuB,CAAa,EAM9BM,GAAS,EACrB,OAAQC,GAAWA,EAAE,EAAE,EACvB,UAHe,EAGI,EACnB,YAAY,IAAMJ,EAAa,GAAK,EAAE,EACtC,UAAUE,CAAS,EACnB,OAAO,CACN,CAAC,EAAG,CAAC,EACL,CAACR,EAAOC,CAAM,CAChB,CAAC,EAKIM,CAAK,EAGZ,IAAMI,EAAcC,GAAeC,EAAiB,EAGpDd,EACG,OAAO,GAAG,EACV,KAAK,QAAS,OAAO,EACrB,UAAU,OAAO,EACjB,KAAKQ,EAAM,KAAK,EAChB,KAAK,GAAG,EACR,KAAK,QAAS,MAAM,EACpB,KAAK,KAAOG,IAAYA,EAAE,IAAMI,EAAI,KAAK,OAAO,GAAG,EAAE,EACrD,KAAK,YAAa,SAAUJ,EAAQ,CACnC,MAAO,aAAeA,EAAE,GAAK,IAAMA,EAAE,GAAK,GAC5C,CAAC,EACA,KAAK,IAAMA,GAAWA,EAAE,EAAE,EAC1B,KAAK,IAAMA,GAAWA,EAAE,EAAE,EAC1B,OAAO,MAAM,EACb,KAAK,SAAWA,GACRA,EAAE,GAAKA,EAAE,EACjB,EACA,KAAK,QAAUA,GAAWA,EAAE,GAAKA,EAAE,EAAE,EACrC,KAAK,OAASA,GAAWC,EAAYD,EAAE,EAAE,CAAC,EAE7C,IAAMK,EAAU7B,EAAA,CAAC,CAAE,GAAAE,EAAI,MAAA4B,CAAM,IACtBV,EAGE,GAAGlB,CAAE;AAAA,EAAKgB,CAAM,GAAG,KAAK,MAAMY,EAAQ,GAAG,EAAI,GAAG,GAAGX,CAAM,GAFvDjB,EAFK,WAQhBW,EACG,OAAO,GAAG,EACV,KAAK,QAAS,aAAa,EAC3B,KAAK,YAAa,EAAE,EACpB,UAAU,MAAM,EAChB,KAAKQ,EAAM,KAAK,EAChB,KAAK,MAAM,EACX,KAAK,IAAMG,GAAYA,EAAE,GAAKV,EAAQ,EAAIU,EAAE,GAAK,EAAIA,EAAE,GAAK,CAAE,EAC9D,KAAK,IAAMA,IAAYA,EAAE,GAAKA,EAAE,IAAM,CAAC,EACvC,KAAK,KAAM,GAAGJ,EAAa,IAAM,MAAM,IAAI,EAC3C,KAAK,cAAgBI,GAAYA,EAAE,GAAKV,EAAQ,EAAI,QAAU,KAAM,EACpE,KAAKe,CAAO,EAGf,IAAME,EAAOlB,EACV,OAAO,GAAG,EACV,KAAK,QAAS,OAAO,EACrB,KAAK,OAAQ,MAAM,EACnB,KAAK,iBAAkB,EAAG,EAC1B,UAAU,OAAO,EACjB,KAAKQ,EAAM,KAAK,EAChB,KAAK,GAAG,EACR,KAAK,QAAS,MAAM,EACpB,MAAM,iBAAkB,UAAU,EAE/BW,EAAY1B,GAAM,WAAa,WAErC,GAAI0B,IAAc,WAAY,CAC5B,IAAMC,EAAWF,EACd,OAAO,gBAAgB,EACvB,KAAK,KAAOP,IAAYA,EAAE,IAAMI,EAAI,KAAK,iBAAiB,GAAG,EAAE,EAC/D,KAAK,gBAAiB,gBAAgB,EACtC,KAAK,KAAOJ,GAAWA,EAAE,OAAO,EAAE,EAClC,KAAK,KAAOA,GAAWA,EAAE,OAAO,EAAE,EAErCS,EACG,OAAO,MAAM,EACb,KAAK,SAAU,IAAI,EACnB,KAAK,aAAeT,GAAWC,EAAYD,EAAE,OAAO,EAAE,CAAC,EAE1DS,EACG,OAAO,MAAM,EACb,KAAK,SAAU,MAAM,EACrB,KAAK,aAAeT,GAAWC,EAAYD,EAAE,OAAO,EAAE,CAAC,CAC5D,CAEA,IAAIU,EACJ,OAAQF,EAAW,CACjB,IAAK,WACHE,EAAWlC,EAACwB,GAAWA,EAAE,IAAd,YACX,MACF,IAAK,SACHU,EAAWlC,EAACwB,GAAWC,EAAYD,EAAE,OAAO,EAAE,EAAnC,YACX,MACF,IAAK,SACHU,EAAWlC,EAACwB,GAAWC,EAAYD,EAAE,OAAO,EAAE,EAAnC,YACX,MACF,QACEU,EAAWF,CACf,CAEAD,EACG,OAAO,MAAM,EACb,KAAK,IAAKI,GAAuB,CAAC,EAClC,KAAK,SAAUD,CAAQ,EACvB,KAAK,eAAiBV,GAAW,KAAK,IAAI,EAAGA,EAAE,KAAK,CAAC,EAExDY,GAAkB,OAAWvB,EAAK,EAAGG,CAAW,CAClD,EApKoB,QAsKbqB,GAAQ,CACb,KAAAtC,EACF,EC/MO,IAAMuC,GAAwBC,EAACC,GAChBA,EACjB,WAAW,2BAA4B,EAAE,EACzC,WAAW,aAAc;AAAA,CAAI,EAC7B,KAAK,EAJ2B,yBCArC,IAAMC,GAAYC,EAACC,GACjB;AAAA,qBACmBA,EAAQ,UAAU;AAAA,OAFrB,aAKXC,GAAQH,GCGf,IAAMI,GAAgBC,EAAO,MAAM,KAAKA,CAAM,EAC9CA,EAAO,MAASC,GAAiBF,GAAcG,GAAsBD,CAAI,CAAC,EAEnE,IAAME,GAA6B,CACxC,OAAQC,GACR,OAAAJ,EACA,GAAAK,GACA,SAAAC,EACF", "names": ["parser", "o", "__name", "k", "v", "$V0", "$V1", "$V2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "source", "target", "value", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "sankey_default", "parser", "links", "nodes", "nodesMap", "clear", "__name", "SankeyLink", "source", "target", "value", "addLink", "SankeyNode", "ID", "findOrCreateNode", "common_default", "getConfig", "node", "getNodes", "getLinks", "getGraph", "link", "sankeyDB_default", "getAccTitle", "setAccTitle", "getAccDescription", "setAccDescription", "getDiagramTitle", "setDiagramTitle", "max", "values", "valueof", "value", "index", "__name", "min", "values", "valueof", "value", "index", "__name", "sum", "values", "valueof", "value", "index", "__name", "targetDepth", "d", "__name", "left", "node", "right", "n", "justify", "center", "min", "constant", "x", "__name", "ascendingSourceBreadth", "a", "b", "ascendingBreadth", "__name", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "d", "defaultId", "defaultNodes", "graph", "defaultLinks", "find", "nodeById", "id", "node", "computeLinkBreadths", "nodes", "y0", "y1", "link", "<PERSON><PERSON>", "x0", "x1", "dx", "dy", "py", "align", "justify", "sort", "linkSort", "links", "iterations", "sankey", "computeNodeLinks", "computeNodeValues", "computeNodeDepths", "computeNodeHeights", "computeNodeBreadths", "_", "constant", "i", "source", "target", "sourceLinks", "targetLinks", "sum", "n", "current", "next", "x", "computeNodeLayers", "max", "kx", "columns", "column", "initializeNodeBreadths", "ky", "min", "c", "y", "reorderLinks", "alpha", "beta", "relaxRightToLeft", "relaxLeftToRight", "w", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom", "width", "pi", "tau", "epsilon", "tauEpsilon", "Path", "__name", "path", "x", "y", "x1", "y1", "x2", "y2", "r", "x0", "y0", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "l", "t01", "t21", "a0", "a1", "ccw", "dx", "dy", "cw", "da", "w", "h", "path_default", "constant_default", "x", "__name", "x", "p", "__name", "y", "slice", "linkSource", "d", "__name", "linkTarget", "link", "curve", "source", "target", "x", "y", "context", "buffer", "argv", "slice", "s", "t", "path_default", "_", "constant_default", "curveHorizontal", "x0", "y0", "x1", "y1", "linkHorizontal", "link", "curveHorizontal", "__name", "horizontalSource", "d", "__name", "horizontalTarget", "sankeyLinkHorizontal_default", "linkHorizontal", "<PERSON><PERSON>", "_Uid", "__name", "name", "id", "alignmentsMap", "left", "right", "center", "justify", "draw", "__name", "text", "id", "_version", "diagObj", "securityLevel", "conf", "getConfig", "defaultSankeyConfig", "defaultConfig", "sandboxElement", "select_default", "root", "svg", "width", "height", "useMaxWidth", "nodeAlignment", "prefix", "suffix", "showValues", "graph", "nodeAlign", "<PERSON><PERSON>", "d", "colorScheme", "ordinal", "Tableau10_default", "<PERSON><PERSON>", "getText", "value", "link", "linkColor", "gradient", "coloring", "sankeyLinkHorizontal_default", "setupGraphViewbox", "sankeyRenderer_default", "prepareTextForParsing", "__name", "text", "getStyles", "__name", "options", "styles_default", "originalParse", "sankey_default", "text", "prepareTextForParsing", "diagram", "styles_default", "sankeyDB_default", "sankeyRenderer_default"]}