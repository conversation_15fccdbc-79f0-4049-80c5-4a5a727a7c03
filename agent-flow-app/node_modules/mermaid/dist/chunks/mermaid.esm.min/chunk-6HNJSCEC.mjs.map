{"version": 3, "sources": ["../../../src/internals.ts", "../../../src/rendering-util/render.ts"], "sourcesContent": ["import { getConfig } from './config.js';\nimport common from './diagrams/common/common.js';\nimport { log } from './logger.js';\nimport { insertCluster } from './rendering-util/rendering-elements/clusters.js';\nimport {\n  insertEdge,\n  insertEdgeLabel,\n  positionEdgeLabel,\n} from './rendering-util/rendering-elements/edges.js';\nimport insertMarkers from './rendering-util/rendering-elements/markers.js';\nimport { insertNode } from './rendering-util/rendering-elements/nodes.js';\nimport { labelHelper } from './rendering-util/rendering-elements/shapes/util.js';\nimport { interpolateToCurve } from './utils.js';\n\n/**\n * Internal helpers for mermaid\n * @deprecated - This should not be used by external packages, as the definitions will change without notice.\n */\nexport const internalHelpers = {\n  common,\n  getConfig,\n  insertCluster,\n  insertEdge,\n  insertEdgeLabel,\n  insertMarkers,\n  insertNode,\n  interpolateToCurve,\n  labelHelper,\n  log,\n  positionEdgeLabel,\n};\n\nexport type InternalHelpers = typeof internalHelpers;\n", "import type { SVG } from '../diagram-api/types.js';\nimport type { InternalHelpers } from '../internals.js';\nimport { internalHelpers } from '../internals.js';\nimport { log } from '../logger.js';\nimport type { LayoutData } from './types.js';\n\nexport interface RenderOptions {\n  algorithm?: string;\n}\n\nexport interface LayoutAlgorithm {\n  render(\n    layoutData: LayoutData,\n    svg: SVG,\n    helpers: InternalHelpers,\n    options?: RenderOptions\n  ): Promise<void>;\n}\n\nexport type LayoutLoader = () => Promise<LayoutAlgorithm>;\nexport interface LayoutLoaderDefinition {\n  name: string;\n  loader: LayoutLoader;\n  algorithm?: string;\n}\n\nconst layoutAlgorithms: Record<string, LayoutLoaderDefinition> = {};\n\nexport const registerLayoutLoaders = (loaders: LayoutLoaderDefinition[]) => {\n  for (const loader of loaders) {\n    layoutAlgorithms[loader.name] = loader;\n  }\n};\n\n// TODO: Should we load dagre without lazy loading?\nconst registerDefaultLayoutLoaders = () => {\n  registerLayoutLoaders([\n    {\n      name: 'dagre',\n      loader: async () => await import('./layout-algorithms/dagre/index.js'),\n    },\n  ]);\n};\n\nregisterDefaultLayoutLoaders();\n\nexport const render = async (data4Layout: LayoutData, svg: SVG) => {\n  if (!(data4Layout.layoutAlgorithm in layoutAlgorithms)) {\n    throw new Error(`Unknown layout algorithm: ${data4Layout.layoutAlgorithm}`);\n  }\n\n  const layoutDefinition = layoutAlgorithms[data4Layout.layoutAlgorithm];\n  const layoutRenderer = await layoutDefinition.loader();\n  return layoutRenderer.render(data4Layout, svg, internalHelpers, {\n    algorithm: layoutDefinition.algorithm,\n  });\n};\n\n/**\n * Get the registered layout algorithm. If the algorithm is not registered, use the fallback algorithm.\n */\nexport const getRegisteredLayoutAlgorithm = (algorithm = '', { fallback = 'dagre' } = {}) => {\n  if (algorithm in layoutAlgorithms) {\n    return algorithm;\n  }\n  if (fallback in layoutAlgorithms) {\n    log.warn(`Layout algorithm ${algorithm} is not registered. Using ${fallback} as fallback.`);\n    return fallback;\n  }\n  throw new Error(`Both layout algorithms ${algorithm} and ${fallback} are not registered.`);\n};\n"], "mappings": "yPAkBO,IAAMA,EAAkB,CAC7B,OAAAC,EACA,UAAAC,EACA,cAAAC,EACA,WAAAC,EACA,gBAAAC,EACA,cAAAC,EACA,WAAAC,EACA,mBAAAC,EACA,YAAAC,EACA,IAAAC,EACA,kBAAAC,CACF,ECJA,IAAMC,EAA2D,CAAC,EAErDC,EAAwBC,EAACC,GAAsC,CAC1E,QAAWC,KAAUD,EACnBH,EAAiBI,EAAO,IAAI,EAAIA,CAEpC,EAJqC,yBAO/BC,EAA+BH,EAAA,IAAM,CACzCD,EAAsB,CACpB,CACE,KAAM,QACN,OAAQC,EAAA,SAAY,KAAM,QAAO,sBAAoC,EAA7D,SACV,CACF,CAAC,CACH,EAPqC,gCASrCG,EAA6B,EAEtB,IAAMC,EAASJ,EAAA,MAAOK,EAAyBC,IAAa,CACjE,GAAI,EAAED,EAAY,mBAAmBP,GACnC,MAAM,IAAI,MAAM,6BAA6BO,EAAY,eAAe,EAAE,EAG5E,IAAME,EAAmBT,EAAiBO,EAAY,eAAe,EAErE,OADuB,MAAME,EAAiB,OAAO,GAC/B,OAAOF,EAAaC,EAAKE,EAAiB,CAC9D,UAAWD,EAAiB,SAC9B,CAAC,CACH,EAVsB,UAeTE,EAA+BT,EAAA,CAACU,EAAY,GAAI,CAAE,SAAAC,EAAW,OAAQ,EAAI,CAAC,IAAM,CAC3F,GAAID,KAAaZ,EACf,OAAOY,EAET,GAAIC,KAAYb,EACd,OAAAc,EAAI,KAAK,oBAAoBF,CAAS,6BAA6BC,CAAQ,eAAe,EACnFA,EAET,MAAM,IAAI,MAAM,0BAA0BD,CAAS,QAAQC,CAAQ,sBAAsB,CAC3F,EAT4C", "names": ["internalHelpers", "common_default", "getConfig", "insertCluster", "insertEdge", "insertEdgeLabel", "markers_default", "insertNode", "interpolateToCurve", "labelHelper", "log", "positionEdgeLabel", "layoutAlgorithms", "registerLayoutLoaders", "__name", "loaders", "loader", "registerDefaultLayoutLoaders", "render", "data4Layout", "svg", "layoutDefinition", "internalHelpers", "getRegisteredLayoutAlgorithm", "algorithm", "fallback", "log"]}