import{a as o,b as n,c as a,d as u,e as p,f as e,g as d,l,p as s,q as G}from"./chunk-QFDCAKKT.mjs";import{a as t}from"./chunk-GTKDMUJJ.mjs";var h=class extends G{static{t(this,"GitGraphTokenBuilder")}static{e(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}},m={parser:{TokenBuilder:e(()=>new h,"TokenBuilder"),ValueConverter:e(()=>new s,"ValueConverter")}};function v(c=u){let r=a(n(c),d),i=a(o({shared:r}),l,m);return r.ServiceRegistry.register(i),{shared:r,GitGraph:i}}t(v,"createGitGraphServices");e(v,"createGitGraphServices");export{m as a,v as b};
