import{o as Ee}from"./chunk-YR5264OA.mjs";import{$ as ze,J as te,L as Fe,M as ve,Y as Ae,b as z,ha as G}from"./chunk-U6SPV2NK.mjs";import{a,b as ee,e as kt}from"./chunk-GTKDMUJJ.mjs";var Oe=ee((ar,Be)=>{"use strict";var j=1e3,D=j*60,_=D*60,E=_*24,Ft=E*7,vt=E*365.25;Be.exports=function(t,e){e=e||{};var n=typeof t;if(n==="string"&&t.length>0)return At(t);if(n==="number"&&isFinite(t))return e.long?Et(t):zt(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function At(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var n=parseFloat(e[1]),s=(e[2]||"ms").toLowerCase();switch(s){case"years":case"year":case"yrs":case"yr":case"y":return n*vt;case"weeks":case"week":case"w":return n*Ft;case"days":case"day":case"d":return n*E;case"hours":case"hour":case"hrs":case"hr":case"h":return n*_;case"minutes":case"minute":case"mins":case"min":case"m":return n*D;case"seconds":case"second":case"secs":case"sec":case"s":return n*j;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}a(At,"parse");function zt(t){var e=Math.abs(t);return e>=E?Math.round(t/E)+"d":e>=_?Math.round(t/_)+"h":e>=D?Math.round(t/D)+"m":e>=j?Math.round(t/j)+"s":t+"ms"}a(zt,"fmtShort");function Et(t){var e=Math.abs(t);return e>=E?Z(t,e,E,"day"):e>=_?Z(t,e,_,"hour"):e>=D?Z(t,e,D,"minute"):e>=j?Z(t,e,j,"second"):t+" ms"}a(Et,"fmtLong");function Z(t,e,n,s){var r=e>=n*1.5;return Math.round(t/n)+" "+s+(r?"s":"")}a(Z,"plural")});var We=ee((pr,qe)=>{"use strict";function Lt(t){n.debug=n,n.default=n,n.coerce=c,n.disable=o,n.enable=r,n.enabled=l,n.humanize=Oe(),n.destroy=p,Object.keys(t).forEach(u=>{n[u]=t[u]}),n.names=[],n.skips=[],n.formatters={};function e(u){let f=0;for(let h=0;h<u.length;h++)f=(f<<5)-f+u.charCodeAt(h),f|=0;return n.colors[Math.abs(f)%n.colors.length]}a(e,"selectColor"),n.selectColor=e;function n(u){let f,h=null,g,d;function m(...b){if(!m.enabled)return;let C=m,I=Number(new Date),S=I-(f||I);C.diff=S,C.prev=f,C.curr=I,f=I,b[0]=n.coerce(b[0]),typeof b[0]!="string"&&b.unshift("%O");let w=0;b[0]=b[0].replace(/%([a-zA-Z%])/g,(v,A)=>{if(v==="%%")return"%";w++;let Re=n.formatters[A];if(typeof Re=="function"){let mt=b[w];v=Re.call(C,mt),b.splice(w,1),w--}return v}),n.formatArgs.call(C,b),(C.log||n.log).apply(C,b)}return a(m,"debug"),m.namespace=u,m.useColors=n.useColors(),m.color=n.selectColor(u),m.extend=s,m.destroy=n.destroy,Object.defineProperty(m,"enabled",{enumerable:!0,configurable:!1,get:a(()=>h!==null?h:(g!==n.namespaces&&(g=n.namespaces,d=n.enabled(u)),d),"get"),set:a(b=>{h=b},"set")}),typeof n.init=="function"&&n.init(m),m}a(n,"createDebug");function s(u,f){let h=n(this.namespace+(typeof f>"u"?":":f)+u);return h.log=this.log,h}a(s,"extend");function r(u){n.save(u),n.namespaces=u,n.names=[],n.skips=[];let f=(typeof u=="string"?u:"").trim().replace(" ",",").split(",").filter(Boolean);for(let h of f)h[0]==="-"?n.skips.push(h.slice(1)):n.names.push(h)}a(r,"enable");function i(u,f){let h=0,g=0,d=-1,m=0;for(;h<u.length;)if(g<f.length&&(f[g]===u[h]||f[g]==="*"))f[g]==="*"?(d=g,m=h,g++):(h++,g++);else if(d!==-1)g=d+1,m++,h=m;else return!1;for(;g<f.length&&f[g]==="*";)g++;return g===f.length}a(i,"matchesTemplate");function o(){let u=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),u}a(o,"disable");function l(u){for(let f of n.skips)if(i(u,f))return!1;for(let f of n.names)if(i(u,f))return!0;return!1}a(l,"enabled");function c(u){return u instanceof Error?u.stack||u.message:u}a(c,"coerce");function p(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return a(p,"destroy"),n.enable(n.load()),n}a(Lt,"setup");qe.exports=Lt});var Ge=ee((T,V)=>{"use strict";T.formatArgs=Pt;T.save=jt;T.load=Dt;T.useColors=Mt;T.storage=_t();T.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();T.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Mt(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}a(Mt,"useColors");function Pt(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+V.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,r=>{r!=="%%"&&(n++,r==="%c"&&(s=n))}),t.splice(s,0,e)}a(Pt,"formatArgs");T.log=console.debug||console.log||(()=>{});function jt(t){try{t?T.storage.setItem("debug",t):T.storage.removeItem("debug")}catch{}}a(jt,"save");function Dt(){let t;try{t=T.storage.getItem("debug")}catch{}return!t&&typeof process<"u"&&"env"in process&&(t=process.env.DEBUG),t}a(Dt,"load");function _t(){try{return localStorage}catch{}}a(_t,"localstorage");V.exports=We()(T);var{formatters:Bt}=V.exports;Bt.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var xt=Object.freeze({left:0,top:0,width:16,height:16}),P=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),ne=Object.freeze({...xt,...P}),Le=Object.freeze({...ne,body:"",hidden:!1});var bt=Object.freeze({width:null,height:null}),Me=Object.freeze({...bt,...P});var re=a((t,e,n,s="")=>{let r=t.split(":");if(t.slice(0,1)==="@"){if(r.length<2||r.length>3)return null;s=r.shift().slice(1)}if(r.length>3||!r.length)return null;if(r.length>1){let l=r.pop(),c=r.pop(),p={provider:r.length>0?r[0]:s,prefix:c,name:l};return e&&!N(p)?null:p}let i=r[0],o=i.split("-");if(o.length>1){let l={provider:s,prefix:o.shift(),name:o.join("-")};return e&&!N(l)?null:l}if(n&&s===""){let l={provider:s,prefix:"",name:i};return e&&!N(l,n)?null:l}return null},"stringToIcon"),N=a((t,e)=>t?!!((e&&t.prefix===""||t.prefix)&&t.name):!1,"validateIconName");function Pe(t,e){let n={};!t.hFlip!=!e.hFlip&&(n.hFlip=!0),!t.vFlip!=!e.vFlip&&(n.vFlip=!0);let s=((t.rotate||0)+(e.rotate||0))%4;return s&&(n.rotate=s),n}a(Pe,"mergeIconTransformations");function se(t,e){let n=Pe(t,e);for(let s in Le)s in P?s in t&&!(s in n)&&(n[s]=P[s]):s in e?n[s]=e[s]:s in t&&(n[s]=t[s]);return n}a(se,"mergeIconData");function je(t,e){let n=t.icons,s=t.aliases||Object.create(null),r=Object.create(null);function i(o){if(n[o])return r[o]=[];if(!(o in r)){r[o]=null;let l=s[o]&&s[o].parent,c=l&&i(l);c&&(r[o]=[l].concat(c))}return r[o]}return a(i,"resolve"),(e||Object.keys(n).concat(Object.keys(s))).forEach(i),r}a(je,"getIconsTree");function De(t,e,n){let s=t.icons,r=t.aliases||Object.create(null),i={};function o(l){i=se(s[l]||r[l],i)}return a(o,"parse"),o(e),n.forEach(o),se(t,i)}a(De,"internalGetIconData");function oe(t,e){if(t.icons[e])return De(t,e,[]);let n=je(t,[e])[e];return n?De(t,e,n):null}a(oe,"getIconData");var wt=/(-?[0-9.]*[0-9]+[0-9.]*)/g,yt=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function ie(t,e,n){if(e===1)return t;if(n=n||100,typeof t=="number")return Math.ceil(t*e*n)/n;if(typeof t!="string")return t;let s=t.split(wt);if(s===null||!s.length)return t;let r=[],i=s.shift(),o=yt.test(i);for(;;){if(o){let l=parseFloat(i);isNaN(l)?r.push(i):r.push(Math.ceil(l*e*n)/n)}else r.push(i);if(i=s.shift(),i===void 0)return r.join("");o=!o}}a(ie,"calculateSize");function Ct(t,e="defs"){let n="",s=t.indexOf("<"+e);for(;s>=0;){let r=t.indexOf(">",s),i=t.indexOf("</"+e);if(r===-1||i===-1)break;let o=t.indexOf(">",i);if(o===-1)break;n+=t.slice(r+1,i).trim(),t=t.slice(0,s).trim()+t.slice(o+1)}return{defs:n,content:t}}a(Ct,"splitSVGDefs");function St(t,e){return t?"<defs>"+t+"</defs>"+e:e}a(St,"mergeDefsAndContent");function _e(t,e,n){let s=Ct(t);return St(s.defs,e+s.content+n)}a(_e,"wrapSVGContent");var Tt=a(t=>t==="unset"||t==="undefined"||t==="none","isUnsetKeyword");function le(t,e){let n={...ne,...t},s={...Me,...e},r={left:n.left,top:n.top,width:n.width,height:n.height},i=n.body;[n,s].forEach(m=>{let b=[],C=m.hFlip,I=m.vFlip,S=m.rotate;C?I?S+=2:(b.push("translate("+(r.width+r.left).toString()+" "+(0-r.top).toString()+")"),b.push("scale(-1 1)"),r.top=r.left=0):I&&(b.push("translate("+(0-r.left).toString()+" "+(r.height+r.top).toString()+")"),b.push("scale(1 -1)"),r.top=r.left=0);let w;switch(S<0&&(S-=Math.floor(S/4)*4),S=S%4,S){case 1:w=r.height/2+r.top,b.unshift("rotate(90 "+w.toString()+" "+w.toString()+")");break;case 2:b.unshift("rotate(180 "+(r.width/2+r.left).toString()+" "+(r.height/2+r.top).toString()+")");break;case 3:w=r.width/2+r.left,b.unshift("rotate(-90 "+w.toString()+" "+w.toString()+")");break}S%2===1&&(r.left!==r.top&&(w=r.left,r.left=r.top,r.top=w),r.width!==r.height&&(w=r.width,r.width=r.height,r.height=w)),b.length&&(i=_e(i,'<g transform="'+b.join(" ")+'">',"</g>"))});let o=s.width,l=s.height,c=r.width,p=r.height,u,f;o===null?(f=l===null?"1em":l==="auto"?p:l,u=ie(f,c/p)):(u=o==="auto"?c:o,f=l===null?ie(u,p/c):l==="auto"?p:l);let h={},g=a((m,b)=>{Tt(b)||(h[m]=b.toString())},"setAttr");g("width",u),g("height",f);let d=[r.left,r.top,c,p];return h.viewBox=d.join(" "),{attributes:h,viewBox:d,body:i}}a(le,"iconToSVG");var It=/\sid="(\S+)"/g,$t="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16),Rt=0;function ae(t,e=$t){let n=[],s;for(;s=It.exec(t);)n.push(s[1]);if(!n.length)return t;let r="suffix"+(Math.random()*16777216|Date.now()).toString(16);return n.forEach(i=>{let o=typeof e=="function"?e(i):e+(Rt++).toString(),l=i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+l+')([")]|\\.[a-z])',"g"),"$1"+o+r+"$3")}),t=t.replace(new RegExp(r,"g"),""),t}a(ae,"replaceIDs");function ce(t,e){let n=t.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(let s in e)n+=" "+s+'="'+e[s]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+n+">"+t+"</svg>"}a(ce,"iconToHTML");var gr=kt(Ge(),1);var Ot={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80},pe=new Map,Ne=new Map,Sr=a(t=>{for(let e of t){if(!e.name)throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.');if(z.debug("Registering icon pack:",e.name),"loader"in e)Ne.set(e.name,e.loader);else if("icons"in e)pe.set(e.name,e.icons);else throw z.error("Invalid icon loader:",e),new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}},"registerIconPacks"),Ze=a(async(t,e)=>{let n=re(t,!0,e!==void 0);if(!n)throw new Error(`Invalid icon name: ${t}`);let s=n.prefix||e;if(!s)throw new Error(`Icon name must contain a prefix: ${t}`);let r=pe.get(s);if(!r){let o=Ne.get(s);if(!o)throw new Error(`Icon set not found: ${n.prefix}`);try{r={...await o(),prefix:s},pe.set(s,r)}catch(l){throw z.error(l),new Error(`Failed to load icon set: ${n.prefix}`)}}let i=oe(r,n.name);if(!i)throw new Error(`Icon not found: ${t}`);return i},"getRegisteredIconData"),Ve=a(async t=>{try{return await Ze(t),!0}catch{return!1}},"isIconAvailable"),He=a(async(t,e,n)=>{let s;try{s=await Ze(t,e?.fallbackPrefix)}catch(o){z.error(o),s=Ot}let r=le(s,e);return ce(ae(r.body),{...r.attributes,...n})},"getIconSVG");function Ue(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var s=Array.from(typeof t=="string"?[t]:t);s[s.length-1]=s[s.length-1].replace(/\r?\n([\t ]*)$/,"");var r=s.reduce(function(l,c){var p=c.match(/\n([\t ]+|(?!\s).)/g);return p?l.concat(p.map(function(u){var f,h;return(h=(f=u.match(/[\t ]/g))===null||f===void 0?void 0:f.length)!==null&&h!==void 0?h:0})):l},[]);if(r.length){var i=new RegExp(`
[	 ]{`+Math.min.apply(Math,r)+"}","g");s=s.map(function(l){return l.replace(i,`
`)})}s[0]=s[0].replace(/^\r?\n/,"");var o=s[0];return e.forEach(function(l,c){var p=o.match(/(?:^|\n)( *)$/),u=p?p[1]:"",f=l;typeof l=="string"&&l.includes(`
`)&&(f=String(l).split(`
`).map(function(h,g){return g===0?h:""+u+h}).join(`
`)),o+=f+s[c+1]}),o}a(Ue,"dedent");function ge(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}a(ge,"M");var M=ge();function et(t){M=t}a(et,"H");var q={exec:a(()=>null,"exec")};function x(t,e=""){let n=typeof t=="string"?t:t.source,s={replace:a((r,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(y.caret,"$1"),n=n.replace(r,o),s},"replace"),getRegex:a(()=>new RegExp(n,e),"getRegex")};return s}a(x,"h");var y={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:a(t=>new RegExp(`^( {0,3}${t})((?:[	 ][^\\n]*)?(?:\\n|$))`),"listItemRegex"),nextBulletRegex:a(t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),"nextBulletRegex"),hrRegex:a(t=>new RegExp(`^ {0,${Math.min(3,t-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),"hrRegex"),fencesBeginRegex:a(t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:\`\`\`|~~~)`),"fencesBeginRegex"),headingBeginRegex:a(t=>new RegExp(`^ {0,${Math.min(3,t-1)}}#`),"headingBeginRegex"),htmlBeginRegex:a(t=>new RegExp(`^ {0,${Math.min(3,t-1)}}<(?:[a-z].*>|!--)`,"i"),"htmlBeginRegex")},qt=/^(?:[ \t]*(?:\n|$))+/,Wt=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Gt=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,W=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Nt=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,de=/(?:[*+-]|\d{1,9}[.)])/,tt=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,nt=x(tt).replace(/bull/g,de).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),Zt=x(tt).replace(/bull/g,de).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),me=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Vt=/^[^\n]+/,ke=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ht=x(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",ke).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ut=x(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,de).getRegex(),X="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",xe=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Qt=x("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",xe).replace("tag",X).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),rt=x(me).replace("hr",W).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",X).getRegex(),Jt=x(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",rt).getRegex(),be={blockquote:Jt,code:Wt,def:Ht,fences:Gt,heading:Nt,hr:W,html:Qt,lheading:nt,list:Ut,newline:qt,paragraph:rt,table:q,text:Vt},Qe=x("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",W).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",X).getRegex(),Kt={...be,lheading:Zt,table:Qe,paragraph:x(me).replace("hr",W).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Qe).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",X).getRegex()},Xt={...be,html:x(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",xe).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:q,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:x(me).replace("hr",W).replace("heading",` *#{1,6} *[^
]`).replace("lheading",nt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Yt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,en=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,st=/^( {2,}|\\)\n(?!\s*$)/,tn=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,Y=/[\p{P}\p{S}]/u,we=/[\s\p{P}\p{S}]/u,ot=/[^\s\p{P}\p{S}]/u,nn=x(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,we).getRegex(),it=/(?!~)[\p{P}\p{S}]/u,rn=/(?!~)[\s\p{P}\p{S}]/u,sn=/(?:[^\s\p{P}\p{S}]|~)/u,on=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,lt=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,ln=x(lt,"u").replace(/punct/g,Y).getRegex(),an=x(lt,"u").replace(/punct/g,it).getRegex(),at="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",cn=x(at,"gu").replace(/notPunctSpace/g,ot).replace(/punctSpace/g,we).replace(/punct/g,Y).getRegex(),pn=x(at,"gu").replace(/notPunctSpace/g,sn).replace(/punctSpace/g,rn).replace(/punct/g,it).getRegex(),un=x("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,ot).replace(/punctSpace/g,we).replace(/punct/g,Y).getRegex(),hn=x(/\\(punct)/,"gu").replace(/punct/g,Y).getRegex(),fn=x(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),gn=x(xe).replace("(?:-->|$)","-->").getRegex(),dn=x("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",gn).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Q=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,mn=x(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Q).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ct=x(/^!?\[(label)\]\[(ref)\]/).replace("label",Q).replace("ref",ke).getRegex(),pt=x(/^!?\[(ref)\](?:\[\])?/).replace("ref",ke).getRegex(),kn=x("reflink|nolink(?!\\()","g").replace("reflink",ct).replace("nolink",pt).getRegex(),ye={_backpedal:q,anyPunctuation:hn,autolink:fn,blockSkip:on,br:st,code:en,del:q,emStrongLDelim:ln,emStrongRDelimAst:cn,emStrongRDelimUnd:un,escape:Yt,link:mn,nolink:pt,punctuation:nn,reflink:ct,reflinkSearch:kn,tag:dn,text:tn,url:q},xn={...ye,link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",Q).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Q).getRegex()},ue={...ye,emStrongRDelimAst:pn,emStrongLDelim:an,url:x(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},bn={...ue,br:x(st).replace("{2,}","*").getRegex(),text:x(ue.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},H={normal:be,gfm:Kt,pedantic:Xt},B={normal:ye,gfm:ue,breaks:bn,pedantic:xn},wn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Je=a(t=>wn[t],"ge");function $(t,e){if(e){if(y.escapeTest.test(t))return t.replace(y.escapeReplace,Je)}else if(y.escapeTestNoEncode.test(t))return t.replace(y.escapeReplaceNoEncode,Je);return t}a($,"R");function Ke(t){try{t=encodeURI(t).replace(y.percentDecode,"%")}catch{return null}return t}a(Ke,"J");function Xe(t,e){let n=t.replace(y.findPipe,(i,o,l)=>{let c=!1,p=o;for(;--p>=0&&l[p]==="\\";)c=!c;return c?"|":" |"}),s=n.split(y.splitPipe),r=0;if(s[0].trim()||s.shift(),s.length>0&&!s.at(-1)?.trim()&&s.pop(),e)if(s.length>e)s.splice(e);else for(;s.length<e;)s.push("");for(;r<s.length;r++)s[r]=s[r].trim().replace(y.slashPipe,"|");return s}a(Xe,"V");function O(t,e,n){let s=t.length;if(s===0)return"";let r=0;for(;r<s;){let i=t.charAt(s-r-1);if(i===e&&!n)r++;else if(i!==e&&n)r++;else break}return t.slice(0,s-r)}a(O,"A");function yn(t,e){if(t.indexOf(e[1])===-1)return-1;let n=0;for(let s=0;s<t.length;s++)if(t[s]==="\\")s++;else if(t[s]===e[0])n++;else if(t[s]===e[1]&&(n--,n<0))return s;return n>0?-2:-1}a(yn,"fe");function Ye(t,e,n,s,r){let i=e.href,o=e.title||null,l=t[1].replace(r.other.outputLinkReplace,"$1");s.state.inLink=!0;let c={type:t[0].charAt(0)==="!"?"image":"link",raw:n,href:i,title:o,text:l,tokens:s.inlineTokens(l)};return s.state.inLink=!1,c}a(Ye,"de");function Cn(t,e,n){let s=t.match(n.other.indentCodeCompensation);if(s===null)return e;let r=s[1];return e.split(`
`).map(i=>{let o=i.match(n.other.beginningSpace);if(o===null)return i;let[l]=o;return l.length>=r.length?i.slice(r.length):i}).join(`
`)}a(Cn,"Je");var J=class{static{a(this,"S")}options;rules;lexer;constructor(t){this.options=t||M}space(t){let e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){let e=this.rules.block.code.exec(t);if(e){let n=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?n:O(n,`
`)}}}fences(t){let e=this.rules.block.fences.exec(t);if(e){let n=e[0],s=Cn(n,e[3]||"",this.rules);return{type:"code",raw:n,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:s}}}heading(t){let e=this.rules.block.heading.exec(t);if(e){let n=e[2].trim();if(this.rules.other.endingHash.test(n)){let s=O(n,"#");(this.options.pedantic||!s||this.rules.other.endingSpaceChar.test(s))&&(n=s.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(t){let e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:O(e[0],`
`)}}blockquote(t){let e=this.rules.block.blockquote.exec(t);if(e){let n=O(e[0],`
`).split(`
`),s="",r="",i=[];for(;n.length>0;){let o=!1,l=[],c;for(c=0;c<n.length;c++)if(this.rules.other.blockquoteStart.test(n[c]))l.push(n[c]),o=!0;else if(!o)l.push(n[c]);else break;n=n.slice(c);let p=l.join(`
`),u=p.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");s=s?`${s}
${p}`:p,r=r?`${r}
${u}`:u;let f=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,i,!0),this.lexer.state.top=f,n.length===0)break;let h=i.at(-1);if(h?.type==="code")break;if(h?.type==="blockquote"){let g=h,d=g.raw+`
`+n.join(`
`),m=this.blockquote(d);i[i.length-1]=m,s=s.substring(0,s.length-g.raw.length)+m.raw,r=r.substring(0,r.length-g.text.length)+m.text;break}else if(h?.type==="list"){let g=h,d=g.raw+`
`+n.join(`
`),m=this.list(d);i[i.length-1]=m,s=s.substring(0,s.length-h.raw.length)+m.raw,r=r.substring(0,r.length-g.raw.length)+m.raw,n=d.substring(i.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:s,tokens:i,text:r}}}list(t){let e=this.rules.block.list.exec(t);if(e){let n=e[1].trim(),s=n.length>1,r={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");let i=this.rules.other.listItemRegex(n),o=!1;for(;t;){let c=!1,p="",u="";if(!(e=i.exec(t))||this.rules.block.hr.test(t))break;p=e[0],t=t.substring(p.length);let f=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,C=>" ".repeat(3*C.length)),h=t.split(`
`,1)[0],g=!f.trim(),d=0;if(this.options.pedantic?(d=2,u=f.trimStart()):g?d=e[1].length+1:(d=e[2].search(this.rules.other.nonSpaceChar),d=d>4?1:d,u=f.slice(d),d+=e[1].length),g&&this.rules.other.blankLine.test(h)&&(p+=h+`
`,t=t.substring(h.length+1),c=!0),!c){let C=this.rules.other.nextBulletRegex(d),I=this.rules.other.hrRegex(d),S=this.rules.other.fencesBeginRegex(d),w=this.rules.other.headingBeginRegex(d),$e=this.rules.other.htmlBeginRegex(d);for(;t;){let v=t.split(`
`,1)[0],A;if(h=v,this.options.pedantic?(h=h.replace(this.rules.other.listReplaceNesting,"  "),A=h):A=h.replace(this.rules.other.tabCharGlobal,"    "),S.test(h)||w.test(h)||$e.test(h)||C.test(h)||I.test(h))break;if(A.search(this.rules.other.nonSpaceChar)>=d||!h.trim())u+=`
`+A.slice(d);else{if(g||f.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||S.test(f)||w.test(f)||I.test(f))break;u+=`
`+h}!g&&!h.trim()&&(g=!0),p+=v+`
`,t=t.substring(v.length+1),f=A.slice(d)}}r.loose||(o?r.loose=!0:this.rules.other.doubleBlankLine.test(p)&&(o=!0));let m=null,b;this.options.gfm&&(m=this.rules.other.listIsTask.exec(u),m&&(b=m[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),r.items.push({type:"list_item",raw:p,task:!!m,checked:b,loose:!1,text:u,tokens:[]}),r.raw+=p}let l=r.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;r.raw=r.raw.trimEnd();for(let c=0;c<r.items.length;c++)if(this.lexer.state.top=!1,r.items[c].tokens=this.lexer.blockTokens(r.items[c].text,[]),!r.loose){let p=r.items[c].tokens.filter(f=>f.type==="space"),u=p.length>0&&p.some(f=>this.rules.other.anyLine.test(f.raw));r.loose=u}if(r.loose)for(let c=0;c<r.items.length;c++)r.items[c].loose=!0;return r}}html(t){let e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){let e=this.rules.block.def.exec(t);if(e){let n=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),s=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:n,raw:e[0],href:s,title:r}}}table(t){let e=this.rules.block.table.exec(t);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;let n=Xe(e[1]),s=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),r=e[3]?.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],i={type:"table",raw:e[0],header:[],align:[],rows:[]};if(n.length===s.length){for(let o of s)this.rules.other.tableAlignRight.test(o)?i.align.push("right"):this.rules.other.tableAlignCenter.test(o)?i.align.push("center"):this.rules.other.tableAlignLeft.test(o)?i.align.push("left"):i.align.push(null);for(let o=0;o<n.length;o++)i.header.push({text:n[o],tokens:this.lexer.inline(n[o]),header:!0,align:i.align[o]});for(let o of r)i.rows.push(Xe(o,i.header.length).map((l,c)=>({text:l,tokens:this.lexer.inline(l),header:!1,align:i.align[c]})));return i}}lheading(t){let e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){let e=this.rules.block.paragraph.exec(t);if(e){let n=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:n,tokens:this.lexer.inline(n)}}}text(t){let e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){let e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(t){let e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){let e=this.rules.inline.link.exec(t);if(e){let n=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;let i=O(n.slice(0,-1),"\\");if((n.length-i.length)%2===0)return}else{let i=yn(e[2],"()");if(i===-2)return;if(i>-1){let o=(e[0].indexOf("!")===0?5:4)+e[1].length+i;e[2]=e[2].substring(0,i),e[0]=e[0].substring(0,o).trim(),e[3]=""}}let s=e[2],r="";if(this.options.pedantic){let i=this.rules.other.pedanticHrefTitle.exec(s);i&&(s=i[1],r=i[3])}else r=e[3]?e[3].slice(1,-1):"";return s=s.trim(),this.rules.other.startAngleBracket.test(s)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?s=s.slice(1):s=s.slice(1,-1)),Ye(e,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(t,e){let n;if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){let s=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),r=e[s.toLowerCase()];if(!r){let i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return Ye(n,r,n[0],this.lexer,this.rules)}}emStrong(t,e,n=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(!(!s||s[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){let r=[...s[0]].length-1,i,o,l=r,c=0,p=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(p.lastIndex=0,e=e.slice(-1*t.length+r);(s=p.exec(e))!=null;){if(i=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!i)continue;if(o=[...i].length,s[3]||s[4]){l+=o;continue}else if((s[5]||s[6])&&r%3&&!((r+o)%3)){c+=o;continue}if(l-=o,l>0)continue;o=Math.min(o,o+l+c);let u=[...s[0]][0].length,f=t.slice(0,r+s.index+u+o);if(Math.min(r,o)%2){let g=f.slice(1,-1);return{type:"em",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}let h=f.slice(2,-2);return{type:"strong",raw:f,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(t){let e=this.rules.inline.code.exec(t);if(e){let n=e[2].replace(this.rules.other.newLineCharGlobal," "),s=this.rules.other.nonSpaceChar.test(n),r=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return s&&r&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:e[0],text:n}}}br(t){let e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){let e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){let e=this.rules.inline.autolink.exec(t);if(e){let n,s;return e[2]==="@"?(n=e[1],s="mailto:"+n):(n=e[1],s=n),{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(t){let e;if(e=this.rules.inline.url.exec(t)){let n,s;if(e[2]==="@")n=e[0],s="mailto:"+n;else{let r;do r=e[0],e[0]=this.rules.inline._backpedal.exec(e[0])?.[0]??"";while(r!==e[0]);n=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){let e=this.rules.inline.text.exec(t);if(e){let n=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:n}}}},R=class he{static{a(this,"a")}tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||M,this.options.tokenizer=this.options.tokenizer||new J,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let n={other:y,block:H.normal,inline:B.normal};this.options.pedantic?(n.block=H.pedantic,n.inline=B.pedantic):this.options.gfm&&(n.block=H.gfm,this.options.breaks?n.inline=B.breaks:n.inline=B.gfm),this.tokenizer.rules=n}static get rules(){return{block:H,inline:B}}static lex(e,n){return new he(n).lex(e)}static lexInline(e,n){return new he(n).inlineTokens(e)}lex(e){e=e.replace(y.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){let s=this.inlineQueue[n];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,n=[],s=!1){for(this.options.pedantic&&(e=e.replace(y.tabCharGlobal,"    ").replace(y.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(o=>(r=o.call({lexer:this},e,n))?(e=e.substring(r.raw.length),n.push(r),!0):!1))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let o=n.at(-1);r.raw.length===1&&o!==void 0?o.raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let o=n.at(-1);o?.type==="paragraph"||o?.type==="text"?(o.raw+=`
`+r.raw,o.text+=`
`+r.text,this.inlineQueue.at(-1).src=o.text):n.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let o=n.at(-1);o?.type==="paragraph"||o?.type==="text"?(o.raw+=`
`+r.raw,o.text+=`
`+r.raw,this.inlineQueue.at(-1).src=o.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),n.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let o=1/0,l=e.slice(1),c;this.options.extensions.startBlock.forEach(p=>{c=p.call({lexer:this},l),typeof c=="number"&&c>=0&&(o=Math.min(o,c))}),o<1/0&&o>=0&&(i=e.substring(0,o+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let o=n.at(-1);s&&o?.type==="paragraph"?(o.raw+=`
`+r.raw,o.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):n.push(r),s=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let o=n.at(-1);o?.type==="text"?(o.raw+=`
`+r.raw,o.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):n.push(r);continue}if(e){let o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,n}inline(e,n=[]){return this.inlineQueue.push({src:e,tokens:n}),n}inlineTokens(e,n=[]){let s=e,r=null;if(this.tokens.links){let l=Object.keys(this.tokens.links);if(l.length>0)for(;(r=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)l.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(r=this.tokenizer.rules.inline.anyPunctuation.exec(s))!=null;)s=s.slice(0,r.index)+"++"+s.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(r=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,o="";for(;e;){i||(o=""),i=!1;let l;if(this.options.extensions?.inline?.some(p=>(l=p.call({lexer:this},e,n))?(e=e.substring(l.raw.length),n.push(l),!0):!1))continue;if(l=this.tokenizer.escape(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.tag(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.link(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(l.raw.length);let p=n.at(-1);l.type==="text"&&p?.type==="text"?(p.raw+=l.raw,p.text+=l.text):n.push(l);continue}if(l=this.tokenizer.emStrong(e,s,o)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.codespan(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.br(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.del(e)){e=e.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.autolink(e)){e=e.substring(l.raw.length),n.push(l);continue}if(!this.state.inLink&&(l=this.tokenizer.url(e))){e=e.substring(l.raw.length),n.push(l);continue}let c=e;if(this.options.extensions?.startInline){let p=1/0,u=e.slice(1),f;this.options.extensions.startInline.forEach(h=>{f=h.call({lexer:this},u),typeof f=="number"&&f>=0&&(p=Math.min(p,f))}),p<1/0&&p>=0&&(c=e.substring(0,p+1))}if(l=this.tokenizer.inlineText(c)){e=e.substring(l.raw.length),l.raw.slice(-1)!=="_"&&(o=l.raw.slice(-1)),i=!0;let p=n.at(-1);p?.type==="text"?(p.raw+=l.raw,p.text+=l.text):n.push(l);continue}if(e){let p="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(p);break}else throw new Error(p)}}return n}},K=class{static{a(this,"$")}options;parser;constructor(t){this.options=t||M}space(t){return""}code({text:t,lang:e,escaped:n}){let s=(e||"").match(y.notSpaceStart)?.[0],r=t.replace(y.endingNewline,"")+`
`;return s?'<pre><code class="language-'+$(s)+'">'+(n?r:$(r,!0))+`</code></pre>
`:"<pre><code>"+(n?r:$(r,!0))+`</code></pre>
`}blockquote({tokens:t}){return`<blockquote>
${this.parser.parse(t)}</blockquote>
`}html({text:t}){return t}heading({tokens:t,depth:e}){return`<h${e}>${this.parser.parseInline(t)}</h${e}>
`}hr(t){return`<hr>
`}list(t){let e=t.ordered,n=t.start,s="";for(let o=0;o<t.items.length;o++){let l=t.items[o];s+=this.listitem(l)}let r=e?"ol":"ul",i=e&&n!==1?' start="'+n+'"':"";return"<"+r+i+`>
`+s+"</"+r+`>
`}listitem(t){let e="";if(t.task){let n=this.checkbox({checked:!!t.checked});t.loose?t.tokens[0]?.type==="paragraph"?(t.tokens[0].text=n+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&t.tokens[0].tokens[0].type==="text"&&(t.tokens[0].tokens[0].text=n+" "+$(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):e+=n+" "}return e+=this.parser.parse(t.tokens,!!t.loose),`<li>${e}</li>
`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>
`}table(t){let e="",n="";for(let r=0;r<t.header.length;r++)n+=this.tablecell(t.header[r]);e+=this.tablerow({text:n});let s="";for(let r=0;r<t.rows.length;r++){let i=t.rows[r];n="";for(let o=0;o<i.length;o++)n+=this.tablecell(i[o]);s+=this.tablerow({text:n})}return s&&(s=`<tbody>${s}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+s+`</table>
`}tablerow({text:t}){return`<tr>
${t}</tr>
`}tablecell(t){let e=this.parser.parseInline(t.tokens),n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${$(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:e,tokens:n}){let s=this.parser.parseInline(n),r=Ke(t);if(r===null)return s;t=r;let i='<a href="'+t+'"';return e&&(i+=' title="'+$(e)+'"'),i+=">"+s+"</a>",i}image({href:t,title:e,text:n,tokens:s}){s&&(n=this.parser.parseInline(s,this.parser.textRenderer));let r=Ke(t);if(r===null)return $(n);t=r;let i=`<img src="${t}" alt="${n}"`;return e&&(i+=` title="${$(e)}"`),i+=">",i}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:$(t.text)}},Ce=class{static{a(this,"_")}strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}},F=class fe{static{a(this,"a")}options;renderer;textRenderer;constructor(e){this.options=e||M,this.options.renderer=this.options.renderer||new K,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Ce}static parse(e,n){return new fe(n).parse(e)}static parseInline(e,n){return new fe(n).parseInline(e)}parse(e,n=!0){let s="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let l=i,c=this.options.extensions.renderers[l.type].call({parser:this},l);if(c!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){s+=c||"";continue}}let o=i;switch(o.type){case"space":{s+=this.renderer.space(o);continue}case"hr":{s+=this.renderer.hr(o);continue}case"heading":{s+=this.renderer.heading(o);continue}case"code":{s+=this.renderer.code(o);continue}case"table":{s+=this.renderer.table(o);continue}case"blockquote":{s+=this.renderer.blockquote(o);continue}case"list":{s+=this.renderer.list(o);continue}case"html":{s+=this.renderer.html(o);continue}case"paragraph":{s+=this.renderer.paragraph(o);continue}case"text":{let l=o,c=this.renderer.text(l);for(;r+1<e.length&&e[r+1].type==="text";)l=e[++r],c+=`
`+this.renderer.text(l);n?s+=this.renderer.paragraph({type:"paragraph",raw:c,text:c,tokens:[{type:"text",raw:c,text:c,escaped:!0}]}):s+=c;continue}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return s}parseInline(e,n=this.renderer){let s="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let l=this.options.extensions.renderers[i.type].call({parser:this},i);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){s+=l||"";continue}}let o=i;switch(o.type){case"escape":{s+=n.text(o);break}case"html":{s+=n.html(o);break}case"link":{s+=n.link(o);break}case"image":{s+=n.image(o);break}case"strong":{s+=n.strong(o);break}case"em":{s+=n.em(o);break}case"codespan":{s+=n.codespan(o);break}case"br":{s+=n.br(o);break}case"del":{s+=n.del(o);break}case"text":{s+=n.text(o);break}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return s}},U=class{static{a(this,"L")}options;block;constructor(t){this.options=t||M}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?R.lex:R.lexInline}provideParser(){return this.block?F.parse:F.parseInline}},Sn=class{static{a(this,"B")}defaults=ge();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=F;Renderer=K;TextRenderer=Ce;Lexer=R;Tokenizer=J;Hooks=U;constructor(...t){this.use(...t)}walkTokens(t,e){let n=[];for(let s of t)switch(n=n.concat(e.call(this,s)),s.type){case"table":{let r=s;for(let i of r.header)n=n.concat(this.walkTokens(i.tokens,e));for(let i of r.rows)for(let o of i)n=n.concat(this.walkTokens(o.tokens,e));break}case"list":{let r=s;n=n.concat(this.walkTokens(r.items,e));break}default:{let r=s;this.defaults.extensions?.childTokens?.[r.type]?this.defaults.extensions.childTokens[r.type].forEach(i=>{let o=r[i].flat(1/0);n=n.concat(this.walkTokens(o,e))}):r.tokens&&(n=n.concat(this.walkTokens(r.tokens,e)))}}return n}use(...t){let e=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach(n=>{let s={...n};if(s.async=this.defaults.async||s.async||!1,n.extensions&&(n.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){let i=e.renderers[r.name];i?e.renderers[r.name]=function(...o){let l=r.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:e.renderers[r.name]=r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let i=e[r.level];i?i.unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),s.extensions=e),n.renderer){let r=this.defaults.renderer||new K(this.defaults);for(let i in n.renderer){if(!(i in r))throw new Error(`renderer '${i}' does not exist`);if(["options","parser"].includes(i))continue;let o=i,l=n.renderer[o],c=r[o];r[o]=(...p)=>{let u=l.apply(r,p);return u===!1&&(u=c.apply(r,p)),u||""}}s.renderer=r}if(n.tokenizer){let r=this.defaults.tokenizer||new J(this.defaults);for(let i in n.tokenizer){if(!(i in r))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;let o=i,l=n.tokenizer[o],c=r[o];r[o]=(...p)=>{let u=l.apply(r,p);return u===!1&&(u=c.apply(r,p)),u}}s.tokenizer=r}if(n.hooks){let r=this.defaults.hooks||new U;for(let i in n.hooks){if(!(i in r))throw new Error(`hook '${i}' does not exist`);if(["options","block"].includes(i))continue;let o=i,l=n.hooks[o],c=r[o];U.passThroughHooks.has(i)?r[o]=p=>{if(this.defaults.async)return Promise.resolve(l.call(r,p)).then(f=>c.call(r,f));let u=l.call(r,p);return c.call(r,u)}:r[o]=(...p)=>{let u=l.apply(r,p);return u===!1&&(u=c.apply(r,p)),u}}s.hooks=r}if(n.walkTokens){let r=this.defaults.walkTokens,i=n.walkTokens;s.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),r&&(l=l.concat(r.call(this,o))),l}}this.defaults={...this.defaults,...s}}),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,e){return R.lex(t,e??this.defaults)}parser(t,e){return F.parse(t,e??this.defaults)}parseMarkdown(t){return(e,n)=>{let s={...n},r={...this.defaults,...s},i=this.onError(!!r.silent,!!r.async);if(this.defaults.async===!0&&s.async===!1)return i(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof e>"u"||e===null)return i(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));r.hooks&&(r.hooks.options=r,r.hooks.block=t);let o=r.hooks?r.hooks.provideLexer():t?R.lex:R.lexInline,l=r.hooks?r.hooks.provideParser():t?F.parse:F.parseInline;if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(e):e).then(c=>o(c,r)).then(c=>r.hooks?r.hooks.processAllTokens(c):c).then(c=>r.walkTokens?Promise.all(this.walkTokens(c,r.walkTokens)).then(()=>c):c).then(c=>l(c,r)).then(c=>r.hooks?r.hooks.postprocess(c):c).catch(i);try{r.hooks&&(e=r.hooks.preprocess(e));let c=o(e,r);r.hooks&&(c=r.hooks.processAllTokens(c)),r.walkTokens&&this.walkTokens(c,r.walkTokens);let p=l(c,r);return r.hooks&&(p=r.hooks.postprocess(p)),p}catch(c){return i(c)}}}onError(t,e){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,t){let s="<p>An error occurred:</p><pre>"+$(n.message+"",!0)+"</pre>";return e?Promise.resolve(s):s}if(e)return Promise.reject(n);throw n}}},L=new Sn;function k(t,e){return L.parse(t,e)}a(k,"k");k.options=k.setOptions=function(t){return L.setOptions(t),k.defaults=L.defaults,et(k.defaults),k};k.getDefaults=ge;k.defaults=M;k.use=function(...t){return L.use(...t),k.defaults=L.defaults,et(k.defaults),k};k.walkTokens=function(t,e){return L.walkTokens(t,e)};k.parseInline=L.parseInline;k.Parser=F;k.parser=F.parse;k.Renderer=K;k.TextRenderer=Ce;k.Lexer=R;k.lexer=R.lex;k.Tokenizer=J;k.Hooks=U;k.parse=k;var Fr=k.options,vr=k.setOptions,Ar=k.use,zr=k.walkTokens,Er=k.parseInline;var Lr=F.parse,Mr=R.lex;function Tn(t,{markdownAutoWrap:e}){let s=t.replace(/<br\/>/g,`
`).replace(/\n{2,}/g,`
`),r=Ue(s);return e===!1?r.replace(/ /g,"&nbsp;"):r}a(Tn,"preprocessMarkdown");function ut(t,e={}){let n=Tn(t,e),s=k.lexer(n),r=[[]],i=0;function o(l,c="normal"){l.type==="text"?l.text.split(`
`).forEach((u,f)=>{f!==0&&(i++,r.push([])),u.split(" ").forEach(h=>{h=h.replace(/&#39;/g,"'"),h&&r[i].push({content:h,type:c})})}):l.type==="strong"||l.type==="em"?l.tokens.forEach(p=>{o(p,l.type)}):l.type==="html"&&r[i].push({content:l.text,type:"normal"})}return a(o,"processNode"),s.forEach(l=>{l.type==="paragraph"?l.tokens?.forEach(c=>{o(c)}):l.type==="html"&&r[i].push({content:l.text,type:"normal"})}),r}a(ut,"markdownToLines");function ht(t,{markdownAutoWrap:e}={}){let n=k.lexer(t);function s(r){return r.type==="text"?e===!1?r.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;"):r.text.replace(/\n */g,"<br/>"):r.type==="strong"?`<strong>${r.tokens?.map(s).join("")}</strong>`:r.type==="em"?`<em>${r.tokens?.map(s).join("")}</em>`:r.type==="paragraph"?`<p>${r.tokens?.map(s).join("")}</p>`:r.type==="space"?"":r.type==="html"?`${r.text}`:r.type==="escape"?r.text:`Unsupported markdown: ${r.type}`}return a(s,"output"),n.map(s).join("")}a(ht,"markdownToHTML");function In(t){return Intl.Segmenter?[...new Intl.Segmenter().segment(t)].map(e=>e.segment):[...t]}a(In,"splitTextToChars");function $n(t,e){let n=In(e.content);return ft(t,[],n,e.type)}a($n,"splitWordToFitWidth");function ft(t,e,n,s){if(n.length===0)return[{content:e.join(""),type:s},{content:"",type:s}];let[r,...i]=n,o=[...e,r];return t([{content:o.join(""),type:s}])?ft(t,o,i,s):(e.length===0&&r&&(e.push(r),n.shift()),[{content:e.join(""),type:s},{content:n.join(""),type:s}])}a(ft,"splitWordToFitWidthRecursion");function gt(t,e){if(t.some(({content:n})=>n.includes(`
`)))throw new Error("splitLineToFitWidth does not support newlines in the line");return Se(t,e)}a(gt,"splitLineToFitWidth");function Se(t,e,n=[],s=[]){if(t.length===0)return s.length>0&&n.push(s),n.length>0?n:[];let r="";t[0].content===" "&&(r=" ",t.shift());let i=t.shift()??{content:" ",type:"normal"},o=[...s];if(r!==""&&o.push({content:r,type:"normal"}),o.push(i),e(o))return Se(t,e,n,o);if(s.length>0)n.push(s),t.unshift(i);else if(i.content){let[l,c]=$n(e,i);n.push([l]),c.content&&t.unshift(c)}return Se(t,e,n)}a(Se,"splitLineToFitWidthRecursion");function dt(t,e){e&&t.attr("style",e)}a(dt,"applyStyle");async function Rn(t,e,n,s,r=!1){let i=t.append("foreignObject");i.attr("width",`${10*n}px`),i.attr("height",`${10*n}px`);let o=i.append("xhtml:div"),l=e.label;e.label&&te(e.label)&&(l=await Fe(e.label.replace(ve.lineBreakRegex,`
`),Ae()));let c=e.isNode?"nodeLabel":"edgeLabel",p=o.append("span");p.html(l),dt(p,e.labelStyle),p.attr("class",`${c} ${s}`),dt(o,e.labelStyle),o.style("display","table-cell"),o.style("white-space","nowrap"),o.style("line-height","1.5"),o.style("max-width",n+"px"),o.style("text-align","center"),o.attr("xmlns","http://www.w3.org/1999/xhtml"),r&&o.attr("class","labelBkg");let u=o.node().getBoundingClientRect();return u.width===n&&(o.style("display","table"),o.style("white-space","break-spaces"),o.style("width",n+"px"),u=o.node().getBoundingClientRect()),i.node()}a(Rn,"addHtmlSpan");function Te(t,e,n){return t.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",e*n-.1+"em").attr("dy",n+"em")}a(Te,"createTspan");function Fn(t,e,n){let s=t.append("text"),r=Te(s,1,e);Ie(r,n);let i=r.node().getComputedTextLength();return s.remove(),i}a(Fn,"computeWidthOfText");function ns(t,e,n){let s=t.append("text"),r=Te(s,1,e);Ie(r,[{content:n,type:"normal"}]);let i=r.node()?.getBoundingClientRect();return i&&s.remove(),i}a(ns,"computeDimensionOfText");function vn(t,e,n,s=!1){let i=e.append("g"),o=i.insert("rect").attr("class","background").attr("style","stroke: none"),l=i.append("text").attr("y","-10.1"),c=0;for(let p of n){let u=a(h=>Fn(i,1.1,h)<=t,"checkWidth"),f=u(p)?[p]:gt(p,u);for(let h of f){let g=Te(l,c,1.1);Ie(g,h),c++}}if(s){let p=l.node().getBBox(),u=2;return o.attr("x",p.x-u).attr("y",p.y-u).attr("width",p.width+2*u).attr("height",p.height+2*u),i.node()}else return l.node()}a(vn,"createFormattedText");function Ie(t,e){t.text(""),e.forEach((n,s)=>{let r=t.append("tspan").attr("font-style",n.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",n.type==="strong"?"bold":"normal");s===0?r.text(n.content):r.text(" "+n.content)})}a(Ie,"updateTextContentAndStyles");async function An(t){let e=[];t.replace(/(fa[bklrs]?):fa-([\w-]+)/g,(s,r,i)=>(e.push((async()=>{let o=`${r}:${i}`;return await Ve(o)?await He(o,void 0,{class:"label-icon"}):`<i class='${ze(s).replace(":"," ")}'></i>`})()),s));let n=await Promise.all(e);return t.replace(/(fa[bklrs]?):fa-([\w-]+)/g,()=>n.shift()??"")}a(An,"replaceIconSubstring");var rs=a(async(t,e="",{style:n="",isTitle:s=!1,classes:r="",useHtmlLabels:i=!0,isNode:o=!0,width:l=200,addSvgBackground:c=!1}={},p)=>{if(z.debug("XYZ createText",e,n,s,r,i,o,"addSvgBackground: ",c),i){let u=ht(e,p),f=await An(Ee(u)),h=e.replace(/\\\\/g,"\\"),g={isNode:o,label:te(e)?h:f,labelStyle:n.replace("fill:","color:")};return await Rn(t,g,l,r,c)}else{let u=e.replace(/<br\s*\/?>/g,"<br/>"),f=ut(u.replace("<br>","<br/>"),p),h=vn(l,t,f,e?c:!1);if(o){/stroke:/.exec(n)&&(n=n.replace("stroke:","lineColor:"));let g=n.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");G(h).attr("style",g)}else{let g=n.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");G(h).select("rect").attr("style",g.replace(/background:/g,"fill:"));let d=n.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");G(h).select("text").attr("style",d)}return h}},"createText");export{Ot as a,Sr as b,He as c,Ue as d,ns as e,An as f,rs as g};
