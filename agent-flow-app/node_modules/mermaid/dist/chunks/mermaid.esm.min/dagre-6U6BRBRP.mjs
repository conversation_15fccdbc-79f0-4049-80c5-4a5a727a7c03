import{a as z}from"./chunk-FASC7IG4.mjs";import{a as C}from"./chunk-ZN7TASNU.mjs";import{a as R,b as M,c as F,d as U,e as Y}from"./chunk-3XUXSURB.mjs";import"./chunk-S67DUUA5.mjs";import{b as G,f as A,g as _,h as H,i as j,j as V,k as O}from"./chunk-5DLFQ4IS.mjs";import{a as T}from"./chunk-LM6QDVU5.mjs";import"./chunk-BW63ANAF.mjs";import"./chunk-3GKE4KUU.mjs";import"./chunk-YR5264OA.mjs";import"./chunk-TI4EEUUG.mjs";import{Y as B,b as i}from"./chunk-U6SPV2NK.mjs";import{D as y,e as q,s as k}from"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as g}from"./chunk-GTKDMUJJ.mjs";function h(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:re(e),edges:se(e)};return y(e.graph())||(t.value=q(e.graph())),t}g(h,"write");function re(e){return k(e.nodes(),function(t){var n=e.node(t),c=e.parent(t),s={v:t};return y(n)||(s.value=n),y(c)||(s.parent=c),s})}g(re,"writeNodes");function se(e){return k(e.edges(),function(t){var n=e.edge(t),c={v:t.v,w:t.w};return y(t.name)||(c.name=t.name),y(n)||(c.value=n),c})}g(se,"writeEdges");var a=new Map,v=new Map,W=new Map,Z=g(()=>{v.clear(),W.clear(),a.clear()},"clear"),J=g((e,t)=>{let n=v.get(t)||[];return i.trace("In isDescendant",t," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),ce=g((e,t)=>{let n=v.get(t)||[];return i.info("Descendants of ",t," is ",n),i.info("Edge is ",e),e.v===t||e.w===t?!1:n?n.includes(e.v)||J(e.v,t)||J(e.w,t)||n.includes(e.w):(i.debug("Tilt, ",t,",not in descendants"),!1)},"edgeInCluster"),$=g((e,t,n,c)=>{i.warn("Copying children of ",e,"root",c,"data",t.node(e),c);let s=t.children(e)||[];e!==c&&s.push(e),i.warn("Copying (nodes) clusterId",e,"nodes",s),s.forEach(o=>{if(t.children(o).length>0)$(o,t,n,c);else{let l=t.node(o);i.info("cp ",o," to ",c," with parent ",e),n.setNode(o,l),c!==t.parent(o)&&(i.warn("Setting parent",o,t.parent(o)),n.setParent(o,t.parent(o))),e!==c&&o!==e?(i.debug("Setting parent",o,e),n.setParent(o,e)):(i.info("In copy ",e,"root",c,"data",t.node(e),c),i.debug("Not Setting parent for node=",o,"cluster!==rootId",e!==c,"node!==clusterId",o!==e));let u=t.edges(o);i.debug("Copying Edges",u),u.forEach(d=>{i.info("Edge",d);let m=t.edge(d.v,d.w,d.name);i.info("Edge data",m,c);try{ce(d,c)?(i.info("Copying as ",d.v,d.w,m,d.name),n.setEdge(d.v,d.w,m,d.name),i.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):i.info("Skipping copy of edge ",d.v,"-->",d.w," rootId: ",c," clusterId:",e)}catch(p){i.error(p)}})}i.debug("Removing node",o),t.removeNode(o)})},"copy"),L=g((e,t)=>{let n=t.children(e),c=[...n];for(let s of n)W.set(s,e),c=[...c,...L(s,t)];return c},"extractDescendants");var ae=g((e,t,n)=>{let c=e.edges().filter(d=>d.v===t||d.w===t),s=e.edges().filter(d=>d.v===n||d.w===n),o=c.map(d=>({v:d.v===t?n:d.v,w:d.w===t?t:d.w})),l=s.map(d=>({v:d.v,w:d.w}));return o.filter(d=>l.some(m=>d.v===m.v&&d.w===m.w))},"findCommonEdges"),x=g((e,t,n)=>{let c=t.children(e);if(i.trace("Searching children of id ",e,c),c.length<1)return e;let s;for(let o of c){let l=x(o,t,n),u=ae(t,n,l);if(l)if(u.length>0)s=l;else return l}return s},"findNonClusterChild"),Q=g(e=>!a.has(e)||!a.get(e).externalConnections?e:a.has(e)?a.get(e).id:e,"getAnchorId"),I=g((e,t)=>{if(!e||t>10){i.debug("Opting out, no graph ");return}else i.debug("Opting in, graph ");e.nodes().forEach(function(n){e.children(n).length>0&&(i.warn("Cluster identified",n," Replacement id in edges: ",x(n,e,n)),v.set(n,L(n,e)),a.set(n,{id:x(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){let c=e.children(n),s=e.edges();c.length>0?(i.debug("Cluster identified",n,v),s.forEach(o=>{let l=J(o.v,n),u=J(o.w,n);l^u&&(i.warn("Edge: ",o," leaves cluster ",n),i.warn("Descendants of XXX ",n,": ",v.get(n)),a.get(n).externalConnections=!0)})):i.debug("Not a cluster ",n,v)});for(let n of a.keys()){let c=a.get(n).id,s=e.parent(c);s!==n&&a.has(s)&&!a.get(s).externalConnections&&(a.get(n).id=s)}e.edges().forEach(function(n){let c=e.edge(n);i.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),i.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let s=n.v,o=n.w;if(i.warn("Fix XXX",a,"ids:",n.v,n.w,"Translating: ",a.get(n.v)," --- ",a.get(n.w)),a.get(n.v)||a.get(n.w)){if(i.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),s=Q(n.v),o=Q(n.w),e.removeEdge(n.v,n.w,n.name),s!==n.v){let l=e.parent(s);a.get(l).externalConnections=!0,c.fromCluster=n.v}if(o!==n.w){let l=e.parent(o);a.get(l).externalConnections=!0,c.toCluster=n.w}i.warn("Fix Replacing with XXX",s,o,n.name),e.setEdge(s,o,c,n.name)}}),i.warn("Adjusted Graph",h(e)),ee(e,0),i.trace(a)},"adjustClustersAndEdges"),ee=g((e,t)=>{if(i.warn("extractor - ",t,h(e),e.children("D")),t>10){i.error("Bailing out");return}let n=e.nodes(),c=!1;for(let s of n){let o=e.children(s);c=c||o.length>0}if(!c){i.debug("Done, no node has children",e.nodes());return}i.debug("Nodes = ",n,t);for(let s of n)if(i.debug("Extracting node",s,a,a.has(s)&&!a.get(s).externalConnections,!e.parent(s),e.node(s),e.children("D")," Depth ",t),!a.has(s))i.debug("Not a cluster",s,t);else if(!a.get(s).externalConnections&&e.children(s)&&e.children(s).length>0){i.warn("Cluster without external connections, without a parent and with children",s,t);let l=e.graph().rankdir==="TB"?"LR":"TB";a.get(s)?.clusterData?.dir&&(l=a.get(s).clusterData.dir,i.warn("Fixing dir",a.get(s).clusterData.dir,l));let u=new C({multigraph:!0,compound:!0}).setGraph({rankdir:l,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});i.warn("Old graph before copy",h(e)),$(s,e,u,s),e.setNode(s,{clusterNode:!0,id:s,clusterData:a.get(s).clusterData,label:a.get(s).label,graph:u}),i.warn("New graph after copy node: (",s,")",h(u)),i.debug("Old graph after copy",h(e))}else i.warn("Cluster ** ",s," **not meeting the criteria !externalConnections:",!a.get(s).externalConnections," no parent: ",!e.parent(s)," children ",e.children(s)&&e.children(s).length>0,e.children("D"),t),i.debug(a);n=e.nodes(),i.warn("New list of nodes",n);for(let s of n){let o=e.node(s);i.warn(" Now next level",s,o),o?.clusterNode&&ee(o.graph,t+1)}},"extractor"),ne=g((e,t)=>{if(t.length===0)return[];let n=Object.assign([],t);return t.forEach(c=>{let s=e.children(c),o=ne(e,s);n=[...n,...o]}),n},"sorter"),te=g(e=>ne(e,e.children()),"sortNodesByHierarchy");var ie=g(async(e,t,n,c,s,o)=>{i.warn("Graph in recursive render:XAX",h(t),s);let l=t.graph().rankdir;i.trace("Dir in recursive render - dir:",l);let u=e.insert("g").attr("class","root");t.nodes()?i.info("Recursive render XXX",t.nodes()):i.info("No nodes found for",t),t.edges().length>0&&i.info("Recursive edges",t.edge(t.edges()[0]));let d=u.insert("g").attr("class","clusters"),m=u.insert("g").attr("class","edgePaths"),p=u.insert("g").attr("class","edgeLabels"),b=u.insert("g").attr("class","nodes");await Promise.all(t.nodes().map(async function(f){let r=t.node(f);if(s!==void 0){let w=JSON.parse(JSON.stringify(s.clusterData));i.trace(`Setting data for parent cluster XXX
 Node.id = `,f,`
 data=`,w.height,`
Parent cluster`,s.height),t.setNode(s.id,w),t.parent(f)||(i.trace("Setting parent",f,s.id),t.setParent(f,s.id,w))}if(i.info("(Insert) Node XXX"+f+": "+JSON.stringify(t.node(f))),r?.clusterNode){i.info("Cluster identified XBX",f,r.width,t.node(f));let{ranksep:w,nodesep:E}=t.graph();r.graph.setGraph({...r.graph.graph(),ranksep:w+25,nodesep:E});let N=await ie(b,r.graph,n,c,t.node(f),o),D=N.elem;G(r,D),r.diff=N.diff||0,i.info("New compound node after recursive render XAX",f,"width",r.width,"height",r.height),j(D,r)}else t.children(f).length>0?(i.trace("Cluster - the non recursive path XBX",f,r.id,r,r.width,"Graph:",t),i.trace(x(r.id,t)),a.set(r.id,{id:x(r.id,t),node:r})):(i.trace("Node - the non recursive path XAX",f,b,t.node(f),l),await H(b,t.node(f),{config:o,dir:l}))})),await g(async()=>{let f=t.edges().map(async function(r){let w=t.edge(r.v,r.w,r.name);i.info("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(r)),i.info("Edge "+r.v+" -> "+r.w+": ",r," ",JSON.stringify(t.edge(r))),i.info("Fix",a,"ids:",r.v,r.w,"Translating: ",a.get(r.v),a.get(r.w)),await M(p,w)});await Promise.all(f)},"processEdges")(),i.info("Graph before layout:",JSON.stringify(h(t))),i.info("############################################# XXX"),i.info("###                Layout                 ### XXX"),i.info("############################################# XXX"),z(t),i.info("Graph after layout:",JSON.stringify(h(t)));let P=0,{subGraphTitleTotalMargin:S}=T(o);return await Promise.all(te(t).map(async function(f){let r=t.node(f);if(i.info("Position XBX => "+f+": ("+r.x,","+r.y,") width: ",r.width," height: ",r.height),r?.clusterNode)r.y+=S,i.info("A tainted cluster node XBX1",f,r.id,r.width,r.height,r.x,r.y,t.parent(f)),a.get(r.id).node=r,O(r);else if(t.children(f).length>0){i.info("A pure cluster node XBX1",f,r.id,r.x,r.y,r.width,r.height,t.parent(f)),r.height+=S,t.node(r.parentId);let w=r?.padding/2||0,E=r?.labelBBox?.height||0,N=E-w||0;i.debug("OffsetY",N,"labelHeight",E,"halfPadding",w),await A(d,r),a.get(r.id).node=r}else{let w=t.node(r.parentId);r.y+=S/2,i.info("A regular node XBX1 - using the padding",r.id,"parent",r.parentId,r.width,r.height,r.x,r.y,"offsetY",r.offsetY,"parent",w,w?.offsetY,r),O(r)}})),t.edges().forEach(function(f){let r=t.edge(f);i.info("Edge "+f.v+" -> "+f.w+": "+JSON.stringify(r),r),r.points.forEach(D=>D.y+=S/2);let w=t.node(f.v);var E=t.node(f.w);let N=U(m,r,a,n,w,E,c);F(r,N)}),t.nodes().forEach(function(f){let r=t.node(f);i.info(f,r.type,r.diff),r.isGroup&&(P=r.diff)}),i.warn("Returning from recursive render XAX",u,P),{elem:u,diff:P}},"recursiveRender"),Se=g(async(e,t)=>{let n=new C({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:e.config?.nodeSpacing||e.config?.flowchart?.nodeSpacing||e.nodeSpacing,ranksep:e.config?.rankSpacing||e.config?.flowchart?.rankSpacing||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),c=t.select("g");Y(c,e.markers,e.type,e.diagramId),V(),R(),_(),Z(),e.nodes.forEach(o=>{n.setNode(o.id,{...o}),o.parentId&&n.setParent(o.id,o.parentId)}),i.debug("Edges:",e.edges),e.edges.forEach(o=>{if(o.start===o.end){let l=o.start,u=l+"---"+l+"---1",d=l+"---"+l+"---2",m=n.node(l);n.setNode(u,{domId:u,id:u,parentId:m.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(u,m.parentId),n.setNode(d,{domId:d,id:d,parentId:m.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(d,m.parentId);let p=structuredClone(o),b=structuredClone(o),X=structuredClone(o);p.label="",p.arrowTypeEnd="none",p.id=l+"-cyclic-special-1",b.arrowTypeStart="none",b.arrowTypeEnd="none",b.id=l+"-cyclic-special-mid",X.label="",m.isGroup&&(p.fromCluster=l,X.toCluster=l),X.id=l+"-cyclic-special-2",X.arrowTypeStart="none",n.setEdge(l,u,p,l+"-cyclic-special-0"),n.setEdge(u,d,b,l+"-cyclic-special-1"),n.setEdge(d,l,X,l+"-cyc<lic-special-2")}else n.setEdge(o.start,o.end,{...o},o.id)}),i.warn("Graph at first:",JSON.stringify(h(n))),I(n),i.warn("Graph after XAX:",JSON.stringify(h(n)));let s=B();await ie(c,n,e.type,e.diagramId,void 0,s)},"render");export{Se as render};
