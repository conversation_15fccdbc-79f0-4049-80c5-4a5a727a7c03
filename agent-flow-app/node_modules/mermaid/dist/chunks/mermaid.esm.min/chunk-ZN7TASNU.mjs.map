{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/graph.js"], "sourcesContent": ["import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = Object.prototype.hasOwnProperty.call(opts, 'directed')\n      ? opts.directed\n      : true;\n    this._isMultigraph = Object.prototype.hasOwnProperty.call(opts, 'multigraph')\n      ? opts.multigraph\n      : false;\n    this._isCompound = Object.prototype.hasOwnProperty.call(opts, 'compound')\n      ? opts.compound\n      : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return Object.prototype.hasOwnProperty.call(this._nodes, v);\n  }\n  removeNode(v) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      var removeEdge = (e) => this.removeEdge(this._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), (child) => {\n          this.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.prototype.hasOwnProperty.call(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return Object.prototype.hasOwnProperty.call(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n"], "mappings": "qNAEA,IAAIA,EAAoB,KACpBC,EAAa,KACbC,EAAiB,IAqBRC,EAAN,KAAY,CAzBnB,MAyBmB,CAAAC,EAAA,cACjB,YAAYC,EAAO,CAAC,EAAG,CACrB,KAAK,YAAc,OAAO,UAAU,eAAe,KAAKA,EAAM,UAAU,EACpEA,EAAK,SACL,GACJ,KAAK,cAAgB,OAAO,UAAU,eAAe,KAAKA,EAAM,YAAY,EACxEA,EAAK,WACL,GACJ,KAAK,YAAc,OAAO,UAAU,eAAe,KAAKA,EAAM,UAAU,EACpEA,EAAK,SACL,GAGJ,KAAK,OAAS,OAGd,KAAK,oBAAwBC,EAAS,MAAS,EAG/C,KAAK,oBAAwBA,EAAS,MAAS,EAG/C,KAAK,OAAS,CAAC,EAEX,KAAK,cAEP,KAAK,QAAU,CAAC,EAGhB,KAAK,UAAY,CAAC,EAClB,KAAK,UAAUL,CAAU,EAAI,CAAC,GAIhC,KAAK,IAAM,CAAC,EAGZ,KAAK,OAAS,CAAC,EAGf,KAAK,KAAO,CAAC,EAGb,KAAK,MAAQ,CAAC,EAGd,KAAK,UAAY,CAAC,EAGlB,KAAK,YAAc,CAAC,CACtB,CAEA,YAAa,CACX,OAAO,KAAK,WACd,CACA,cAAe,CACb,OAAO,KAAK,aACd,CACA,YAAa,CACX,OAAO,KAAK,WACd,CACA,SAASM,EAAO,CACd,YAAK,OAASA,EACP,IACT,CACA,OAAQ,CACN,OAAO,KAAK,MACd,CAEA,oBAAoBC,EAAY,CAC9B,OAAOC,EAAWD,CAAU,IAC1BA,EAAeF,EAASE,CAAU,GAEpC,KAAK,oBAAsBA,EACpB,IACT,CACA,WAAY,CACV,OAAO,KAAK,UACd,CACA,OAAQ,CACN,OAASE,EAAK,KAAK,MAAM,CAC3B,CACA,SAAU,CACR,IAAIC,EAAO,KACX,OAASC,EAAO,KAAK,MAAM,EAAG,SAAUC,EAAG,CACzC,OAASC,EAAQH,EAAK,IAAIE,CAAC,CAAC,CAC9B,CAAC,CACH,CACA,OAAQ,CACN,IAAIF,EAAO,KACX,OAASC,EAAO,KAAK,MAAM,EAAG,SAAUC,EAAG,CACzC,OAASC,EAAQH,EAAK,KAAKE,CAAC,CAAC,CAC/B,CAAC,CACH,CACA,SAASE,EAAIC,EAAO,CAClB,IAAIC,EAAO,UACPN,EAAO,KACX,OAAEO,EAAKH,EAAI,SAAUF,EAAG,CAClBI,EAAK,OAAS,EAChBN,EAAK,QAAQE,EAAGG,CAAK,EAErBL,EAAK,QAAQE,CAAC,CAElB,CAAC,EACM,IACT,CACA,QAAQA,EAAGG,EAAO,CAChB,OAAI,OAAO,UAAU,eAAe,KAAK,KAAK,OAAQH,CAAC,GACjD,UAAU,OAAS,IACrB,KAAK,OAAOA,CAAC,EAAIG,GAEZ,OAIT,KAAK,OAAOH,CAAC,EAAI,UAAU,OAAS,EAAIG,EAAQ,KAAK,oBAAoBH,CAAC,EACtE,KAAK,cACP,KAAK,QAAQA,CAAC,EAAIZ,EAClB,KAAK,UAAUY,CAAC,EAAI,CAAC,EACrB,KAAK,UAAUZ,CAAU,EAAEY,CAAC,EAAI,IAElC,KAAK,IAAIA,CAAC,EAAI,CAAC,EACf,KAAK,OAAOA,CAAC,EAAI,CAAC,EAClB,KAAK,KAAKA,CAAC,EAAI,CAAC,EAChB,KAAK,MAAMA,CAAC,EAAI,CAAC,EACjB,EAAE,KAAK,WACA,KACT,CACA,KAAKA,EAAG,CACN,OAAO,KAAK,OAAOA,CAAC,CACtB,CACA,QAAQA,EAAG,CACT,OAAO,OAAO,UAAU,eAAe,KAAK,KAAK,OAAQA,CAAC,CAC5D,CACA,WAAWA,EAAG,CACZ,GAAI,OAAO,UAAU,eAAe,KAAK,KAAK,OAAQA,CAAC,EAAG,CACxD,IAAIM,EAAaf,EAACgB,GAAM,KAAK,WAAW,KAAK,UAAUA,CAAC,CAAC,EAAxC,cACjB,OAAO,KAAK,OAAOP,CAAC,EAChB,KAAK,cACP,KAAK,4BAA4BA,CAAC,EAClC,OAAO,KAAK,QAAQA,CAAC,EACnBK,EAAK,KAAK,SAASL,CAAC,EAAIQ,GAAU,CAClC,KAAK,UAAUA,CAAK,CACtB,CAAC,EACD,OAAO,KAAK,UAAUR,CAAC,GAEvBK,EAAOR,EAAK,KAAK,IAAIG,CAAC,CAAC,EAAGM,CAAU,EACtC,OAAO,KAAK,IAAIN,CAAC,EACjB,OAAO,KAAK,OAAOA,CAAC,EAClBK,EAAOR,EAAK,KAAK,KAAKG,CAAC,CAAC,EAAGM,CAAU,EACvC,OAAO,KAAK,KAAKN,CAAC,EAClB,OAAO,KAAK,MAAMA,CAAC,EACnB,EAAE,KAAK,UACT,CACA,OAAO,IACT,CACA,UAAUA,EAAGS,EAAQ,CACnB,GAAI,CAAC,KAAK,YACR,MAAM,IAAI,MAAM,2CAA2C,EAG7D,GAAMC,EAAYD,CAAM,EACtBA,EAASrB,MACJ,CAELqB,GAAU,GACV,QAASE,EAAWF,EAAQ,CAAGC,EAAYC,CAAQ,EAAGA,EAAW,KAAK,OAAOA,CAAQ,EACnF,GAAIA,IAAaX,EACf,MAAM,IAAI,MAAM,WAAaS,EAAS,iBAAmBT,EAAI,uBAAuB,EAIxF,KAAK,QAAQS,CAAM,CACrB,CAEA,YAAK,QAAQT,CAAC,EACd,KAAK,4BAA4BA,CAAC,EAClC,KAAK,QAAQA,CAAC,EAAIS,EAClB,KAAK,UAAUA,CAAM,EAAET,CAAC,EAAI,GACrB,IACT,CACA,4BAA4BA,EAAG,CAC7B,OAAO,KAAK,UAAU,KAAK,QAAQA,CAAC,CAAC,EAAEA,CAAC,CAC1C,CACA,OAAOA,EAAG,CACR,GAAI,KAAK,YAAa,CACpB,IAAIS,EAAS,KAAK,QAAQT,CAAC,EAC3B,GAAIS,IAAWrB,EACb,OAAOqB,CAEX,CACF,CACA,SAAST,EAAG,CAKV,GAJMU,EAAYV,CAAC,IACjBA,EAAIZ,GAGF,KAAK,YAAa,CACpB,IAAIwB,EAAW,KAAK,UAAUZ,CAAC,EAC/B,GAAIY,EACF,OAASf,EAAKe,CAAQ,CAE1B,KAAO,IAAIZ,IAAMZ,EACf,OAAO,KAAK,MAAM,EACb,GAAI,KAAK,QAAQY,CAAC,EACvB,MAAO,CAAC,EAEZ,CACA,aAAaA,EAAG,CACd,IAAIa,EAAS,KAAK,OAAOb,CAAC,EAC1B,GAAIa,EACF,OAAShB,EAAKgB,CAAM,CAExB,CACA,WAAWb,EAAG,CACZ,IAAIc,EAAQ,KAAK,MAAMd,CAAC,EACxB,GAAIc,EACF,OAASjB,EAAKiB,CAAK,CAEvB,CACA,UAAUd,EAAG,CACX,IAAIe,EAAQ,KAAK,aAAaf,CAAC,EAC/B,GAAIe,EACF,OAASC,EAAMD,EAAO,KAAK,WAAWf,CAAC,CAAC,CAE5C,CACA,OAAOA,EAAG,CACR,IAAIiB,EACJ,OAAI,KAAK,WAAW,EAClBA,EAAY,KAAK,WAAWjB,CAAC,EAE7BiB,EAAY,KAAK,UAAUjB,CAAC,EAEvBiB,EAAU,SAAW,CAC9B,CACA,YAAYC,EAAQ,CAElB,IAAIC,EAAO,IAAI,KAAK,YAAY,CAC9B,SAAU,KAAK,YACf,WAAY,KAAK,cACjB,SAAU,KAAK,WACjB,CAAC,EAEDA,EAAK,SAAS,KAAK,MAAM,CAAC,EAE1B,IAAIrB,EAAO,KACTO,EAAK,KAAK,OAAQ,SAAUF,EAAOH,EAAG,CAClCkB,EAAOlB,CAAC,GACVmB,EAAK,QAAQnB,EAAGG,CAAK,CAEzB,CAAC,EAECE,EAAK,KAAK,UAAW,SAAUE,EAAG,CAE9BY,EAAK,QAAQZ,EAAE,CAAC,GAAKY,EAAK,QAAQZ,EAAE,CAAC,GACvCY,EAAK,QAAQZ,EAAGT,EAAK,KAAKS,CAAC,CAAC,CAEhC,CAAC,EAED,IAAIa,EAAU,CAAC,EACf,SAASC,EAAWrB,EAAG,CACrB,IAAIS,EAASX,EAAK,OAAOE,CAAC,EAC1B,OAAIS,IAAW,QAAaU,EAAK,QAAQV,CAAM,GAC7CW,EAAQpB,CAAC,EAAIS,EACNA,GACEA,KAAUW,EACZA,EAAQX,CAAM,EAEdY,EAAWZ,CAAM,CAE5B,CAVS,OAAAlB,EAAA8B,EAAA,cAYL,KAAK,aACLhB,EAAKc,EAAK,MAAM,EAAG,SAAUnB,EAAG,CAChCmB,EAAK,UAAUnB,EAAGqB,EAAWrB,CAAC,CAAC,CACjC,CAAC,EAGImB,CACT,CAEA,oBAAoBxB,EAAY,CAC9B,OAAOC,EAAWD,CAAU,IAC1BA,EAAeF,EAASE,CAAU,GAEpC,KAAK,oBAAsBA,EACpB,IACT,CACA,WAAY,CACV,OAAO,KAAK,UACd,CACA,OAAQ,CACN,OAAS2B,EAAO,KAAK,SAAS,CAChC,CACA,QAAQpB,EAAIC,EAAO,CACjB,IAAIL,EAAO,KACPM,EAAO,UACX,OAAEmB,EAAOrB,EAAI,SAAUF,EAAGwB,EAAG,CAC3B,OAAIpB,EAAK,OAAS,EAChBN,EAAK,QAAQE,EAAGwB,EAAGrB,CAAK,EAExBL,EAAK,QAAQE,EAAGwB,CAAC,EAEZA,CACT,CAAC,EACM,IACT,CAKA,SAAU,CACR,IAAIxB,EAAGwB,EAAGC,EAAMtB,EACZuB,EAAiB,GACjBC,EAAO,UAAU,CAAC,EAElB,OAAOA,GAAS,UAAYA,IAAS,MAAQ,MAAOA,GACtD3B,EAAI2B,EAAK,EACTH,EAAIG,EAAK,EACTF,EAAOE,EAAK,KACR,UAAU,SAAW,IACvBxB,EAAQ,UAAU,CAAC,EACnBuB,EAAiB,MAGnB1B,EAAI2B,EACJH,EAAI,UAAU,CAAC,EACfC,EAAO,UAAU,CAAC,EACd,UAAU,OAAS,IACrBtB,EAAQ,UAAU,CAAC,EACnBuB,EAAiB,KAIrB1B,EAAI,GAAKA,EACTwB,EAAI,GAAKA,EACFd,EAAYe,CAAI,IACrBA,EAAO,GAAKA,GAGd,IAAIlB,EAAIqB,EAAa,KAAK,YAAa5B,EAAGwB,EAAGC,CAAI,EACjD,GAAI,OAAO,UAAU,eAAe,KAAK,KAAK,YAAalB,CAAC,EAC1D,OAAImB,IACF,KAAK,YAAYnB,CAAC,EAAIJ,GAEjB,KAGT,GAAI,CAAGO,EAAYe,CAAI,GAAK,CAAC,KAAK,cAChC,MAAM,IAAI,MAAM,mDAAmD,EAKrE,KAAK,QAAQzB,CAAC,EACd,KAAK,QAAQwB,CAAC,EAGd,KAAK,YAAYjB,CAAC,EAAImB,EAAiBvB,EAAQ,KAAK,oBAAoBH,EAAGwB,EAAGC,CAAI,EAElF,IAAII,EAAUC,EAAc,KAAK,YAAa9B,EAAGwB,EAAGC,CAAI,EAExD,OAAAzB,EAAI6B,EAAQ,EACZL,EAAIK,EAAQ,EAEZ,OAAO,OAAOA,CAAO,EACrB,KAAK,UAAUtB,CAAC,EAAIsB,EACpBE,EAAqB,KAAK,OAAOP,CAAC,EAAGxB,CAAC,EACtC+B,EAAqB,KAAK,MAAM/B,CAAC,EAAGwB,CAAC,EACrC,KAAK,IAAIA,CAAC,EAAEjB,CAAC,EAAIsB,EACjB,KAAK,KAAK7B,CAAC,EAAEO,CAAC,EAAIsB,EAClB,KAAK,aACE,IACT,CACA,KAAK7B,EAAGwB,EAAGC,EAAM,CACf,IAAIlB,EACF,UAAU,SAAW,EACjByB,EAAY,KAAK,YAAa,UAAU,CAAC,CAAC,EAC1CJ,EAAa,KAAK,YAAa5B,EAAGwB,EAAGC,CAAI,EAC/C,OAAO,KAAK,YAAYlB,CAAC,CAC3B,CACA,QAAQP,EAAGwB,EAAGC,EAAM,CAClB,IAAIlB,EACF,UAAU,SAAW,EACjByB,EAAY,KAAK,YAAa,UAAU,CAAC,CAAC,EAC1CJ,EAAa,KAAK,YAAa5B,EAAGwB,EAAGC,CAAI,EAC/C,OAAO,OAAO,UAAU,eAAe,KAAK,KAAK,YAAalB,CAAC,CACjE,CACA,WAAWP,EAAGwB,EAAGC,EAAM,CACrB,IAAIlB,EACF,UAAU,SAAW,EACjByB,EAAY,KAAK,YAAa,UAAU,CAAC,CAAC,EAC1CJ,EAAa,KAAK,YAAa5B,EAAGwB,EAAGC,CAAI,EAC3CQ,EAAO,KAAK,UAAU1B,CAAC,EAC3B,OAAI0B,IACFjC,EAAIiC,EAAK,EACTT,EAAIS,EAAK,EACT,OAAO,KAAK,YAAY1B,CAAC,EACzB,OAAO,KAAK,UAAUA,CAAC,EACvB2B,EAAuB,KAAK,OAAOV,CAAC,EAAGxB,CAAC,EACxCkC,EAAuB,KAAK,MAAMlC,CAAC,EAAGwB,CAAC,EACvC,OAAO,KAAK,IAAIA,CAAC,EAAEjB,CAAC,EACpB,OAAO,KAAK,KAAKP,CAAC,EAAEO,CAAC,EACrB,KAAK,cAEA,IACT,CACA,QAAQP,EAAGmC,EAAG,CACZ,IAAIC,EAAM,KAAK,IAAIpC,CAAC,EACpB,GAAIoC,EAAK,CACP,IAAIC,EAAUf,EAAOc,CAAG,EACxB,OAAKD,EAGIpC,EAAOsC,EAAO,SAAUJ,EAAM,CACrC,OAAOA,EAAK,IAAME,CACpB,CAAC,EAJQE,CAKX,CACF,CACA,SAASrC,EAAGwB,EAAG,CACb,IAAIc,EAAO,KAAK,KAAKtC,CAAC,EACtB,GAAIsC,EAAM,CACR,IAAID,EAAUf,EAAOgB,CAAI,EACzB,OAAKd,EAGIzB,EAAOsC,EAAO,SAAUJ,EAAM,CACrC,OAAOA,EAAK,IAAMT,CACpB,CAAC,EAJQa,CAKX,CACF,CACA,UAAUrC,EAAGwB,EAAG,CACd,IAAIe,EAAU,KAAK,QAAQvC,EAAGwB,CAAC,EAC/B,GAAIe,EACF,OAAOA,EAAQ,OAAO,KAAK,SAASvC,EAAGwB,CAAC,CAAC,CAE7C,CACF,EAGAlC,EAAM,UAAU,WAAa,EAG7BA,EAAM,UAAU,WAAa,EAE7B,SAASyC,EAAqBS,EAAKC,EAAG,CAChCD,EAAIC,CAAC,EACPD,EAAIC,CAAC,IAELD,EAAIC,CAAC,EAAI,CAEb,CANSlD,EAAAwC,EAAA,wBAQT,SAASG,EAAuBM,EAAKC,EAAG,CACjC,EAAED,EAAIC,CAAC,GACV,OAAOD,EAAIC,CAAC,CAEhB,CAJSlD,EAAA2C,EAAA,0BAMT,SAASN,EAAac,EAAYC,EAAIC,EAAInB,EAAM,CAC9C,IAAIzB,EAAI,GAAK2C,EACTnB,EAAI,GAAKoB,EACb,GAAI,CAACF,GAAc1C,EAAIwB,EAAG,CACxB,IAAIqB,EAAM7C,EACVA,EAAIwB,EACJA,EAAIqB,CACN,CACA,OAAO7C,EAAIX,EAAiBmC,EAAInC,GAAoBqB,EAAYe,CAAI,EAAItC,EAAoBsC,EAC9F,CATSlC,EAAAqC,EAAA,gBAWT,SAASE,EAAcY,EAAYC,EAAIC,EAAInB,EAAM,CAC/C,IAAIzB,EAAI,GAAK2C,EACTnB,EAAI,GAAKoB,EACb,GAAI,CAACF,GAAc1C,EAAIwB,EAAG,CACxB,IAAIqB,EAAM7C,EACVA,EAAIwB,EACJA,EAAIqB,CACN,CACA,IAAIhB,EAAU,CAAE,EAAG7B,EAAG,EAAGwB,CAAE,EAC3B,OAAIC,IACFI,EAAQ,KAAOJ,GAEVI,CACT,CAbStC,EAAAuC,EAAA,iBAeT,SAASE,EAAYU,EAAYb,EAAS,CACxC,OAAOD,EAAac,EAAYb,EAAQ,EAAGA,EAAQ,EAAGA,EAAQ,IAAI,CACpE,CAFStC,EAAAyC,EAAA", "names": ["DEFAULT_EDGE_NAME", "GRAPH_NODE", "EDGE_KEY_DELIM", "Graph", "__name", "opts", "constant_default", "label", "newDefault", "isFunction_default", "keys_default", "self", "filter_default", "v", "isEmpty_default", "vs", "value", "args", "forEach_default", "removeEdge", "e", "child", "parent", "isUndefined_default", "ancestor", "children", "predsV", "sucsV", "preds", "union_default", "neighbors", "filter", "copy", "parents", "findParent", "values_default", "reduce_default", "w", "name", "valueSpecified", "arg0", "edgeArgsToId", "edgeObj", "edgeArgsToObj", "incrementOrInitEntry", "edgeObjToId", "edge", "decrementOrRemoveEntry", "u", "inV", "edges", "outV", "inEdges", "map", "k", "isDirected", "v_", "w_", "tmp"]}