{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-KMC2YHZD.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  MermaidGeneratedSharedModule,\n  PacketGeneratedModule,\n  __name\n} from \"./chunk-4KMFLZZN.mjs\";\n\n// src/language/packet/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/packet/tokenBuilder.ts\nvar PacketTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PacketTokenBuilder\");\n  }\n  constructor() {\n    super([\"packet\"]);\n  }\n};\n\n// src/language/packet/module.ts\nvar PacketModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PacketTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPacketServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Packet = inject(\n    createDefaultCoreModule({ shared }),\n    PacketGeneratedModule,\n    PacketModule\n  );\n  shared.ServiceRegistry.register(Packet);\n  return { shared, Packet };\n}\n__name(createPacketServices, \"createPacketServices\");\n\nexport {\n  PacketModule,\n  createPacketServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,qBAAqB,cAAc,4BAA4B;AAAA,EAjBnE,OAiBmE;AAAA;AAAA;AAAA,EACjE,OAAO;AACL,IAAAA,QAAO,MAAM,oBAAoB;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,QAAQ,CAAC;AAAA,EAClB;AACF;AAGA,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,mBAAmB,GAAG,cAAc;AAAA,IACnF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,qBAAqB,UAAU,iBAAiB;AACvD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,MAAM;AACtC,SAAO,EAAE,QAAQ,OAAO;AAC1B;AAZS;AAaTA,QAAO,sBAAsB,sBAAsB;", "names": ["__name"]}