{"version": 3, "sources": ["../../../src/diagrams/git/gitGraphTypes.ts", "../../../src/diagrams/git/gitGraphAst.ts", "../../../src/diagrams/git/gitGraphParser.ts", "../../../src/diagrams/git/gitGraphRenderer.ts", "../../../src/diagrams/git/styles.js", "../../../src/diagrams/git/gitGraphDiagram.ts"], "sourcesContent": ["import type { GitGraphDiagramConfig } from '../../config.type.js';\nimport type { DiagramDBBase } from '../../diagram-api/types.js';\n\nexport const commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4,\n} as const;\n\nexport interface CommitDB {\n  msg: string;\n  id: string;\n  type: number;\n  tags?: string[];\n}\n\nexport interface BranchDB {\n  name: string;\n  order: number;\n}\n\nexport interface MergeDB {\n  branch: string;\n  id: string;\n  type?: number;\n  tags?: string[];\n}\n\nexport interface CherryPickDB {\n  id: string;\n  targetId: string;\n  parent: string;\n  tags?: string[];\n}\n\nexport interface Commit {\n  id: string;\n  message: string;\n  seq: number;\n  type: number;\n  tags: string[];\n  parents: string[];\n  branch: string;\n  customType?: number;\n  customId?: boolean;\n}\n\nexport interface GitGraph {\n  statements: Statement[];\n}\n\nexport type Statement = CommitAst | BranchAst | MergeAst | CheckoutAst | CherryPickingAst;\n\nexport interface CommitAst {\n  $type: 'Commit';\n  id: string;\n  message?: string;\n  tags?: string[];\n  type?: 'NORMAL' | 'REVERSE' | 'HIGHLIGHT';\n}\n\nexport interface BranchAst {\n  $type: 'Branch';\n  name: string;\n  order?: number;\n}\n\nexport interface MergeAst {\n  $type: 'Merge';\n  branch: string;\n  id?: string;\n  tags?: string[];\n  type?: 'NORMAL' | 'REVERSE' | 'HIGHLIGHT';\n}\n\nexport interface CheckoutAst {\n  $type: 'Checkout';\n  branch: string;\n}\n\nexport interface CherryPickingAst {\n  $type: 'CherryPicking';\n  id: string;\n  parent: string;\n  tags?: string[];\n}\n\nexport interface GitGraphDB extends DiagramDBBase<GitGraphDiagramConfig> {\n  commitType: typeof commitType;\n  setDirection: (dir: DiagramOrientation) => void;\n  setOptions: (rawOptString: string) => void;\n  getOptions: () => any;\n  commit: (commitDB: CommitDB) => void;\n  branch: (branchDB: BranchDB) => void;\n  merge: (mergeDB: MergeDB) => void;\n  cherryPick: (cherryPickDB: CherryPickDB) => void;\n  checkout: (branch: string) => void;\n  prettyPrint: () => void;\n  clear: () => void;\n  getBranchesAsObjArray: () => { name: string }[];\n  getBranches: () => Map<string, string | null>;\n  getCommits: () => Map<string, Commit>;\n  getCommitsArray: () => Commit[];\n  getCurrentBranch: () => string;\n  getDirection: () => DiagramOrientation;\n  getHead: () => Commit | null;\n}\n\nexport interface GitGraphDBParseProvider extends Partial<GitGraphDB> {\n  commitType: typeof commitType;\n  setDirection: (dir: DiagramOrientation) => void;\n  commit: (commitDB: CommitDB) => void;\n  branch: (branchDB: BranchDB) => void;\n  merge: (mergeDB: MergeDB) => void;\n  cherryPick: (cherryPickDB: CherryPickDB) => void;\n  checkout: (branch: string) => void;\n}\n\nexport interface GitGraphDBRenderProvider extends Partial<GitGraphDB> {\n  prettyPrint: () => void;\n  clear: () => void;\n  getBranchesAsObjArray: () => { name: string }[];\n  getBranches: () => Map<string, string | null>;\n  getCommits: () => Map<string, Commit>;\n  getCommitsArray: () => Commit[];\n  getCurrentBranch: () => string;\n  getDirection: () => DiagramOrientation;\n  getHead: () => Commit | null;\n  getDiagramTitle: () => string;\n}\n\nexport type DiagramOrientation = 'LR' | 'TB' | 'BT';\n", "import { log } from '../../logger.js';\nimport { cleanAndMerge, random } from '../../utils.js';\nimport { getConfig as commonGetConfig } from '../../config.js';\nimport common from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n  setDiagramTitle,\n  getDiagramTitle,\n} from '../common/commonDb.js';\nimport type {\n  DiagramOrientation,\n  Commit,\n  GitGraphDB,\n  CommitDB,\n  MergeDB,\n  BranchDB,\n  CherryPickDB,\n} from './gitGraphTypes.js';\nimport { commitType } from './gitGraphTypes.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\n\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\n\nimport type { GitGraphDiagramConfig } from '../../config.type.js';\ninterface GitGraphState {\n  commits: Map<string, Commit>;\n  head: Commit | null;\n  branchConfig: Map<string, { name: string; order: number | undefined }>;\n  branches: Map<string, string | null>;\n  currBranch: string;\n  direction: DiagramOrientation;\n  seq: number;\n  options: any;\n}\n\nconst DEFAULT_GITGRAPH_CONFIG: Required<GitGraphDiagramConfig> = DEFAULT_CONFIG.gitGraph;\nconst getConfig = (): Required<GitGraphDiagramConfig> => {\n  const config = cleanAndMerge({\n    ...DEFAULT_GITGRAPH_CONFIG,\n    ...commonGetConfig().gitGraph,\n  });\n  return config;\n};\n\nconst state = new ImperativeState<GitGraphState>(() => {\n  const config = getConfig();\n  const mainBranchName = config.mainBranchName;\n  const mainBranchOrder = config.mainBranchOrder;\n  return {\n    mainBranchName,\n    commits: new Map(),\n    head: null,\n    branchConfig: new Map([[mainBranchName, { name: mainBranchName, order: mainBranchOrder }]]),\n    branches: new Map([[mainBranchName, null]]),\n    currBranch: mainBranchName,\n    direction: 'LR',\n    seq: 0,\n    options: {},\n  };\n});\n\nfunction getID() {\n  return random({ length: 7 });\n}\n\n/**\n * @param list - list of items\n * @param fn -  function to get the key\n */\nfunction uniqBy(list: any[], fn: (item: any) => any) {\n  const recordMap = Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\n\nexport const setDirection = function (dir: DiagramOrientation) {\n  state.records.direction = dir;\n};\n\nexport const setOptions = function (rawOptString: string) {\n  log.debug('options str', rawOptString);\n  rawOptString = rawOptString?.trim();\n  rawOptString = rawOptString || '{}';\n  try {\n    state.records.options = JSON.parse(rawOptString);\n  } catch (e: any) {\n    log.error('error while parsing gitGraph options', e.message);\n  }\n};\n\nexport const getOptions = function () {\n  return state.records.options;\n};\n\nexport const commit = function (commitDB: CommitDB) {\n  let msg = commitDB.msg;\n  let id = commitDB.id;\n  const type = commitDB.type;\n  let tags = commitDB.tags;\n\n  log.info('commit', msg, id, type, tags);\n  log.debug('Entering commit:', msg, id, type, tags);\n  const config = getConfig();\n  id = common.sanitizeText(id, config);\n  msg = common.sanitizeText(msg, config);\n  tags = tags?.map((tag) => common.sanitizeText(tag, config));\n  const newCommit: Commit = {\n    id: id ? id : state.records.seq + '-' + getID(),\n    message: msg,\n    seq: state.records.seq++,\n    type: type ?? commitType.NORMAL,\n    tags: tags ?? [],\n    parents: state.records.head == null ? [] : [state.records.head.id],\n    branch: state.records.currBranch,\n  };\n  state.records.head = newCommit;\n  log.info('main branch', config.mainBranchName);\n  if (state.records.commits.has(newCommit.id)) {\n    log.warn(`Commit ID ${newCommit.id} already exists`);\n  }\n  state.records.commits.set(newCommit.id, newCommit);\n  state.records.branches.set(state.records.currBranch, newCommit.id);\n  log.debug('in pushCommit ' + newCommit.id);\n};\n\nexport const branch = function (branchDB: BranchDB) {\n  let name = branchDB.name;\n  const order = branchDB.order;\n  name = common.sanitizeText(name, getConfig());\n  if (state.records.branches.has(name)) {\n    throw new Error(\n      `Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ${name}\")`\n    );\n  }\n\n  state.records.branches.set(name, state.records.head != null ? state.records.head.id : null);\n  state.records.branchConfig.set(name, { name, order });\n  checkout(name);\n  log.debug('in createBranch');\n};\n\nexport const merge = (mergeDB: MergeDB): void => {\n  let otherBranch = mergeDB.branch;\n  let customId = mergeDB.id;\n  const overrideType = mergeDB.type;\n  const customTags = mergeDB.tags;\n  const config = getConfig();\n  otherBranch = common.sanitizeText(otherBranch, config);\n  if (customId) {\n    customId = common.sanitizeText(customId, config);\n  }\n  const currentBranchCheck = state.records.branches.get(state.records.currBranch);\n  const otherBranchCheck = state.records.branches.get(otherBranch);\n  const currentCommit = currentBranchCheck\n    ? state.records.commits.get(currentBranchCheck)\n    : undefined;\n  const otherCommit: Commit | undefined = otherBranchCheck\n    ? state.records.commits.get(otherBranchCheck)\n    : undefined;\n  if (currentCommit && otherCommit && currentCommit.branch === otherBranch) {\n    throw new Error(`Cannot merge branch '${otherBranch}' into itself.`);\n  }\n  if (state.records.currBranch === otherBranch) {\n    const error: any = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['branch abc'],\n    };\n    throw error;\n  }\n  if (currentCommit === undefined || !currentCommit) {\n    const error: any = new Error(\n      `Incorrect usage of \"merge\". Current branch (${state.records.currBranch})has no commits`\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['commit'],\n    };\n    throw error;\n  }\n  if (!state.records.branches.has(otherBranch)) {\n    const error: any = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + ') does not exist'\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [`branch ${otherBranch}`],\n    };\n    throw error;\n  }\n  if (otherCommit === undefined || !otherCommit) {\n    const error: any = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + ') has no commits'\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['\"commit\"'],\n    };\n    throw error;\n  }\n  if (currentCommit === otherCommit) {\n    const error: any = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['branch abc'],\n    };\n    throw error;\n  }\n  if (customId && state.records.commits.has(customId)) {\n    const error: any = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' +\n        customId +\n        ' already exists, use different custom id'\n    );\n    error.hash = {\n      text: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(' ')}`,\n      token: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(' ')}`,\n      expected: [\n        `merge ${otherBranch} ${customId}_UNIQUE ${overrideType} ${customTags?.join(' ')}`,\n      ],\n    };\n\n    throw error;\n  }\n\n  const verifiedBranch: string = otherBranchCheck ? otherBranchCheck : ''; //figure out a cleaner way to do this\n\n  const commit = {\n    id: customId || `${state.records.seq}-${getID()}`,\n    message: `merged branch ${otherBranch} into ${state.records.currBranch}`,\n    seq: state.records.seq++,\n    parents: state.records.head == null ? [] : [state.records.head.id, verifiedBranch],\n    branch: state.records.currBranch,\n    type: commitType.MERGE,\n    customType: overrideType,\n    customId: customId ? true : false,\n    tags: customTags ?? [],\n  } satisfies Commit;\n  state.records.head = commit;\n  state.records.commits.set(commit.id, commit);\n  state.records.branches.set(state.records.currBranch, commit.id);\n  log.debug(state.records.branches);\n  log.debug('in mergeBranch');\n};\n\nexport const cherryPick = function (cherryPickDB: CherryPickDB) {\n  let sourceId = cherryPickDB.id;\n  let targetId = cherryPickDB.targetId;\n  let tags = cherryPickDB.tags;\n  let parentCommitId = cherryPickDB.parent;\n  log.debug('Entering cherryPick:', sourceId, targetId, tags);\n  const config = getConfig();\n  sourceId = common.sanitizeText(sourceId, config);\n  targetId = common.sanitizeText(targetId, config);\n\n  tags = tags?.map((tag) => common.sanitizeText(tag, config));\n\n  parentCommitId = common.sanitizeText(parentCommitId, config);\n\n  if (!sourceId || !state.records.commits.has(sourceId)) {\n    const error: any = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: `cherryPick ${sourceId} ${targetId}`,\n      token: `cherryPick ${sourceId} ${targetId}`,\n      expected: ['cherry-pick abc'],\n    };\n    throw error;\n  }\n\n  const sourceCommit = state.records.commits.get(sourceId);\n  if (sourceCommit === undefined || !sourceCommit) {\n    throw new Error('Incorrect usage of \"cherryPick\". Source commit id should exist and provided');\n  }\n  if (\n    parentCommitId &&\n    !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))\n  ) {\n    const error = new Error(\n      'Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.'\n    );\n    throw error;\n  }\n  const sourceCommitBranch = sourceCommit.branch;\n  if (sourceCommit.type === commitType.MERGE && !parentCommitId) {\n    const error = new Error(\n      'Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.'\n    );\n    throw error;\n  }\n  if (!targetId || !state.records.commits.has(targetId)) {\n    // cherry-pick source commit to current branch\n\n    if (sourceCommitBranch === state.records.currBranch) {\n      const error: any = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: ['cherry-pick abc'],\n      };\n      throw error;\n    }\n    const currentCommitId = state.records.branches.get(state.records.currBranch);\n    if (currentCommitId === undefined || !currentCommitId) {\n      const error: any = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: ['cherry-pick abc'],\n      };\n      throw error;\n    }\n\n    const currentCommit = state.records.commits.get(currentCommitId);\n    if (currentCommit === undefined || !currentCommit) {\n      const error: any = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: ['cherry-pick abc'],\n      };\n      throw error;\n    }\n    const commit = {\n      id: state.records.seq + '-' + getID(),\n      message: `cherry-picked ${sourceCommit?.message} into ${state.records.currBranch}`,\n      seq: state.records.seq++,\n      parents: state.records.head == null ? [] : [state.records.head.id, sourceCommit.id],\n      branch: state.records.currBranch,\n      type: commitType.CHERRY_PICK,\n      tags: tags\n        ? tags.filter(Boolean)\n        : [\n            `cherry-pick:${sourceCommit.id}${\n              sourceCommit.type === commitType.MERGE ? `|parent:${parentCommitId}` : ''\n            }`,\n          ],\n    };\n\n    state.records.head = commit;\n    state.records.commits.set(commit.id, commit);\n    state.records.branches.set(state.records.currBranch, commit.id);\n    log.debug(state.records.branches);\n    log.debug('in cherryPick');\n  }\n};\nexport const checkout = function (branch: string) {\n  branch = common.sanitizeText(branch, getConfig());\n  if (!state.records.branches.has(branch)) {\n    const error: any = new Error(\n      `Trying to checkout branch which is not yet created. (Help try using \"branch ${branch}\")`\n    );\n    error.hash = {\n      text: `checkout ${branch}`,\n      token: `checkout ${branch}`,\n      expected: [`branch ${branch}`],\n    };\n    throw error;\n  } else {\n    state.records.currBranch = branch;\n    const id = state.records.branches.get(state.records.currBranch);\n    if (id === undefined || !id) {\n      state.records.head = null;\n    } else {\n      state.records.head = state.records.commits.get(id) ?? null;\n    }\n  }\n};\n\n/**\n * @param arr - array\n * @param key - key\n * @param newVal - new value\n */\nfunction upsert(arr: any[], key: any, newVal: any) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\n\nfunction prettyPrintCommitHistory(commitArr: Commit[]) {\n  const commit = commitArr.reduce((out, commit) => {\n    if (out.seq > commit.seq) {\n      return out;\n    }\n    return commit;\n  }, commitArr[0]);\n  let line = '';\n  commitArr.forEach(function (c) {\n    if (c === commit) {\n      line += '\\t*';\n    } else {\n      line += '\\t|';\n    }\n  });\n  const label = [line, commit.id, commit.seq];\n  for (const branch in state.records.branches) {\n    if (state.records.branches.get(branch) === commit.id) {\n      label.push(branch);\n    }\n  }\n  log.debug(label.join(' '));\n  if (commit.parents && commit.parents.length == 2 && commit.parents[0] && commit.parents[1]) {\n    const newCommit = state.records.commits.get(commit.parents[0]);\n    upsert(commitArr, commit, newCommit);\n    if (commit.parents[1]) {\n      commitArr.push(state.records.commits.get(commit.parents[1])!);\n    }\n  } else if (commit.parents.length == 0) {\n    return;\n  } else {\n    if (commit.parents[0]) {\n      const newCommit = state.records.commits.get(commit.parents[0]);\n      upsert(commitArr, commit, newCommit);\n    }\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\n\nexport const prettyPrint = function () {\n  log.debug(state.records.commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n};\n\nexport const clear = function () {\n  state.reset();\n  commonClear();\n};\n\nexport const getBranchesAsObjArray = function () {\n  const branchesArray = [...state.records.branchConfig.values()]\n    .map((branchConfig, i) => {\n      if (branchConfig.order !== null && branchConfig.order !== undefined) {\n        return branchConfig;\n      }\n      return {\n        ...branchConfig,\n        order: parseFloat(`0.${i}`),\n      };\n    })\n    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))\n    .map(({ name }) => ({ name }));\n\n  return branchesArray;\n};\n\nexport const getBranches = function () {\n  return state.records.branches;\n};\nexport const getCommits = function () {\n  return state.records.commits;\n};\nexport const getCommitsArray = function () {\n  const commitArr = [...state.records.commits.values()];\n  commitArr.forEach(function (o) {\n    log.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n};\nexport const getCurrentBranch = function () {\n  return state.records.currBranch;\n};\nexport const getDirection = function () {\n  return state.records.direction;\n};\nexport const getHead = function () {\n  return state.records.head;\n};\n\nexport const db: GitGraphDB = {\n  commitType,\n  getConfig,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle,\n};\n", "import type { GitGraph } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport { db } from './gitGraphAst.js';\nimport { commitType } from './gitGraphTypes.js';\nimport type {\n  CheckoutAst,\n  CherryPickingAst,\n  MergeAst,\n  CommitAst,\n  BranchAst,\n  GitGraphDBParseProvider,\n  CommitDB,\n  BranchDB,\n  MergeDB,\n  CherryPickDB,\n} from './gitGraphTypes.js';\n\nconst populate = (ast: GitGraph, db: GitGraphDBParseProvider) => {\n  populateCommonDb(ast, db);\n  // @ts-ignore: this wont exist if the direction is not specified\n  if (ast.dir) {\n    // @ts-ignore: this wont exist if the direction is not specified\n    db.setDirection(ast.dir);\n  }\n  for (const statement of ast.statements) {\n    parseStatement(statement, db);\n  }\n};\n\nconst parseStatement = (statement: any, db: GitGraphDBParseProvider) => {\n  const parsers: Record<string, (stmt: any) => void> = {\n    Commit: (stmt) => db.commit(parseCommit(stmt)),\n    Branch: (stmt) => db.branch(parseBranch(stmt)),\n    Merge: (stmt) => db.merge(parseMerge(stmt)),\n    Checkout: (stmt) => db.checkout(parseCheckout(stmt)),\n    CherryPicking: (stmt) => db.cherryPick(parseCherryPicking(stmt)),\n  };\n\n  const parser = parsers[statement.$type];\n  if (parser) {\n    parser(statement);\n  } else {\n    log.error(`Unknown statement type: ${statement.$type}`);\n  }\n};\n\nconst parseCommit = (commit: CommitAst): CommitDB => {\n  const commitDB: CommitDB = {\n    id: commit.id,\n    msg: commit.message ?? '',\n    type: commit.type !== undefined ? commitType[commit.type] : commitType.NORMAL,\n    tags: commit.tags ?? undefined,\n  };\n  return commitDB;\n};\n\nconst parseBranch = (branch: BranchAst): BranchDB => {\n  const branchDB: BranchDB = {\n    name: branch.name,\n    order: branch.order ?? 0,\n  };\n  return branchDB;\n};\n\nconst parseMerge = (merge: MergeAst): MergeDB => {\n  const mergeDB: MergeDB = {\n    branch: merge.branch,\n    id: merge.id ?? '',\n    type: merge.type !== undefined ? commitType[merge.type] : undefined,\n    tags: merge.tags ?? undefined,\n  };\n  return mergeDB;\n};\n\nconst parseCheckout = (checkout: CheckoutAst): string => {\n  const branch = checkout.branch;\n  return branch;\n};\n\nconst parseCherryPicking = (cherryPicking: CherryPickingAst): CherryPickDB => {\n  const cherryPickDB: CherryPickDB = {\n    id: cherryPicking.id,\n    targetId: '',\n    tags: cherryPicking.tags?.length === 0 ? undefined : cherryPicking.tags,\n    parent: cherryPicking.parent,\n  };\n  return cherryPickDB;\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: GitGraph = await parse('gitGraph', input);\n    log.debug(ast);\n    populate(ast, db);\n  },\n};\n\nif (import.meta.vitest) {\n  const { it, expect, describe } = import.meta.vitest;\n\n  const mockDB: GitGraphDBParseProvider = {\n    commitType: commitType,\n    setDirection: vi.fn(),\n    commit: vi.fn(),\n    branch: vi.fn(),\n    merge: vi.fn(),\n    cherryPick: vi.fn(),\n    checkout: vi.fn(),\n  };\n\n  describe('GitGraph Parser', () => {\n    it('should parse a commit statement', () => {\n      const commit = {\n        $type: 'Commit',\n        id: '1',\n        message: 'test',\n        tags: ['tag1', 'tag2'],\n        type: 'NORMAL',\n      };\n      parseStatement(commit, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: '1',\n        msg: 'test',\n        tags: ['tag1', 'tag2'],\n        type: 0,\n      });\n    });\n    it('should parse a branch statement', () => {\n      const branch = {\n        $type: 'Branch',\n        name: 'newBranch',\n        order: 1,\n      };\n      parseStatement(branch, mockDB);\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: 'newBranch', order: 1 });\n    });\n    it('should parse a checkout statement', () => {\n      const checkout = {\n        $type: 'Checkout',\n        branch: 'newBranch',\n      };\n      parseStatement(checkout, mockDB);\n      expect(mockDB.checkout).toHaveBeenCalledWith('newBranch');\n    });\n    it('should parse a merge statement', () => {\n      const merge = {\n        $type: 'Merge',\n        branch: 'newBranch',\n        id: '1',\n        tags: ['tag1', 'tag2'],\n        type: 'NORMAL',\n      };\n      parseStatement(merge, mockDB);\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: 'newBranch',\n        id: '1',\n        tags: ['tag1', 'tag2'],\n        type: 0,\n      });\n    });\n    it('should parse a cherry picking statement', () => {\n      const cherryPick = {\n        $type: 'CherryPicking',\n        id: '1',\n        tags: ['tag1', 'tag2'],\n        parent: '2',\n      };\n      parseStatement(cherryPick, mockDB);\n      expect(mockDB.cherryPick).toHaveBeenCalledWith({\n        id: '1',\n        targetId: '',\n        parent: '2',\n        tags: ['tag1', 'tag2'],\n      });\n    });\n\n    it('should parse a langium generated gitGraph ast', () => {\n      const dummy: GitGraph = {\n        $type: 'GitGraph',\n        statements: [],\n      };\n      const gitGraphAst: GitGraph = {\n        $type: 'GitGraph',\n        statements: [\n          {\n            $container: dummy,\n            $type: 'Commit',\n            id: '1',\n            message: 'test',\n            tags: ['tag1', 'tag2'],\n            type: 'NORMAL',\n          },\n          {\n            $container: dummy,\n            $type: 'Branch',\n            name: 'newBranch',\n            order: 1,\n          },\n          {\n            $container: dummy,\n            $type: 'Merge',\n            branch: 'newBranch',\n            id: '1',\n            tags: ['tag1', 'tag2'],\n            type: 'NORMAL',\n          },\n          {\n            $container: dummy,\n            $type: 'Checkout',\n            branch: 'newBranch',\n          },\n          {\n            $container: dummy,\n            $type: 'CherryPicking',\n            id: '1',\n            tags: ['tag1', 'tag2'],\n            parent: '2',\n          },\n        ],\n      };\n\n      populate(gitGraphAst, mockDB);\n\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: '1',\n        msg: 'test',\n        tags: ['tag1', 'tag2'],\n        type: 0,\n      });\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: 'newBranch', order: 1 });\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: 'newBranch',\n        id: '1',\n        tags: ['tag1', 'tag2'],\n        type: 0,\n      });\n      expect(mockDB.checkout).toHaveBeenCalledWith('newBranch');\n    });\n  });\n}\n", "import { select } from 'd3';\nimport { getConfig, setupGraphViewbox } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport utils from '../../utils.js';\nimport type { DrawDefinition } from '../../diagram-api/types.js';\nimport type d3 from 'd3';\nimport type { Commit, GitGraphDBRenderProvider, DiagramOrientation } from './gitGraphTypes.js';\nimport { commitType } from './gitGraphTypes.js';\n\ninterface BranchPosition {\n  pos: number;\n  index: number;\n}\n\ninterface CommitPosition {\n  x: number;\n  y: number;\n}\n\ninterface CommitPositionOffset extends CommitPosition {\n  posWithOffset: number;\n}\n\nconst DEFAULT_CONFIG = getConfig();\nconst DEFAULT_GITGRAPH_CONFIG = DEFAULT_CONFIG?.gitGraph;\nconst LAYOUT_OFFSET = 10;\nconst COMMIT_STEP = 40;\nconst PX = 4;\nconst PY = 2;\n\nconst THEME_COLOR_LIMIT = 8;\nconst branchPos = new Map<string, BranchPosition>();\nconst commitPos = new Map<string, CommitPosition>();\nconst defaultPos = 30;\n\nlet allCommitsDict = new Map();\nlet lanes: number[] = [];\nlet maxPos = 0;\nlet dir: DiagramOrientation = 'LR';\n\nconst clear = () => {\n  branchPos.clear();\n  commitPos.clear();\n  allCommitsDict.clear();\n  maxPos = 0;\n  lanes = [];\n  dir = 'LR';\n};\n\nconst drawText = (txt: string | string[]) => {\n  const svgLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n  const rows = typeof txt === 'string' ? txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi) : txt;\n\n  rows.forEach((row) => {\n    const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n    tspan.setAttributeNS('http://www.w3.org/XML/1998/namespace', 'xml:space', 'preserve');\n    tspan.setAttribute('dy', '1em');\n    tspan.setAttribute('x', '0');\n    tspan.setAttribute('class', 'row');\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  });\n\n  return svgLabel;\n};\n\nconst findClosestParent = (parents: string[]): string | undefined => {\n  let closestParent: string | undefined;\n  let comparisonFunc;\n  let targetPosition: number;\n  if (dir === 'BT') {\n    comparisonFunc = (a: number, b: number) => a <= b;\n    targetPosition = Infinity;\n  } else {\n    comparisonFunc = (a: number, b: number) => a >= b;\n    targetPosition = 0;\n  }\n\n  parents.forEach((parent) => {\n    const parentPosition =\n      dir === 'TB' || dir == 'BT' ? commitPos.get(parent)?.y : commitPos.get(parent)?.x;\n\n    if (parentPosition !== undefined && comparisonFunc(parentPosition, targetPosition)) {\n      closestParent = parent;\n      targetPosition = parentPosition;\n    }\n  });\n\n  return closestParent;\n};\n\nconst findClosestParentBT = (parents: string[]) => {\n  let closestParent = '';\n  let maxPosition = Infinity;\n\n  parents.forEach((parent) => {\n    const parentPosition = commitPos.get(parent)!.y;\n    if (parentPosition <= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || undefined;\n};\n\nconst setParallelBTPos = (\n  sortedKeys: string[],\n  commits: Map<string, Commit>,\n  defaultPos: number\n) => {\n  let curPos = defaultPos;\n  let maxPosition = defaultPos;\n  const roots: Commit[] = [];\n\n  sortedKeys.forEach((key) => {\n    const commit = commits.get(key);\n    if (!commit) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n\n    if (commit.parents.length) {\n      curPos = calculateCommitPosition(commit);\n      maxPosition = Math.max(curPos, maxPosition);\n    } else {\n      roots.push(commit);\n    }\n    setCommitPosition(commit, curPos);\n  });\n\n  curPos = maxPosition;\n  roots.forEach((commit) => {\n    setRootPosition(commit, curPos, defaultPos);\n  });\n  sortedKeys.forEach((key) => {\n    const commit = commits.get(key);\n\n    if (commit?.parents.length) {\n      const closestParent = findClosestParentBT(commit.parents)!;\n      curPos = commitPos.get(closestParent)!.y - COMMIT_STEP;\n      if (curPos <= maxPosition) {\n        maxPosition = curPos;\n      }\n      const x = branchPos.get(commit.branch)!.pos;\n      const y = curPos - LAYOUT_OFFSET;\n      commitPos.set(commit.id, { x: x, y: y });\n    }\n  });\n};\n\nconst findClosestParentPos = (commit: Commit): number => {\n  const closestParent = findClosestParent(commit.parents.filter((p) => p !== null));\n  if (!closestParent) {\n    throw new Error(`Closest parent not found for commit ${commit.id}`);\n  }\n\n  const closestParentPos = commitPos.get(closestParent)?.y;\n  if (closestParentPos === undefined) {\n    throw new Error(`Closest parent position not found for commit ${commit.id}`);\n  }\n  return closestParentPos;\n};\n\nconst calculateCommitPosition = (commit: Commit): number => {\n  const closestParentPos = findClosestParentPos(commit);\n  return closestParentPos + COMMIT_STEP;\n};\n\nconst setCommitPosition = (commit: Commit, curPos: number): CommitPosition => {\n  const branch = branchPos.get(commit.branch);\n\n  if (!branch) {\n    throw new Error(`Branch not found for commit ${commit.id}`);\n  }\n\n  const x = branch.pos;\n  const y = curPos + LAYOUT_OFFSET;\n  commitPos.set(commit.id, { x, y });\n  return { x, y };\n};\n\nconst setRootPosition = (commit: Commit, curPos: number, defaultPos: number) => {\n  const branch = branchPos.get(commit.branch);\n  if (!branch) {\n    throw new Error(`Branch not found for commit ${commit.id}`);\n  }\n\n  const y = curPos + defaultPos;\n  const x = branch.pos;\n  commitPos.set(commit.id, { x, y });\n};\n\nconst drawCommitBullet = (\n  gBullets: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  commit: Commit,\n  commitPosition: CommitPositionOffset,\n  typeClass: string,\n  branchIndex: number,\n  commitSymbolType: number\n) => {\n  if (commitSymbolType === commitType.HIGHLIGHT) {\n    gBullets\n      .append('rect')\n      .attr('x', commitPosition.x - 10)\n      .attr('y', commitPosition.y - 10)\n      .attr('width', 20)\n      .attr('height', 20)\n      .attr(\n        'class',\n        `commit ${commit.id} commit-highlight${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-outer`\n      );\n    gBullets\n      .append('rect')\n      .attr('x', commitPosition.x - 6)\n      .attr('y', commitPosition.y - 6)\n      .attr('width', 12)\n      .attr('height', 12)\n      .attr(\n        'class',\n        `commit ${commit.id} commit${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-inner`\n      );\n  } else if (commitSymbolType === commitType.CHERRY_PICK) {\n    gBullets\n      .append('circle')\n      .attr('cx', commitPosition.x)\n      .attr('cy', commitPosition.y)\n      .attr('r', 10)\n      .attr('class', `commit ${commit.id} ${typeClass}`);\n    gBullets\n      .append('circle')\n      .attr('cx', commitPosition.x - 3)\n      .attr('cy', commitPosition.y + 2)\n      .attr('r', 2.75)\n      .attr('fill', '#fff')\n      .attr('class', `commit ${commit.id} ${typeClass}`);\n    gBullets\n      .append('circle')\n      .attr('cx', commitPosition.x + 3)\n      .attr('cy', commitPosition.y + 2)\n      .attr('r', 2.75)\n      .attr('fill', '#fff')\n      .attr('class', `commit ${commit.id} ${typeClass}`);\n    gBullets\n      .append('line')\n      .attr('x1', commitPosition.x + 3)\n      .attr('y1', commitPosition.y + 1)\n      .attr('x2', commitPosition.x)\n      .attr('y2', commitPosition.y - 5)\n      .attr('stroke', '#fff')\n      .attr('class', `commit ${commit.id} ${typeClass}`);\n    gBullets\n      .append('line')\n      .attr('x1', commitPosition.x - 3)\n      .attr('y1', commitPosition.y + 1)\n      .attr('x2', commitPosition.x)\n      .attr('y2', commitPosition.y - 5)\n      .attr('stroke', '#fff')\n      .attr('class', `commit ${commit.id} ${typeClass}`);\n  } else {\n    const circle = gBullets.append('circle');\n    circle.attr('cx', commitPosition.x);\n    circle.attr('cy', commitPosition.y);\n    circle.attr('r', commit.type === commitType.MERGE ? 9 : 10);\n    circle.attr('class', `commit ${commit.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    if (commitSymbolType === commitType.MERGE) {\n      const circle2 = gBullets.append('circle');\n      circle2.attr('cx', commitPosition.x);\n      circle2.attr('cy', commitPosition.y);\n      circle2.attr('r', 6);\n      circle2.attr(\n        'class',\n        `commit ${typeClass} ${commit.id} commit${branchIndex % THEME_COLOR_LIMIT}`\n      );\n    }\n    if (commitSymbolType === commitType.REVERSE) {\n      const cross = gBullets.append('path');\n      cross\n        .attr(\n          'd',\n          `M ${commitPosition.x - 5},${commitPosition.y - 5}L${commitPosition.x + 5},${commitPosition.y + 5}M${commitPosition.x - 5},${commitPosition.y + 5}L${commitPosition.x + 5},${commitPosition.y - 5}`\n        )\n        .attr('class', `commit ${typeClass} ${commit.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    }\n  }\n};\n\nconst drawCommitLabel = (\n  gLabels: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  commit: Commit,\n  commitPosition: CommitPositionOffset,\n  pos: number\n) => {\n  if (\n    commit.type !== commitType.CHERRY_PICK &&\n    ((commit.customId && commit.type === commitType.MERGE) || commit.type !== commitType.MERGE) &&\n    DEFAULT_GITGRAPH_CONFIG?.showCommitLabel\n  ) {\n    const wrapper = gLabels.append('g');\n    const labelBkg = wrapper.insert('rect').attr('class', 'commit-label-bkg');\n    const text = wrapper\n      .append('text')\n      .attr('x', pos)\n      .attr('y', commitPosition.y + 25)\n      .attr('class', 'commit-label')\n      .text(commit.id);\n    const bbox = text.node()?.getBBox();\n\n    if (bbox) {\n      labelBkg\n        .attr('x', commitPosition.posWithOffset - bbox.width / 2 - PY)\n        .attr('y', commitPosition.y + 13.5)\n        .attr('width', bbox.width + 2 * PY)\n        .attr('height', bbox.height + 2 * PY);\n\n      if (dir === 'TB' || dir === 'BT') {\n        labelBkg\n          .attr('x', commitPosition.x - (bbox.width + 4 * PX + 5))\n          .attr('y', commitPosition.y - 12);\n        text\n          .attr('x', commitPosition.x - (bbox.width + 4 * PX))\n          .attr('y', commitPosition.y + bbox.height - 12);\n      } else {\n        text.attr('x', commitPosition.posWithOffset - bbox.width / 2);\n      }\n\n      if (DEFAULT_GITGRAPH_CONFIG.rotateCommitLabel) {\n        if (dir === 'TB' || dir === 'BT') {\n          text.attr(\n            'transform',\n            'rotate(' + -45 + ', ' + commitPosition.x + ', ' + commitPosition.y + ')'\n          );\n          labelBkg.attr(\n            'transform',\n            'rotate(' + -45 + ', ' + commitPosition.x + ', ' + commitPosition.y + ')'\n          );\n        } else {\n          const r_x = -7.5 - ((bbox.width + 10) / 25) * 9.5;\n          const r_y = 10 + (bbox.width / 25) * 8.5;\n          wrapper.attr(\n            'transform',\n            'translate(' +\n              r_x +\n              ', ' +\n              r_y +\n              ') rotate(' +\n              -45 +\n              ', ' +\n              pos +\n              ', ' +\n              commitPosition.y +\n              ')'\n          );\n        }\n      }\n    }\n  }\n};\n\nconst drawCommitTags = (\n  gLabels: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  commit: Commit,\n  commitPosition: CommitPositionOffset,\n  pos: number\n) => {\n  if (commit.tags.length > 0) {\n    let yOffset = 0;\n    let maxTagBboxWidth = 0;\n    let maxTagBboxHeight = 0;\n    const tagElements = [];\n\n    for (const tagValue of commit.tags.reverse()) {\n      const rect = gLabels.insert('polygon');\n      const hole = gLabels.append('circle');\n      const tag = gLabels\n        .append('text')\n        .attr('y', commitPosition.y - 16 - yOffset)\n        .attr('class', 'tag-label')\n        .text(tagValue);\n      const tagBbox = tag.node()?.getBBox();\n      if (!tagBbox) {\n        throw new Error('Tag bbox not found');\n      }\n\n      maxTagBboxWidth = Math.max(maxTagBboxWidth, tagBbox.width);\n      maxTagBboxHeight = Math.max(maxTagBboxHeight, tagBbox.height);\n\n      tag.attr('x', commitPosition.posWithOffset - tagBbox.width / 2);\n\n      tagElements.push({\n        tag,\n        hole,\n        rect,\n        yOffset,\n      });\n\n      yOffset += 20;\n    }\n\n    for (const { tag, hole, rect, yOffset } of tagElements) {\n      const h2 = maxTagBboxHeight / 2;\n      const ly = commitPosition.y - 19.2 - yOffset;\n      rect.attr('class', 'tag-label-bkg').attr(\n        'points',\n        `\n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly + PY}  \n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly - PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly + h2 + PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly + h2 + PY}`\n      );\n\n      hole\n        .attr('cy', ly)\n        .attr('cx', pos - maxTagBboxWidth / 2 + PX / 2)\n        .attr('r', 1.5)\n        .attr('class', 'tag-hole');\n\n      if (dir === 'TB' || dir === 'BT') {\n        const yOrigin = pos + yOffset;\n\n        rect\n          .attr('class', 'tag-label-bkg')\n          .attr(\n            'points',\n            `\n        ${commitPosition.x},${yOrigin + 2}\n        ${commitPosition.x},${yOrigin - 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin + h2 + 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin + h2 + 2}`\n          )\n          .attr('transform', 'translate(12,12) rotate(45, ' + commitPosition.x + ',' + pos + ')');\n        hole\n          .attr('cx', commitPosition.x + PX / 2)\n          .attr('cy', yOrigin)\n          .attr('transform', 'translate(12,12) rotate(45, ' + commitPosition.x + ',' + pos + ')');\n        tag\n          .attr('x', commitPosition.x + 5)\n          .attr('y', yOrigin + 3)\n          .attr('transform', 'translate(14,14) rotate(45, ' + commitPosition.x + ',' + pos + ')');\n      }\n    }\n  }\n};\n\nconst getCommitClassType = (commit: Commit): string => {\n  const commitSymbolType = commit.customType ?? commit.type;\n  switch (commitSymbolType) {\n    case commitType.NORMAL:\n      return 'commit-normal';\n    case commitType.REVERSE:\n      return 'commit-reverse';\n    case commitType.HIGHLIGHT:\n      return 'commit-highlight';\n    case commitType.MERGE:\n      return 'commit-merge';\n    case commitType.CHERRY_PICK:\n      return 'commit-cherry-pick';\n    default:\n      return 'commit-normal';\n  }\n};\n\nconst calculatePosition = (\n  commit: Commit,\n  dir: string,\n  pos: number,\n  commitPos: Map<string, CommitPosition>\n): number => {\n  const defaultCommitPosition = { x: 0, y: 0 }; // Default position if commit is not found\n\n  if (commit.parents.length > 0) {\n    const closestParent = findClosestParent(commit.parents);\n    if (closestParent) {\n      const parentPosition = commitPos.get(closestParent) ?? defaultCommitPosition;\n\n      if (dir === 'TB') {\n        return parentPosition.y + COMMIT_STEP;\n      } else if (dir === 'BT') {\n        const currentPosition = commitPos.get(commit.id) ?? defaultCommitPosition;\n        return currentPosition.y - COMMIT_STEP;\n      } else {\n        return parentPosition.x + COMMIT_STEP;\n      }\n    }\n  } else {\n    if (dir === 'TB') {\n      return defaultPos;\n    } else if (dir === 'BT') {\n      const currentPosition = commitPos.get(commit.id) ?? defaultCommitPosition;\n      return currentPosition.y - COMMIT_STEP;\n    } else {\n      return 0;\n    }\n  }\n  return 0;\n};\n\nconst getCommitPosition = (\n  commit: Commit,\n  pos: number,\n  isParallelCommits: boolean\n): CommitPositionOffset => {\n  const posWithOffset = dir === 'BT' && isParallelCommits ? pos : pos + LAYOUT_OFFSET;\n  const y = dir === 'TB' || dir === 'BT' ? posWithOffset : branchPos.get(commit.branch)?.pos;\n  const x = dir === 'TB' || dir === 'BT' ? branchPos.get(commit.branch)?.pos : posWithOffset;\n  if (x === undefined || y === undefined) {\n    throw new Error(`Position were undefined for commit ${commit.id}`);\n  }\n  return { x, y, posWithOffset };\n};\n\nconst drawCommits = (\n  svg: d3.Selection<d3.BaseType, unknown, HTMLElement, any>,\n  commits: Map<string, Commit>,\n  modifyGraph: boolean\n) => {\n  if (!DEFAULT_GITGRAPH_CONFIG) {\n    throw new Error('GitGraph config not found');\n  }\n  const gBullets = svg.append('g').attr('class', 'commit-bullets');\n  const gLabels = svg.append('g').attr('class', 'commit-labels');\n  let pos = dir === 'TB' || dir === 'BT' ? defaultPos : 0;\n  const keys = [...commits.keys()];\n  const isParallelCommits = DEFAULT_GITGRAPH_CONFIG?.parallelCommits ?? false;\n\n  const sortKeys = (a: string, b: string) => {\n    const seqA = commits.get(a)?.seq;\n    const seqB = commits.get(b)?.seq;\n    return seqA !== undefined && seqB !== undefined ? seqA - seqB : 0;\n  };\n\n  let sortedKeys = keys.sort(sortKeys);\n  if (dir === 'BT') {\n    if (isParallelCommits) {\n      setParallelBTPos(sortedKeys, commits, pos);\n    }\n    sortedKeys = sortedKeys.reverse();\n  }\n\n  sortedKeys.forEach((key) => {\n    const commit = commits.get(key);\n    if (!commit) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (isParallelCommits) {\n      pos = calculatePosition(commit, dir, pos, commitPos);\n    }\n\n    const commitPosition = getCommitPosition(commit, pos, isParallelCommits);\n    // Don't draw the commits now but calculate the positioning which is used by the branch lines etc.\n    if (modifyGraph) {\n      const typeClass = getCommitClassType(commit);\n      const commitSymbolType = commit.customType ?? commit.type;\n      const branchIndex = branchPos.get(commit.branch)?.index ?? 0;\n      drawCommitBullet(gBullets, commit, commitPosition, typeClass, branchIndex, commitSymbolType);\n      drawCommitLabel(gLabels, commit, commitPosition, pos);\n      drawCommitTags(gLabels, commit, commitPosition, pos);\n    }\n    if (dir === 'TB' || dir === 'BT') {\n      commitPos.set(commit.id, { x: commitPosition.x, y: commitPosition.posWithOffset });\n    } else {\n      commitPos.set(commit.id, { x: commitPosition.posWithOffset, y: commitPosition.y });\n    }\n    pos = dir === 'BT' && isParallelCommits ? pos + COMMIT_STEP : pos + COMMIT_STEP + LAYOUT_OFFSET;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n};\n\nconst shouldRerouteArrow = (\n  commitA: Commit,\n  commitB: Commit,\n  p1: CommitPosition,\n  p2: CommitPosition,\n  allCommits: Map<string, Commit>\n) => {\n  const commitBIsFurthest = dir === 'TB' || dir === 'BT' ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = (x: Commit) => x.branch === branchToGetCurve;\n  const isBetweenCommits = (x: Commit) => x.seq > commitA.seq && x.seq < commitB.seq;\n  return [...allCommits.values()].some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n};\n\nconst findLane = (y1: number, y2: number, depth = 0): number => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n\n  const ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n};\n\nconst drawArrow = (\n  svg: d3.Selection<SVGGElement, unknown, HTMLElement, any>,\n  commitA: Commit,\n  commitB: Commit,\n  allCommits: Map<string, Commit>\n) => {\n  const p1 = commitPos.get(commitA.id); // arrowStart\n  const p2 = commitPos.get(commitB.id); // arrowEnd\n  if (p1 === undefined || p2 === undefined) {\n    throw new Error(`Commit positions not found for commits ${commitA.id} and ${commitB.id}`);\n  }\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  // log.debug('drawArrow', p1, p2, arrowNeedsRerouting, commitA.id, commitB.id);\n\n  // Lower-right quadrant logic; top-left is 0,0\n\n  let arc = '';\n  let arc2 = '';\n  let radius = 0;\n  let offset = 0;\n\n  let colorClassNum = branchPos.get(commitB.branch)?.index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos.get(commitA.branch)?.index;\n  }\n\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = 'A 10 10, 0, 0, 0,';\n    arc2 = 'A 10 10, 0, 0, 1,';\n    radius = 10;\n    offset = 10;\n\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n\n    if (dir === 'TB') {\n      if (p1.x < p2.x) {\n        // Source commit is on branch position left of destination commit\n        // so render arrow rightward with colour of destination branch\n\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${\n          p1.y + offset\n        } L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        // Source commit is on branch position right of destination commit\n        // so render arrow leftward with colour of source branch\n\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === 'BT') {\n      if (p1.x < p2.x) {\n        // Source commit is on branch position left of destination commit\n        // so render arrow rightward with colour of destination branch\n\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc2} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        // Source commit is on branch position right of destination commit\n        // so render arrow leftward with colour of source branch\n\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc2} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        // Source commit is on branch positioned above destination commit\n        // so render arrow downward with colour of destination branch\n\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${\n          p1.x + offset\n        } ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        // Source commit is on branch positioned below destination commit\n        // so render arrow upward with colour of source branch\n\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${\n          p1.x + offset\n        } ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = 'A 20 20, 0, 0, 0,';\n    arc2 = 'A 20 20, 0, 0, 1,';\n    radius = 20;\n    offset = 20;\n\n    if (dir === 'TB') {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${\n            p1.y + offset\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n\n      if (p1.x > p2.x) {\n        arc = 'A 20 20, 0, 0, 0,';\n        arc2 = 'A 20 20, 0, 0, 1,';\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${\n            p1.y + offset\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === 'BT') {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${\n            p1.y - offset\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = 'A 20 20, 0, 0, 0,';\n        arc2 = 'A 20 20, 0, 0, 1,';\n        radius = 20;\n        offset = 20;\n\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc} ${p1.x - offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${\n            p1.y - offset\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${\n            p1.y + offset\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${\n            p1.y - offset\n          } L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${\n            p2.y\n          } L ${p2.x} ${p2.y}`;\n        }\n      }\n\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  if (lineDef === undefined) {\n    throw new Error('Line definition not found');\n  }\n  svg\n    .append('path')\n    .attr('d', lineDef)\n    .attr('class', 'arrow arrow' + (colorClassNum! % THEME_COLOR_LIMIT));\n};\n\nconst drawArrows = (\n  svg: d3.Selection<d3.BaseType, unknown, HTMLElement, any>,\n  commits: Map<string, Commit>\n) => {\n  const gArrows = svg.append('g').attr('class', 'commit-arrows');\n  [...commits.keys()].forEach((key) => {\n    const commit = commits.get(key);\n\n    if (commit!.parents && commit!.parents.length > 0) {\n      commit!.parents.forEach((parent) => {\n        drawArrow(gArrows, commits.get(parent)!, commit!, commits);\n      });\n    }\n  });\n};\n\nconst drawBranches = (\n  svg: d3.Selection<d3.BaseType, unknown, HTMLElement, any>,\n  branches: { name: string }[]\n) => {\n  const g = svg.append('g');\n  branches.forEach((branch, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n\n    const pos = branchPos.get(branch.name)?.pos;\n    if (pos === undefined) {\n      throw new Error(`Position not found for branch ${branch.name}`);\n    }\n    const line = g.append('line');\n    line.attr('x1', 0);\n    line.attr('y1', pos);\n    line.attr('x2', maxPos);\n    line.attr('y2', pos);\n    line.attr('class', 'branch branch' + adjustIndexForTheme);\n\n    if (dir === 'TB') {\n      line.attr('y1', defaultPos);\n      line.attr('x1', pos);\n      line.attr('y2', maxPos);\n      line.attr('x2', pos);\n    } else if (dir === 'BT') {\n      line.attr('y1', maxPos);\n      line.attr('x1', pos);\n      line.attr('y2', defaultPos);\n      line.attr('x2', pos);\n    }\n    lanes.push(pos);\n\n    const name = branch.name;\n\n    // Create the actual text element\n    const labelElement = drawText(name);\n    // Create outer g, edgeLabel, this will be positioned after graph layout\n    const bkg = g.insert('rect');\n    const branchLabel = g.insert('g').attr('class', 'branchLabel');\n\n    // Create inner g, label, this will be positioned now for centering the text\n    const label = branchLabel.insert('g').attr('class', 'label branch-label' + adjustIndexForTheme);\n\n    label.node()!.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    bkg\n      .attr('class', 'branchLabelBkg label' + adjustIndexForTheme)\n      .attr('rx', 4)\n      .attr('ry', 4)\n      .attr('x', -bbox.width - 4 - (DEFAULT_GITGRAPH_CONFIG?.rotateCommitLabel === true ? 30 : 0))\n      .attr('y', -bbox.height / 2 + 8)\n      .attr('width', bbox.width + 18)\n      .attr('height', bbox.height + 4);\n    label.attr(\n      'transform',\n      'translate(' +\n        (-bbox.width - 14 - (DEFAULT_GITGRAPH_CONFIG?.rotateCommitLabel === true ? 30 : 0)) +\n        ', ' +\n        (pos - bbox.height / 2 - 1) +\n        ')'\n    );\n    if (dir === 'TB') {\n      bkg.attr('x', pos - bbox.width / 2 - 10).attr('y', 0);\n      label.attr('transform', 'translate(' + (pos - bbox.width / 2 - 5) + ', ' + 0 + ')');\n    } else if (dir === 'BT') {\n      bkg.attr('x', pos - bbox.width / 2 - 10).attr('y', maxPos);\n      label.attr('transform', 'translate(' + (pos - bbox.width / 2 - 5) + ', ' + maxPos + ')');\n    } else {\n      bkg.attr('transform', 'translate(' + -19 + ', ' + (pos - bbox.height / 2) + ')');\n    }\n  });\n};\n\nconst setBranchPosition = function (\n  name: string,\n  pos: number,\n  index: number,\n  bbox: DOMRect,\n  rotateCommitLabel: boolean\n): number {\n  branchPos.set(name, { pos, index });\n  pos += 50 + (rotateCommitLabel ? 40 : 0) + (dir === 'TB' || dir === 'BT' ? bbox.width / 2 : 0);\n  return pos;\n};\n\nexport const draw: DrawDefinition = function (txt, id, ver, diagObj) {\n  clear();\n\n  log.debug('in gitgraph renderer', txt + '\\n', 'id:', id, ver);\n  if (!DEFAULT_GITGRAPH_CONFIG) {\n    throw new Error('GitGraph config not found');\n  }\n  const rotateCommitLabel = DEFAULT_GITGRAPH_CONFIG.rotateCommitLabel ?? false;\n  const db = diagObj.db as GitGraphDBRenderProvider;\n  allCommitsDict = db.getCommits();\n  const branches = db.getBranchesAsObjArray();\n  dir = db.getDirection();\n  const diagram = select(`[id=\"${id}\"]`);\n  let pos = 0;\n\n  branches.forEach((branch, index) => {\n    const labelElement = drawText(branch.name);\n    const g = diagram.append('g');\n    const branchLabel = g.insert('g').attr('class', 'branchLabel');\n    const label = branchLabel.insert('g').attr('class', 'label branch-label');\n    label.node()?.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n\n    pos = setBranchPosition(branch.name, pos, index, bbox, rotateCommitLabel);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n\n  drawCommits(diagram, allCommitsDict, false);\n  if (DEFAULT_GITGRAPH_CONFIG.showBranches) {\n    drawBranches(diagram, branches);\n  }\n  drawArrows(diagram, allCommitsDict);\n  drawCommits(diagram, allCommitsDict, true);\n\n  utils.insertTitle(\n    diagram,\n    'gitTitleText',\n    DEFAULT_GITGRAPH_CONFIG.titleTopMargin ?? 0,\n    db.getDiagramTitle()\n  );\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    diagram,\n    DEFAULT_GITGRAPH_CONFIG.diagramPadding,\n    DEFAULT_GITGRAPH_CONFIG.useMaxWidth\n  );\n};\n\nexport default {\n  draw,\n};\n\nif (import.meta.vitest) {\n  const { it, expect, describe } = import.meta.vitest;\n\n  describe('drawText', () => {\n    it('should drawText', () => {\n      const svgLabel = drawText('main');\n      expect(svgLabel).toBeDefined();\n      expect(svgLabel.children[0].innerHTML).toBe('main');\n    });\n  });\n\n  describe('branchPosition', () => {\n    const bbox: DOMRect = {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 10,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      toJSON: () => '',\n    };\n\n    it('should setBranchPositions LR with two branches', () => {\n      dir = 'LR';\n\n      const pos = setBranchPosition('main', 0, 0, bbox, true);\n      expect(pos).toBe(90);\n      expect(branchPos.get('main')).toEqual({ pos: 0, index: 0 });\n      const posNext = setBranchPosition('develop', pos, 1, bbox, true);\n      expect(posNext).toBe(180);\n      expect(branchPos.get('develop')).toEqual({ pos: pos, index: 1 });\n    });\n\n    it('should setBranchPositions TB with two branches', () => {\n      dir = 'TB';\n      bbox.width = 34.9921875;\n\n      const pos = setBranchPosition('main', 0, 0, bbox, true);\n      expect(pos).toBe(107.49609375);\n      expect(branchPos.get('main')).toEqual({ pos: 0, index: 0 });\n\n      bbox.width = 56.421875;\n      const posNext = setBranchPosition('develop', pos, 1, bbox, true);\n      expect(posNext).toBe(225.70703125);\n      expect(branchPos.get('develop')).toEqual({ pos: pos, index: 1 });\n    });\n  });\n\n  describe('commitPosition', () => {\n    const commits = new Map<string, Commit>([\n      [\n        'commitZero',\n        {\n          id: 'ZERO',\n          message: '',\n          seq: 0,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [],\n          branch: 'main',\n        },\n      ],\n      [\n        'commitA',\n        {\n          id: 'A',\n          message: '',\n          seq: 1,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: ['ZERO'],\n          branch: 'feature',\n        },\n      ],\n      [\n        'commitB',\n        {\n          id: 'B',\n          message: '',\n          seq: 2,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: ['A'],\n          branch: 'feature',\n        },\n      ],\n      [\n        'commitM',\n        {\n          id: 'M',\n          message: 'merged branch feature into main',\n          seq: 3,\n          type: commitType.MERGE,\n          tags: [],\n          parents: ['ZERO', 'B'],\n          branch: 'main',\n          customId: true,\n        },\n      ],\n      [\n        'commitC',\n        {\n          id: 'C',\n          message: '',\n          seq: 4,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: ['ZERO'],\n          branch: 'release',\n        },\n      ],\n      [\n        'commit5_8928ea0',\n        {\n          id: '5-8928ea0',\n          message: 'cherry-picked [object Object] into release',\n          seq: 5,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: ['C', 'M'],\n          branch: 'release',\n        },\n      ],\n      [\n        'commitD',\n        {\n          id: 'D',\n          message: '',\n          seq: 6,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: ['5-8928ea0'],\n          branch: 'release',\n        },\n      ],\n      [\n        'commit7_ed848ba',\n        {\n          id: '7-ed848ba',\n          message: 'cherry-picked [object Object] into release',\n          seq: 7,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: ['D', 'M'],\n          branch: 'release',\n        },\n      ],\n    ]);\n    let pos = 0;\n    branchPos.set('main', { pos: 0, index: 0 });\n    branchPos.set('feature', { pos: 107.49609375, index: 1 });\n    branchPos.set('release', { pos: 224.03515625, index: 2 });\n\n    describe('TB', () => {\n      pos = 30;\n      dir = 'TB';\n      const expectedCommitPositionTB = new Map<string, CommitPositionOffset>([\n        ['commitZero', { x: 0, y: 40, posWithOffset: 40 }],\n        ['commitA', { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        ['commitB', { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        ['commitM', { x: 0, y: 190, posWithOffset: 190 }],\n        ['commitC', { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        ['commit5_8928ea0', { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        ['commitD', { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        ['commit7_ed848ba', { x: 224.03515625, y: 390, posWithOffset: 390 }],\n      ]);\n      commits.forEach((commit, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit, pos, false);\n          expect(position).toEqual(expectedCommitPositionTB.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe('LR', () => {\n      let pos = 30;\n      dir = 'LR';\n      const expectedCommitPositionLR = new Map<string, CommitPositionOffset>([\n        ['commitZero', { x: 0, y: 40, posWithOffset: 40 }],\n        ['commitA', { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        ['commitB', { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        ['commitM', { x: 0, y: 190, posWithOffset: 190 }],\n        ['commitC', { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        ['commit5_8928ea0', { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        ['commitD', { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        ['commit7_ed848ba', { x: 224.03515625, y: 390, posWithOffset: 390 }],\n      ]);\n      commits.forEach((commit, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit, pos, false);\n          expect(position).toEqual(expectedCommitPositionLR.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe('getCommitClassType', () => {\n      const expectedCommitClassType = new Map<string, string>([\n        ['commitZero', 'commit-normal'],\n        ['commitA', 'commit-normal'],\n        ['commitB', 'commit-normal'],\n        ['commitM', 'commit-merge'],\n        ['commitC', 'commit-normal'],\n        ['commit5_8928ea0', 'commit-cherry-pick'],\n        ['commitD', 'commit-normal'],\n        ['commit7_ed848ba', 'commit-cherry-pick'],\n      ]);\n      commits.forEach((commit, key) => {\n        it(`should give the correct class type for commit ${key}`, () => {\n          const classType = getCommitClassType(commit);\n          expect(classType).toBe(expectedCommitClassType.get(key));\n        });\n      });\n    });\n  });\n  describe('building BT parallel commit diagram', () => {\n    const commits = new Map<string, Commit>([\n      [\n        '1-abcdefg',\n        {\n          id: '1-abcdefg',\n          message: '',\n          seq: 0,\n          type: 0,\n          tags: [],\n          parents: [],\n          branch: 'main',\n        },\n      ],\n      [\n        '2-abcdefg',\n        {\n          id: '2-abcdefg',\n          message: '',\n          seq: 1,\n          type: 0,\n          tags: [],\n          parents: ['1-abcdefg'],\n          branch: 'main',\n        },\n      ],\n      [\n        '3-abcdefg',\n        {\n          id: '3-abcdefg',\n          message: '',\n          seq: 2,\n          type: 0,\n          tags: [],\n          parents: ['2-abcdefg'],\n          branch: 'develop',\n        },\n      ],\n      [\n        '4-abcdefg',\n        {\n          id: '4-abcdefg',\n          message: '',\n          seq: 3,\n          type: 0,\n          tags: [],\n          parents: ['3-abcdefg'],\n          branch: 'develop',\n        },\n      ],\n      [\n        '5-abcdefg',\n        {\n          id: '5-abcdefg',\n          message: '',\n          seq: 4,\n          type: 0,\n          tags: [],\n          parents: ['2-abcdefg'],\n          branch: 'feature',\n        },\n      ],\n      [\n        '6-abcdefg',\n        {\n          id: '6-abcdefg',\n          message: '',\n          seq: 5,\n          type: 0,\n          tags: [],\n          parents: ['5-abcdefg'],\n          branch: 'feature',\n        },\n      ],\n      [\n        '7-abcdefg',\n        {\n          id: '7-abcdefg',\n          message: '',\n          seq: 6,\n          type: 0,\n          tags: [],\n          parents: ['2-abcdefg'],\n          branch: 'main',\n        },\n      ],\n      [\n        '8-abcdefg',\n        {\n          id: '8-abcdefg',\n          message: '',\n          seq: 7,\n          type: 0,\n          tags: [],\n          parents: ['7-abcdefg'],\n          branch: 'main',\n        },\n      ],\n    ]);\n    const expectedCommitPosition = new Map<string, CommitPosition>([\n      ['1-abcdefg', { x: 0, y: 40 }],\n      ['2-abcdefg', { x: 0, y: 90 }],\n      ['3-abcdefg', { x: 107.49609375, y: 140 }],\n      ['4-abcdefg', { x: 107.49609375, y: 190 }],\n      ['5-abcdefg', { x: 225.70703125, y: 140 }],\n      ['6-abcdefg', { x: 225.70703125, y: 190 }],\n      ['7-abcdefg', { x: 0, y: 140 }],\n      ['8-abcdefg', { x: 0, y: 190 }],\n    ]);\n\n    const expectedCommitPositionAfterParallel = new Map<string, CommitPosition>([\n      ['1-abcdefg', { x: 0, y: 210 }],\n      ['2-abcdefg', { x: 0, y: 160 }],\n      ['3-abcdefg', { x: 107.49609375, y: 110 }],\n      ['4-abcdefg', { x: 107.49609375, y: 60 }],\n      ['5-abcdefg', { x: 225.70703125, y: 110 }],\n      ['6-abcdefg', { x: 225.70703125, y: 60 }],\n      ['7-abcdefg', { x: 0, y: 110 }],\n      ['8-abcdefg', { x: 0, y: 60 }],\n    ]);\n\n    const expectedCommitCurrentPosition = new Map<string, number>([\n      ['1-abcdefg', 30],\n      ['2-abcdefg', 80],\n      ['3-abcdefg', 130],\n      ['4-abcdefg', 180],\n      ['5-abcdefg', 130],\n      ['6-abcdefg', 180],\n      ['7-abcdefg', 130],\n      ['8-abcdefg', 180],\n    ]);\n    const sortedKeys = [...expectedCommitPosition.keys()];\n    it('should get the correct commit position and current position', () => {\n      dir = 'BT';\n      let curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set('main', { pos: 0, index: 0 });\n      branchPos.set('develop', { pos: 107.49609375, index: 1 });\n      branchPos.set('feature', { pos: 225.70703125, index: 2 });\n      DEFAULT_GITGRAPH_CONFIG!.parallelCommits = true;\n      commits.forEach((commit, key) => {\n        if (commit.parents.length > 0) {\n          curPos = calculateCommitPosition(commit);\n        }\n        const position = setCommitPosition(commit, curPos);\n        expect(position).toEqual(expectedCommitPosition.get(key));\n        expect(curPos).toEqual(expectedCommitCurrentPosition.get(key));\n      });\n    });\n\n    it('should get the correct commit position after parallel commits', () => {\n      commitPos.clear();\n      branchPos.clear();\n      dir = 'BT';\n      const curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set('main', { pos: 0, index: 0 });\n      branchPos.set('develop', { pos: 107.49609375, index: 1 });\n      branchPos.set('feature', { pos: 225.70703125, index: 2 });\n      setParallelBTPos(sortedKeys, commits, curPos);\n      sortedKeys.forEach((commit) => {\n        const position = commitPos.get(commit);\n        expect(position).toEqual(expectedCommitPositionAfterParallel.get(commit));\n      });\n    });\n  });\n  DEFAULT_GITGRAPH_CONFIG!.parallelCommits = false;\n  it('add', () => {\n    commitPos.set('parent1', { x: 1, y: 1 });\n    commitPos.set('parent2', { x: 2, y: 2 });\n    commitPos.set('parent3', { x: 3, y: 3 });\n    dir = 'LR';\n    const parents = ['parent1', 'parent2', 'parent3'];\n    const closestParent = findClosestParent(parents);\n\n    expect(closestParent).toBe('parent3');\n    commitPos.clear();\n  });\n}\n", "const getStyles = (options) =>\n  `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7]\n    .map(\n      (i) =>\n        `\n        .branch-label${i} { fill: ${options['gitBranchLabel' + i]}; }\n        .commit${i} { stroke: ${options['git' + i]}; fill: ${options['git' + i]}; }\n        .commit-highlight${i} { stroke: ${options['gitInv' + i]}; fill: ${options['gitInv' + i]}; }\n        .label${i}  { fill: ${options['git' + i]}; }\n        .arrow${i} { stroke: ${options['git' + i]}; }\n        `\n    )\n    .join('\\n')}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options.commitLabelFontSize}; fill: ${\n    options.commitLabelBackground\n  }; opacity: 0.5; }\n  .tag-label { font-size: ${options.tagLabelFontSize}; fill: ${options.tagLabelColor};}\n  .tag-label-bkg { fill: ${options.tagLabelBackground}; stroke: ${options.tagLabelBorder}; }\n  .tag-hole { fill: ${options.textColor}; }\n\n  .commit-merge {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`;\n\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport { parser } from './gitGraphParser.js';\nimport { db } from './gitGraphAst.js';\nimport gitGraphRenderer from './gitGraphRenderer.js';\nimport gitGraphStyles from './styles.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer: gitGraphRenderer,\n  styles: gitGraphStyles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAM,aAAa;AAAA,EACxB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,OAAO;AAAA,EACP,aAAa;AACf;;;AC8BA,IAAM,0BAA2D,sBAAe;AAChF,IAAMA,aAAY,6BAAuC;AACvD,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAgB,EAAE;AAAA,EACvB,CAAC;AACD,SAAO;AACT,GANkB;AAQlB,IAAM,QAAQ,IAAI,gBAA+B,MAAM;AACrD,QAAM,SAASA,WAAU;AACzB,QAAM,iBAAiB,OAAO;AAC9B,QAAM,kBAAkB,OAAO;AAC/B,SAAO;AAAA,IACL;AAAA,IACA,SAAS,oBAAI,IAAI;AAAA,IACjB,MAAM;AAAA,IACN,cAAc,oBAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,gBAAgB,OAAO,gBAAgB,CAAC,CAAC,CAAC;AAAA,IAC1F,UAAU,oBAAI,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC;AAAA,IAC1C,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,KAAK;AAAA,IACL,SAAS,CAAC;AAAA,EACZ;AACF,CAAC;AAED,SAAS,QAAQ;AACf,SAAO,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC7B;AAFS;AAQT,SAAS,OAAO,MAAa,IAAwB;AACnD,QAAM,YAAY,uBAAO,OAAO,IAAI;AACpC,SAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAChC,UAAM,MAAM,GAAG,IAAI;AACnB,QAAI,CAAC,UAAU,GAAG,GAAG;AACnB,gBAAU,GAAG,IAAI;AACjB,UAAI,KAAK,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAVS;AAYF,IAAM,eAAe,gCAAUC,MAAyB;AAC7D,QAAM,QAAQ,YAAYA;AAC5B,GAF4B;AAIrB,IAAM,aAAa,gCAAU,cAAsB;AACxD,MAAI,MAAM,eAAe,YAAY;AACrC,iBAAe,cAAc,KAAK;AAClC,iBAAe,gBAAgB;AAC/B,MAAI;AACF,UAAM,QAAQ,UAAU,KAAK,MAAM,YAAY;AAAA,EACjD,SAAS,GAAQ;AACf,QAAI,MAAM,wCAAwC,EAAE,OAAO;AAAA,EAC7D;AACF,GAT0B;AAWnB,IAAM,aAAa,kCAAY;AACpC,SAAO,MAAM,QAAQ;AACvB,GAF0B;AAInB,IAAM,SAAS,gCAAU,UAAoB;AAClD,MAAI,MAAM,SAAS;AACnB,MAAI,KAAK,SAAS;AAClB,QAAM,OAAO,SAAS;AACtB,MAAI,OAAO,SAAS;AAEpB,MAAI,KAAK,UAAU,KAAK,IAAI,MAAM,IAAI;AACtC,MAAI,MAAM,oBAAoB,KAAK,IAAI,MAAM,IAAI;AACjD,QAAM,SAASD,WAAU;AACzB,OAAK,eAAO,aAAa,IAAI,MAAM;AACnC,QAAM,eAAO,aAAa,KAAK,MAAM;AACrC,SAAO,MAAM,IAAI,CAAC,QAAQ,eAAO,aAAa,KAAK,MAAM,CAAC;AAC1D,QAAM,YAAoB;AAAA,IACxB,IAAI,KAAK,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM;AAAA,IAC9C,SAAS;AAAA,IACT,KAAK,MAAM,QAAQ;AAAA,IACnB,MAAM,QAAQ,WAAW;AAAA,IACzB,MAAM,QAAQ,CAAC;AAAA,IACf,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,EAAE;AAAA,IACjE,QAAQ,MAAM,QAAQ;AAAA,EACxB;AACA,QAAM,QAAQ,OAAO;AACrB,MAAI,KAAK,eAAe,OAAO,cAAc;AAC7C,MAAI,MAAM,QAAQ,QAAQ,IAAI,UAAU,EAAE,GAAG;AAC3C,QAAI,KAAK,aAAa,UAAU,EAAE,iBAAiB;AAAA,EACrD;AACA,QAAM,QAAQ,QAAQ,IAAI,UAAU,IAAI,SAAS;AACjD,QAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY,UAAU,EAAE;AACjE,MAAI,MAAM,mBAAmB,UAAU,EAAE;AAC3C,GA7BsB;AA+Bf,IAAM,SAAS,gCAAU,UAAoB;AAClD,MAAI,OAAO,SAAS;AACpB,QAAM,QAAQ,SAAS;AACvB,SAAO,eAAO,aAAa,MAAMA,WAAU,CAAC;AAC5C,MAAI,MAAM,QAAQ,SAAS,IAAI,IAAI,GAAG;AACpC,UAAM,IAAI;AAAA,MACR,4HAA4H,IAAI;AAAA,IAClI;AAAA,EACF;AAEA,QAAM,QAAQ,SAAS,IAAI,MAAM,MAAM,QAAQ,QAAQ,OAAO,MAAM,QAAQ,KAAK,KAAK,IAAI;AAC1F,QAAM,QAAQ,aAAa,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AACpD,WAAS,IAAI;AACb,MAAI,MAAM,iBAAiB;AAC7B,GAdsB;AAgBf,IAAM,QAAQ,wBAAC,YAA2B;AAC/C,MAAI,cAAc,QAAQ;AAC1B,MAAI,WAAW,QAAQ;AACvB,QAAM,eAAe,QAAQ;AAC7B,QAAM,aAAa,QAAQ;AAC3B,QAAM,SAASA,WAAU;AACzB,gBAAc,eAAO,aAAa,aAAa,MAAM;AACrD,MAAI,UAAU;AACZ,eAAW,eAAO,aAAa,UAAU,MAAM;AAAA,EACjD;AACA,QAAM,qBAAqB,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC9E,QAAM,mBAAmB,MAAM,QAAQ,SAAS,IAAI,WAAW;AAC/D,QAAM,gBAAgB,qBAClB,MAAM,QAAQ,QAAQ,IAAI,kBAAkB,IAC5C;AACJ,QAAM,cAAkC,mBACpC,MAAM,QAAQ,QAAQ,IAAI,gBAAgB,IAC1C;AACJ,MAAI,iBAAiB,eAAe,cAAc,WAAW,aAAa;AACxE,UAAM,IAAI,MAAM,wBAAwB,WAAW,gBAAgB;AAAA,EACrE;AACA,MAAI,MAAM,QAAQ,eAAe,aAAa;AAC5C,UAAM,QAAa,IAAI,MAAM,6DAA6D;AAC1F,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,YAAY;AAAA,IACzB;AACA,UAAM;AAAA,EACR;AACA,MAAI,kBAAkB,UAAa,CAAC,eAAe;AACjD,UAAM,QAAa,IAAI;AAAA,MACrB,+CAA+C,MAAM,QAAQ,UAAU;AAAA,IACzE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,QAAQ;AAAA,IACrB;AACA,UAAM;AAAA,EACR;AACA,MAAI,CAAC,MAAM,QAAQ,SAAS,IAAI,WAAW,GAAG;AAC5C,UAAM,QAAa,IAAI;AAAA,MACrB,sDAAsD,cAAc;AAAA,IACtE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,UAAU,WAAW,EAAE;AAAA,IACpC;AACA,UAAM;AAAA,EACR;AACA,MAAI,gBAAgB,UAAa,CAAC,aAAa;AAC7C,UAAM,QAAa,IAAI;AAAA,MACrB,sDAAsD,cAAc;AAAA,IACtE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,UAAU;AAAA,IACvB;AACA,UAAM;AAAA,EACR;AACA,MAAI,kBAAkB,aAAa;AACjC,UAAM,QAAa,IAAI,MAAM,0DAA0D;AACvF,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,YAAY;AAAA,IACzB;AACA,UAAM;AAAA,EACR;AACA,MAAI,YAAY,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACnD,UAAM,QAAa,IAAI;AAAA,MACrB,gDACE,WACA;AAAA,IACJ;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,MAC/E,OAAO,SAAS,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,MAChF,UAAU;AAAA,QACR,SAAS,WAAW,IAAI,QAAQ,WAAW,YAAY,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,MAClF;AAAA,IACF;AAEA,UAAM;AAAA,EACR;AAEA,QAAM,iBAAyB,mBAAmB,mBAAmB;AAErE,QAAME,UAAS;AAAA,IACb,IAAI,YAAY,GAAG,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC;AAAA,IAC/C,SAAS,iBAAiB,WAAW,SAAS,MAAM,QAAQ,UAAU;AAAA,IACtE,KAAK,MAAM,QAAQ;AAAA,IACnB,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,cAAc;AAAA,IACjF,QAAQ,MAAM,QAAQ;AAAA,IACtB,MAAM,WAAW;AAAA,IACjB,YAAY;AAAA,IACZ,UAAU,WAAW,OAAO;AAAA,IAC5B,MAAM,cAAc,CAAC;AAAA,EACvB;AACA,QAAM,QAAQ,OAAOA;AACrB,QAAM,QAAQ,QAAQ,IAAIA,QAAO,IAAIA,OAAM;AAC3C,QAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAYA,QAAO,EAAE;AAC9D,MAAI,MAAM,MAAM,QAAQ,QAAQ;AAChC,MAAI,MAAM,gBAAgB;AAC5B,GA3GqB;AA6Gd,IAAM,aAAa,gCAAU,cAA4B;AAC9D,MAAI,WAAW,aAAa;AAC5B,MAAI,WAAW,aAAa;AAC5B,MAAI,OAAO,aAAa;AACxB,MAAI,iBAAiB,aAAa;AAClC,MAAI,MAAM,wBAAwB,UAAU,UAAU,IAAI;AAC1D,QAAM,SAASF,WAAU;AACzB,aAAW,eAAO,aAAa,UAAU,MAAM;AAC/C,aAAW,eAAO,aAAa,UAAU,MAAM;AAE/C,SAAO,MAAM,IAAI,CAAC,QAAQ,eAAO,aAAa,KAAK,MAAM,CAAC;AAE1D,mBAAiB,eAAO,aAAa,gBAAgB,MAAM;AAE3D,MAAI,CAAC,YAAY,CAAC,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACrD,UAAM,QAAa,IAAI;AAAA,MACrB;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,MACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,MACzC,UAAU,CAAC,iBAAiB;AAAA,IAC9B;AACA,UAAM;AAAA,EACR;AAEA,QAAM,eAAe,MAAM,QAAQ,QAAQ,IAAI,QAAQ;AACvD,MAAI,iBAAiB,UAAa,CAAC,cAAc;AAC/C,UAAM,IAAI,MAAM,6EAA6E;AAAA,EAC/F;AACA,MACE,kBACA,EAAE,MAAM,QAAQ,aAAa,OAAO,KAAK,aAAa,QAAQ,SAAS,cAAc,IACrF;AACA,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACA,QAAM,qBAAqB,aAAa;AACxC,MAAI,aAAa,SAAS,WAAW,SAAS,CAAC,gBAAgB;AAC7D,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACA,MAAI,CAAC,YAAY,CAAC,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AAGrD,QAAI,uBAAuB,MAAM,QAAQ,YAAY;AACnD,YAAM,QAAa,IAAI;AAAA,QACrB;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AACA,UAAM,kBAAkB,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC3E,QAAI,oBAAoB,UAAa,CAAC,iBAAiB;AACrD,YAAM,QAAa,IAAI;AAAA,QACrB,qDAAqD,MAAM,QAAQ,UAAU;AAAA,MAC/E;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AAEA,UAAM,gBAAgB,MAAM,QAAQ,QAAQ,IAAI,eAAe;AAC/D,QAAI,kBAAkB,UAAa,CAAC,eAAe;AACjD,YAAM,QAAa,IAAI;AAAA,QACrB,qDAAqD,MAAM,QAAQ,UAAU;AAAA,MAC/E;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AACA,UAAME,UAAS;AAAA,MACb,IAAI,MAAM,QAAQ,MAAM,MAAM,MAAM;AAAA,MACpC,SAAS,iBAAiB,cAAc,OAAO,SAAS,MAAM,QAAQ,UAAU;AAAA,MAChF,KAAK,MAAM,QAAQ;AAAA,MACnB,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,aAAa,EAAE;AAAA,MAClF,QAAQ,MAAM,QAAQ;AAAA,MACtB,MAAM,WAAW;AAAA,MACjB,MAAM,OACF,KAAK,OAAO,OAAO,IACnB;AAAA,QACE,eAAe,aAAa,EAAE,GAC5B,aAAa,SAAS,WAAW,QAAQ,WAAW,cAAc,KAAK,EACzE;AAAA,MACF;AAAA,IACN;AAEA,UAAM,QAAQ,OAAOA;AACrB,UAAM,QAAQ,QAAQ,IAAIA,QAAO,IAAIA,OAAM;AAC3C,UAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAYA,QAAO,EAAE;AAC9D,QAAI,MAAM,MAAM,QAAQ,QAAQ;AAChC,QAAI,MAAM,eAAe;AAAA,EAC3B;AACF,GA3G0B;AA4GnB,IAAM,WAAW,gCAAUC,SAAgB;AAChD,EAAAA,UAAS,eAAO,aAAaA,SAAQH,WAAU,CAAC;AAChD,MAAI,CAAC,MAAM,QAAQ,SAAS,IAAIG,OAAM,GAAG;AACvC,UAAM,QAAa,IAAI;AAAA,MACrB,+EAA+EA,OAAM;AAAA,IACvF;AACA,UAAM,OAAO;AAAA,MACX,MAAM,YAAYA,OAAM;AAAA,MACxB,OAAO,YAAYA,OAAM;AAAA,MACzB,UAAU,CAAC,UAAUA,OAAM,EAAE;AAAA,IAC/B;AACA,UAAM;AAAA,EACR,OAAO;AACL,UAAM,QAAQ,aAAaA;AAC3B,UAAM,KAAK,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC9D,QAAI,OAAO,UAAa,CAAC,IAAI;AAC3B,YAAM,QAAQ,OAAO;AAAA,IACvB,OAAO;AACL,YAAM,QAAQ,OAAO,MAAM,QAAQ,QAAQ,IAAI,EAAE,KAAK;AAAA,IACxD;AAAA,EACF;AACF,GArBwB;AA4BxB,SAAS,OAAO,KAAY,KAAU,QAAa;AACjD,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,MAAI,UAAU,IAAI;AAChB,QAAI,KAAK,MAAM;AAAA,EACjB,OAAO;AACL,QAAI,OAAO,OAAO,GAAG,MAAM;AAAA,EAC7B;AACF;AAPS;AAST,SAAS,yBAAyB,WAAqB;AACrD,QAAMD,UAAS,UAAU,OAAO,CAAC,KAAKA,YAAW;AAC/C,QAAI,IAAI,MAAMA,QAAO,KAAK;AACxB,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,GAAG,UAAU,CAAC,CAAC;AACf,MAAI,OAAO;AACX,YAAU,QAAQ,SAAU,GAAG;AAC7B,QAAI,MAAMA,SAAQ;AAChB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,CAAC,MAAMA,QAAO,IAAIA,QAAO,GAAG;AAC1C,aAAWC,WAAU,MAAM,QAAQ,UAAU;AAC3C,QAAI,MAAM,QAAQ,SAAS,IAAIA,OAAM,MAAMD,QAAO,IAAI;AACpD,YAAM,KAAKC,OAAM;AAAA,IACnB;AAAA,EACF;AACA,MAAI,MAAM,MAAM,KAAK,GAAG,CAAC;AACzB,MAAID,QAAO,WAAWA,QAAO,QAAQ,UAAU,KAAKA,QAAO,QAAQ,CAAC,KAAKA,QAAO,QAAQ,CAAC,GAAG;AAC1F,UAAM,YAAY,MAAM,QAAQ,QAAQ,IAAIA,QAAO,QAAQ,CAAC,CAAC;AAC7D,WAAO,WAAWA,SAAQ,SAAS;AACnC,QAAIA,QAAO,QAAQ,CAAC,GAAG;AACrB,gBAAU,KAAK,MAAM,QAAQ,QAAQ,IAAIA,QAAO,QAAQ,CAAC,CAAC,CAAE;AAAA,IAC9D;AAAA,EACF,WAAWA,QAAO,QAAQ,UAAU,GAAG;AACrC;AAAA,EACF,OAAO;AACL,QAAIA,QAAO,QAAQ,CAAC,GAAG;AACrB,YAAM,YAAY,MAAM,QAAQ,QAAQ,IAAIA,QAAO,QAAQ,CAAC,CAAC;AAC7D,aAAO,WAAWA,SAAQ,SAAS;AAAA,IACrC;AAAA,EACF;AACA,cAAY,OAAO,WAAW,CAAC,MAAM,EAAE,EAAE;AACzC,2BAAyB,SAAS;AACpC;AAtCS;AAwCF,IAAM,cAAc,kCAAY;AACrC,MAAI,MAAM,MAAM,QAAQ,OAAO;AAC/B,QAAM,OAAO,gBAAgB,EAAE,CAAC;AAChC,2BAAyB,CAAC,IAAI,CAAC;AACjC,GAJ2B;AAMpB,IAAME,SAAQ,kCAAY;AAC/B,QAAM,MAAM;AACZ,QAAY;AACd,GAHqB;AAKd,IAAM,wBAAwB,kCAAY;AAC/C,QAAM,gBAAgB,CAAC,GAAG,MAAM,QAAQ,aAAa,OAAO,CAAC,EAC1D,IAAI,CAAC,cAAc,MAAM;AACxB,QAAI,aAAa,UAAU,QAAQ,aAAa,UAAU,QAAW;AACnE,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,OAAO,WAAW,KAAK,CAAC,EAAE;AAAA,IAC5B;AAAA,EACF,CAAC,EACA,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE,EAC9C,IAAI,CAAC,EAAE,KAAK,OAAO,EAAE,KAAK,EAAE;AAE/B,SAAO;AACT,GAfqC;AAiB9B,IAAM,cAAc,kCAAY;AACrC,SAAO,MAAM,QAAQ;AACvB,GAF2B;AAGpB,IAAM,aAAa,kCAAY;AACpC,SAAO,MAAM,QAAQ;AACvB,GAF0B;AAGnB,IAAM,kBAAkB,kCAAY;AACzC,QAAM,YAAY,CAAC,GAAG,MAAM,QAAQ,QAAQ,OAAO,CAAC;AACpD,YAAU,QAAQ,SAAU,GAAG;AAC7B,QAAI,MAAM,EAAE,EAAE;AAAA,EAChB,CAAC;AACD,YAAU,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG;AACtC,SAAO;AACT,GAP+B;AAQxB,IAAM,mBAAmB,kCAAY;AAC1C,SAAO,MAAM,QAAQ;AACvB,GAFgC;AAGzB,IAAM,eAAe,kCAAY;AACtC,SAAO,MAAM,QAAQ;AACvB,GAF4B;AAGrB,IAAM,UAAU,kCAAY;AACjC,SAAO,MAAM,QAAQ;AACvB,GAFuB;AAIhB,IAAM,KAAiB;AAAA,EAC5B;AAAA,EACA,WAAAJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,OAAAI;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACxfA,IAAM,WAAW,wBAAC,KAAeC,QAAgC;AAC/D,mBAAiB,KAAKA,GAAE;AAExB,MAAI,IAAI,KAAK;AAEX,IAAAA,IAAG,aAAa,IAAI,GAAG;AAAA,EACzB;AACA,aAAW,aAAa,IAAI,YAAY;AACtC,mBAAe,WAAWA,GAAE;AAAA,EAC9B;AACF,GAViB;AAYjB,IAAM,iBAAiB,wBAAC,WAAgBA,QAAgC;AACtE,QAAM,UAA+C;AAAA,IACnD,QAAQ,wBAAC,SAASA,IAAG,OAAO,YAAY,IAAI,CAAC,GAArC;AAAA,IACR,QAAQ,wBAAC,SAASA,IAAG,OAAO,YAAY,IAAI,CAAC,GAArC;AAAA,IACR,OAAO,wBAAC,SAASA,IAAG,MAAM,WAAW,IAAI,CAAC,GAAnC;AAAA,IACP,UAAU,wBAAC,SAASA,IAAG,SAAS,cAAc,IAAI,CAAC,GAAzC;AAAA,IACV,eAAe,wBAAC,SAASA,IAAG,WAAW,mBAAmB,IAAI,CAAC,GAAhD;AAAA,EACjB;AAEA,QAAMC,UAAS,QAAQ,UAAU,KAAK;AACtC,MAAIA,SAAQ;AACV,IAAAA,QAAO,SAAS;AAAA,EAClB,OAAO;AACL,QAAI,MAAM,2BAA2B,UAAU,KAAK,EAAE;AAAA,EACxD;AACF,GAfuB;AAiBvB,IAAM,cAAc,wBAACC,YAAgC;AACnD,QAAM,WAAqB;AAAA,IACzB,IAAIA,QAAO;AAAA,IACX,KAAKA,QAAO,WAAW;AAAA,IACvB,MAAMA,QAAO,SAAS,SAAY,WAAWA,QAAO,IAAI,IAAI,WAAW;AAAA,IACvE,MAAMA,QAAO,QAAQ;AAAA,EACvB;AACA,SAAO;AACT,GARoB;AAUpB,IAAM,cAAc,wBAACC,YAAgC;AACnD,QAAM,WAAqB;AAAA,IACzB,MAAMA,QAAO;AAAA,IACb,OAAOA,QAAO,SAAS;AAAA,EACzB;AACA,SAAO;AACT,GANoB;AAQpB,IAAM,aAAa,wBAACC,WAA6B;AAC/C,QAAM,UAAmB;AAAA,IACvB,QAAQA,OAAM;AAAA,IACd,IAAIA,OAAM,MAAM;AAAA,IAChB,MAAMA,OAAM,SAAS,SAAY,WAAWA,OAAM,IAAI,IAAI;AAAA,IAC1D,MAAMA,OAAM,QAAQ;AAAA,EACtB;AACA,SAAO;AACT,GARmB;AAUnB,IAAM,gBAAgB,wBAACC,cAAkC;AACvD,QAAMF,UAASE,UAAS;AACxB,SAAOF;AACT,GAHsB;AAKtB,IAAM,qBAAqB,wBAAC,kBAAkD;AAC5E,QAAM,eAA6B;AAAA,IACjC,IAAI,cAAc;AAAA,IAClB,UAAU;AAAA,IACV,MAAM,cAAc,MAAM,WAAW,IAAI,SAAY,cAAc;AAAA,IACnE,QAAQ,cAAc;AAAA,EACxB;AACA,SAAO;AACT,GAR2B;AAUpB,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,UAAiC;AAC7C,UAAM,MAAgB,MAAM,MAAM,YAAY,KAAK;AACnD,QAAI,MAAM,GAAG;AACb,aAAS,KAAK,EAAE;AAAA,EAClB,GAJO;AAKT;AAEA,IAAI,QAAoB;AACtB,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AAEjC,QAAM,SAAkC;AAAA,IACtC;AAAA,IACA,cAAc,GAAG,GAAG;AAAA,IACpB,QAAQ,GAAG,GAAG;AAAA,IACd,QAAQ,GAAG,GAAG;AAAA,IACd,OAAO,GAAG,GAAG;AAAA,IACb,YAAY,GAAG,GAAG;AAAA,IAClB,UAAU,GAAG,GAAG;AAAA,EAClB;AAEA,WAAS,mBAAmB,MAAM;AAChC,OAAG,mCAAmC,MAAM;AAC1C,YAAMD,UAAS;AAAA,QACb,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR;AACA,qBAAeA,SAAQ,MAAM;AAC7B,aAAO,OAAO,MAAM,EAAE,qBAAqB;AAAA,QACzC,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,OAAG,mCAAmC,MAAM;AAC1C,YAAMC,UAAS;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AACA,qBAAeA,SAAQ,MAAM;AAC7B,aAAO,OAAO,MAAM,EAAE,qBAAqB,EAAE,MAAM,aAAa,OAAO,EAAE,CAAC;AAAA,IAC5E,CAAC;AACD,OAAG,qCAAqC,MAAM;AAC5C,YAAME,YAAW;AAAA,QACf,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,qBAAeA,WAAU,MAAM;AAC/B,aAAO,OAAO,QAAQ,EAAE,qBAAqB,WAAW;AAAA,IAC1D,CAAC;AACD,OAAG,kCAAkC,MAAM;AACzC,YAAMD,SAAQ;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR;AACA,qBAAeA,QAAO,MAAM;AAC5B,aAAO,OAAO,KAAK,EAAE,qBAAqB;AAAA,QACxC,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,OAAG,2CAA2C,MAAM;AAClD,YAAME,cAAa;AAAA,QACjB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,QAAQ;AAAA,MACV;AACA,qBAAeA,aAAY,MAAM;AACjC,aAAO,OAAO,UAAU,EAAE,qBAAqB;AAAA,QAC7C,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAED,OAAG,iDAAiD,MAAM;AACxD,YAAM,QAAkB;AAAA,QACtB,OAAO;AAAA,QACP,YAAY,CAAC;AAAA,MACf;AACA,YAAM,cAAwB;AAAA,QAC5B,OAAO;AAAA,QACP,YAAY;AAAA,UACV;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,SAAS;AAAA,YACT,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAEA,eAAS,aAAa,MAAM;AAE5B,aAAO,OAAO,MAAM,EAAE,qBAAqB;AAAA,QACzC,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD,aAAO,OAAO,MAAM,EAAE,qBAAqB,EAAE,MAAM,aAAa,OAAO,EAAE,CAAC;AAC1E,aAAO,OAAO,KAAK,EAAE,qBAAqB;AAAA,QACxC,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD,aAAO,OAAO,QAAQ,EAAE,qBAAqB,WAAW;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC;AACH;;;AC3NA,IAAM,iBAAiBC,WAAU;AACjC,IAAMC,2BAA0B,gBAAgB;AAChD,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,KAAK;AACX,IAAM,KAAK;AAEX,IAAM,oBAAoB;AAC1B,IAAM,YAAY,oBAAI,IAA4B;AAClD,IAAM,YAAY,oBAAI,IAA4B;AAClD,IAAM,aAAa;AAEnB,IAAI,iBAAiB,oBAAI,IAAI;AAC7B,IAAI,QAAkB,CAAC;AACvB,IAAI,SAAS;AACb,IAAI,MAA0B;AAE9B,IAAMC,SAAQ,6BAAM;AAClB,YAAU,MAAM;AAChB,YAAU,MAAM;AAChB,iBAAe,MAAM;AACrB,WAAS;AACT,UAAQ,CAAC;AACT,QAAM;AACR,GAPc;AASd,IAAM,WAAW,wBAAC,QAA2B;AAC3C,QAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,QAAM,OAAO,OAAO,QAAQ,WAAW,IAAI,MAAM,qBAAqB,IAAI;AAE1E,OAAK,QAAQ,CAAC,QAAQ;AACpB,UAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,UAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,UAAM,aAAa,MAAM,KAAK;AAC9B,UAAM,aAAa,KAAK,GAAG;AAC3B,UAAM,aAAa,SAAS,KAAK;AACjC,UAAM,cAAc,IAAI,KAAK;AAC7B,aAAS,YAAY,KAAK;AAAA,EAC5B,CAAC;AAED,SAAO;AACT,GAfiB;AAiBjB,IAAM,oBAAoB,wBAAC,YAA0C;AACnE,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,qBAAiB,wBAAC,GAAW,MAAc,KAAK,GAA/B;AACjB,qBAAiB;AAAA,EACnB,OAAO;AACL,qBAAiB,wBAAC,GAAW,MAAc,KAAK,GAA/B;AACjB,qBAAiB;AAAA,EACnB;AAEA,UAAQ,QAAQ,CAAC,WAAW;AAC1B,UAAM,iBACJ,QAAQ,QAAQ,OAAO,OAAO,UAAU,IAAI,MAAM,GAAG,IAAI,UAAU,IAAI,MAAM,GAAG;AAElF,QAAI,mBAAmB,UAAa,eAAe,gBAAgB,cAAc,GAAG;AAClF,sBAAgB;AAChB,uBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AAED,SAAO;AACT,GAvB0B;AAyB1B,IAAM,sBAAsB,wBAAC,YAAsB;AACjD,MAAI,gBAAgB;AACpB,MAAI,cAAc;AAElB,UAAQ,QAAQ,CAAC,WAAW;AAC1B,UAAM,iBAAiB,UAAU,IAAI,MAAM,EAAG;AAC9C,QAAI,kBAAkB,aAAa;AACjC,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB;AAC1B,GAZ4B;AAc5B,IAAM,mBAAmB,wBACvB,YACA,SACAC,gBACG;AACH,MAAI,SAASA;AACb,MAAI,cAAcA;AAClB,QAAM,QAAkB,CAAC;AAEzB,aAAW,QAAQ,CAAC,QAAQ;AAC1B,UAAMC,UAAS,QAAQ,IAAI,GAAG;AAC9B,QAAI,CAACA,SAAQ;AACX,YAAM,IAAI,MAAM,4BAA4B,GAAG,EAAE;AAAA,IACnD;AAEA,QAAIA,QAAO,QAAQ,QAAQ;AACzB,eAAS,wBAAwBA,OAAM;AACvC,oBAAc,KAAK,IAAI,QAAQ,WAAW;AAAA,IAC5C,OAAO;AACL,YAAM,KAAKA,OAAM;AAAA,IACnB;AACA,sBAAkBA,SAAQ,MAAM;AAAA,EAClC,CAAC;AAED,WAAS;AACT,QAAM,QAAQ,CAACA,YAAW;AACxB,oBAAgBA,SAAQ,QAAQD,WAAU;AAAA,EAC5C,CAAC;AACD,aAAW,QAAQ,CAAC,QAAQ;AAC1B,UAAMC,UAAS,QAAQ,IAAI,GAAG;AAE9B,QAAIA,SAAQ,QAAQ,QAAQ;AAC1B,YAAM,gBAAgB,oBAAoBA,QAAO,OAAO;AACxD,eAAS,UAAU,IAAI,aAAa,EAAG,IAAI;AAC3C,UAAI,UAAU,aAAa;AACzB,sBAAc;AAAA,MAChB;AACA,YAAM,IAAI,UAAU,IAAIA,QAAO,MAAM,EAAG;AACxC,YAAM,IAAI,SAAS;AACnB,gBAAU,IAAIA,QAAO,IAAI,EAAE,GAAM,EAAK,CAAC;AAAA,IACzC;AAAA,EACF,CAAC;AACH,GA1CyB;AA4CzB,IAAM,uBAAuB,wBAACA,YAA2B;AACvD,QAAM,gBAAgB,kBAAkBA,QAAO,QAAQ,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC;AAChF,MAAI,CAAC,eAAe;AAClB,UAAM,IAAI,MAAM,uCAAuCA,QAAO,EAAE,EAAE;AAAA,EACpE;AAEA,QAAM,mBAAmB,UAAU,IAAI,aAAa,GAAG;AACvD,MAAI,qBAAqB,QAAW;AAClC,UAAM,IAAI,MAAM,gDAAgDA,QAAO,EAAE,EAAE;AAAA,EAC7E;AACA,SAAO;AACT,GAX6B;AAa7B,IAAM,0BAA0B,wBAACA,YAA2B;AAC1D,QAAM,mBAAmB,qBAAqBA,OAAM;AACpD,SAAO,mBAAmB;AAC5B,GAHgC;AAKhC,IAAM,oBAAoB,wBAACA,SAAgB,WAAmC;AAC5E,QAAMC,UAAS,UAAU,IAAID,QAAO,MAAM;AAE1C,MAAI,CAACC,SAAQ;AACX,UAAM,IAAI,MAAM,+BAA+BD,QAAO,EAAE,EAAE;AAAA,EAC5D;AAEA,QAAM,IAAIC,QAAO;AACjB,QAAM,IAAI,SAAS;AACnB,YAAU,IAAID,QAAO,IAAI,EAAE,GAAG,EAAE,CAAC;AACjC,SAAO,EAAE,GAAG,EAAE;AAChB,GAX0B;AAa1B,IAAM,kBAAkB,wBAACA,SAAgB,QAAgBD,gBAAuB;AAC9E,QAAME,UAAS,UAAU,IAAID,QAAO,MAAM;AAC1C,MAAI,CAACC,SAAQ;AACX,UAAM,IAAI,MAAM,+BAA+BD,QAAO,EAAE,EAAE;AAAA,EAC5D;AAEA,QAAM,IAAI,SAASD;AACnB,QAAM,IAAIE,QAAO;AACjB,YAAU,IAAID,QAAO,IAAI,EAAE,GAAG,EAAE,CAAC;AACnC,GATwB;AAWxB,IAAM,mBAAmB,wBACvB,UACAA,SACA,gBACA,WACA,aACA,qBACG;AACH,MAAI,qBAAqB,WAAW,WAAW;AAC7C,aACG,OAAO,MAAM,EACb,KAAK,KAAK,eAAe,IAAI,EAAE,EAC/B,KAAK,KAAK,eAAe,IAAI,EAAE,EAC/B,KAAK,SAAS,EAAE,EAChB,KAAK,UAAU,EAAE,EACjB;AAAA,MACC;AAAA,MACA,UAAUA,QAAO,EAAE,oBAAoB,cAAc,iBAAiB,IAAI,SAAS;AAAA,IACrF;AACF,aACG,OAAO,MAAM,EACb,KAAK,KAAK,eAAe,IAAI,CAAC,EAC9B,KAAK,KAAK,eAAe,IAAI,CAAC,EAC9B,KAAK,SAAS,EAAE,EAChB,KAAK,UAAU,EAAE,EACjB;AAAA,MACC;AAAA,MACA,UAAUA,QAAO,EAAE,UAAU,cAAc,iBAAiB,IAAI,SAAS;AAAA,IAC3E;AAAA,EACJ,WAAW,qBAAqB,WAAW,aAAa;AACtD,aACG,OAAO,QAAQ,EACf,KAAK,MAAM,eAAe,CAAC,EAC3B,KAAK,MAAM,eAAe,CAAC,EAC3B,KAAK,KAAK,EAAE,EACZ,KAAK,SAAS,UAAUA,QAAO,EAAE,IAAI,SAAS,EAAE;AACnD,aACG,OAAO,QAAQ,EACf,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,KAAK,IAAI,EACd,KAAK,QAAQ,MAAM,EACnB,KAAK,SAAS,UAAUA,QAAO,EAAE,IAAI,SAAS,EAAE;AACnD,aACG,OAAO,QAAQ,EACf,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,KAAK,IAAI,EACd,KAAK,QAAQ,MAAM,EACnB,KAAK,SAAS,UAAUA,QAAO,EAAE,IAAI,SAAS,EAAE;AACnD,aACG,OAAO,MAAM,EACb,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,CAAC,EAC3B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,UAAUA,QAAO,EAAE,IAAI,SAAS,EAAE;AACnD,aACG,OAAO,MAAM,EACb,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,MAAM,eAAe,CAAC,EAC3B,KAAK,MAAM,eAAe,IAAI,CAAC,EAC/B,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,UAAUA,QAAO,EAAE,IAAI,SAAS,EAAE;AAAA,EACrD,OAAO;AACL,UAAM,SAAS,SAAS,OAAO,QAAQ;AACvC,WAAO,KAAK,MAAM,eAAe,CAAC;AAClC,WAAO,KAAK,MAAM,eAAe,CAAC;AAClC,WAAO,KAAK,KAAKA,QAAO,SAAS,WAAW,QAAQ,IAAI,EAAE;AAC1D,WAAO,KAAK,SAAS,UAAUA,QAAO,EAAE,UAAU,cAAc,iBAAiB,EAAE;AACnF,QAAI,qBAAqB,WAAW,OAAO;AACzC,YAAM,UAAU,SAAS,OAAO,QAAQ;AACxC,cAAQ,KAAK,MAAM,eAAe,CAAC;AACnC,cAAQ,KAAK,MAAM,eAAe,CAAC;AACnC,cAAQ,KAAK,KAAK,CAAC;AACnB,cAAQ;AAAA,QACN;AAAA,QACA,UAAU,SAAS,IAAIA,QAAO,EAAE,UAAU,cAAc,iBAAiB;AAAA,MAC3E;AAAA,IACF;AACA,QAAI,qBAAqB,WAAW,SAAS;AAC3C,YAAM,QAAQ,SAAS,OAAO,MAAM;AACpC,YACG;AAAA,QACC;AAAA,QACA,KAAK,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC;AAAA,MACnM,EACC,KAAK,SAAS,UAAU,SAAS,IAAIA,QAAO,EAAE,UAAU,cAAc,iBAAiB,EAAE;AAAA,IAC9F;AAAA,EACF;AACF,GA5FyB;AA8FzB,IAAM,kBAAkB,wBACtB,SACAA,SACA,gBACA,QACG;AACH,MACEA,QAAO,SAAS,WAAW,gBACzBA,QAAO,YAAYA,QAAO,SAAS,WAAW,SAAUA,QAAO,SAAS,WAAW,UACrFH,0BAAyB,iBACzB;AACA,UAAM,UAAU,QAAQ,OAAO,GAAG;AAClC,UAAM,WAAW,QAAQ,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AACxE,UAAM,OAAO,QACV,OAAO,MAAM,EACb,KAAK,KAAK,GAAG,EACb,KAAK,KAAK,eAAe,IAAI,EAAE,EAC/B,KAAK,SAAS,cAAc,EAC5B,KAAKG,QAAO,EAAE;AACjB,UAAM,OAAO,KAAK,KAAK,GAAG,QAAQ;AAElC,QAAI,MAAM;AACR,eACG,KAAK,KAAK,eAAe,gBAAgB,KAAK,QAAQ,IAAI,EAAE,EAC5D,KAAK,KAAK,eAAe,IAAI,IAAI,EACjC,KAAK,SAAS,KAAK,QAAQ,IAAI,EAAE,EACjC,KAAK,UAAU,KAAK,SAAS,IAAI,EAAE;AAEtC,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,iBACG,KAAK,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,EACtD,KAAK,KAAK,eAAe,IAAI,EAAE;AAClC,aACG,KAAK,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,GAAG,EAClD,KAAK,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE;AAAA,MAClD,OAAO;AACL,aAAK,KAAK,KAAK,eAAe,gBAAgB,KAAK,QAAQ,CAAC;AAAA,MAC9D;AAEA,UAAIH,yBAAwB,mBAAmB;AAC7C,YAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,eAAK;AAAA,YACH;AAAA,YACA,iBAAyB,eAAe,IAAI,OAAO,eAAe,IAAI;AAAA,UACxE;AACA,mBAAS;AAAA,YACP;AAAA,YACA,iBAAyB,eAAe,IAAI,OAAO,eAAe,IAAI;AAAA,UACxE;AAAA,QACF,OAAO;AACL,gBAAM,MAAM,QAAS,KAAK,QAAQ,MAAM,KAAM;AAC9C,gBAAM,MAAM,KAAM,KAAK,QAAQ,KAAM;AACrC,kBAAQ;AAAA,YACN;AAAA,YACA,eACE,MACA,OACA,MACA,mBAGA,MACA,OACA,eAAe,IACf;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,GAtEwB;AAwExB,IAAM,iBAAiB,wBACrB,SACAG,SACA,gBACA,QACG;AACH,MAAIA,QAAO,KAAK,SAAS,GAAG;AAC1B,QAAI,UAAU;AACd,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AACvB,UAAM,cAAc,CAAC;AAErB,eAAW,YAAYA,QAAO,KAAK,QAAQ,GAAG;AAC5C,YAAM,OAAO,QAAQ,OAAO,SAAS;AACrC,YAAM,OAAO,QAAQ,OAAO,QAAQ;AACpC,YAAM,MAAM,QACT,OAAO,MAAM,EACb,KAAK,KAAK,eAAe,IAAI,KAAK,OAAO,EACzC,KAAK,SAAS,WAAW,EACzB,KAAK,QAAQ;AAChB,YAAM,UAAU,IAAI,KAAK,GAAG,QAAQ;AACpC,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AAEA,wBAAkB,KAAK,IAAI,iBAAiB,QAAQ,KAAK;AACzD,yBAAmB,KAAK,IAAI,kBAAkB,QAAQ,MAAM;AAE5D,UAAI,KAAK,KAAK,eAAe,gBAAgB,QAAQ,QAAQ,CAAC;AAE9D,kBAAY,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,iBAAW;AAAA,IACb;AAEA,eAAW,EAAE,KAAK,MAAM,MAAM,SAAAE,SAAQ,KAAK,aAAa;AACtD,YAAM,KAAK,mBAAmB;AAC9B,YAAM,KAAK,eAAe,IAAI,OAAOA;AACrC,WAAK,KAAK,SAAS,eAAe,EAAE;AAAA,QAClC;AAAA,QACA;AAAA,QACA,MAAM,kBAAkB,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;AAAA,QAC7C,MAAM,kBAAkB,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;AAAA,QAC7C,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,MACzE;AAEA,WACG,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,MAAM,kBAAkB,IAAI,KAAK,CAAC,EAC7C,KAAK,KAAK,GAAG,EACb,KAAK,SAAS,UAAU;AAE3B,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,cAAM,UAAU,MAAMA;AAEtB,aACG,KAAK,SAAS,eAAe,EAC7B;AAAA,UACC;AAAA,UACA;AAAA,UACF,eAAe,CAAC,IAAI,UAAU,CAAC;AAAA,UAC/B,eAAe,CAAC,IAAI,UAAU,CAAC;AAAA,UAC/B,eAAe,IAAI,aAAa,IAAI,UAAU,KAAK,CAAC;AAAA,UACpD,eAAe,IAAI,gBAAgB,kBAAkB,CAAC,IAAI,UAAU,KAAK,CAAC;AAAA,UAC1E,eAAe,IAAI,gBAAgB,kBAAkB,CAAC,IAAI,UAAU,KAAK,CAAC;AAAA,UAC1E,eAAe,IAAI,aAAa,IAAI,UAAU,KAAK,CAAC;AAAA,QACpD,EACC,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AACxF,aACG,KAAK,MAAM,eAAe,IAAI,KAAK,CAAC,EACpC,KAAK,MAAM,OAAO,EAClB,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AACxF,YACG,KAAK,KAAK,eAAe,IAAI,CAAC,EAC9B,KAAK,KAAK,UAAU,CAAC,EACrB,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AAAA,MAC1F;AAAA,IACF;AAAA,EACF;AACF,GAvFuB;AAyFvB,IAAM,qBAAqB,wBAACF,YAA2B;AACrD,QAAM,mBAAmBA,QAAO,cAAcA,QAAO;AACrD,UAAQ,kBAAkB;AAAA,IACxB,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF,GAhB2B;AAkB3B,IAAM,oBAAoB,wBACxBA,SACAG,MACA,KACAC,eACW;AACX,QAAM,wBAAwB,EAAE,GAAG,GAAG,GAAG,EAAE;AAE3C,MAAIJ,QAAO,QAAQ,SAAS,GAAG;AAC7B,UAAM,gBAAgB,kBAAkBA,QAAO,OAAO;AACtD,QAAI,eAAe;AACjB,YAAM,iBAAiBI,WAAU,IAAI,aAAa,KAAK;AAEvD,UAAID,SAAQ,MAAM;AAChB,eAAO,eAAe,IAAI;AAAA,MAC5B,WAAWA,SAAQ,MAAM;AACvB,cAAM,kBAAkBC,WAAU,IAAIJ,QAAO,EAAE,KAAK;AACpD,eAAO,gBAAgB,IAAI;AAAA,MAC7B,OAAO;AACL,eAAO,eAAe,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIG,SAAQ,MAAM;AAChB,aAAO;AAAA,IACT,WAAWA,SAAQ,MAAM;AACvB,YAAM,kBAAkBC,WAAU,IAAIJ,QAAO,EAAE,KAAK;AACpD,aAAO,gBAAgB,IAAI;AAAA,IAC7B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT,GAjC0B;AAmC1B,IAAM,oBAAoB,wBACxBA,SACA,KACA,sBACyB;AACzB,QAAM,gBAAgB,QAAQ,QAAQ,oBAAoB,MAAM,MAAM;AACtE,QAAM,IAAI,QAAQ,QAAQ,QAAQ,OAAO,gBAAgB,UAAU,IAAIA,QAAO,MAAM,GAAG;AACvF,QAAM,IAAI,QAAQ,QAAQ,QAAQ,OAAO,UAAU,IAAIA,QAAO,MAAM,GAAG,MAAM;AAC7E,MAAI,MAAM,UAAa,MAAM,QAAW;AACtC,UAAM,IAAI,MAAM,sCAAsCA,QAAO,EAAE,EAAE;AAAA,EACnE;AACA,SAAO,EAAE,GAAG,GAAG,cAAc;AAC/B,GAZ0B;AAc1B,IAAM,cAAc,wBAClB,KACA,SACA,gBACG;AACH,MAAI,CAACH,0BAAyB;AAC5B,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,QAAM,WAAW,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AAC/D,QAAM,UAAU,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC7D,MAAI,MAAM,QAAQ,QAAQ,QAAQ,OAAO,aAAa;AACtD,QAAM,OAAO,CAAC,GAAG,QAAQ,KAAK,CAAC;AAC/B,QAAM,oBAAoBA,0BAAyB,mBAAmB;AAEtE,QAAM,WAAW,wBAAC,GAAW,MAAc;AACzC,UAAM,OAAO,QAAQ,IAAI,CAAC,GAAG;AAC7B,UAAM,OAAO,QAAQ,IAAI,CAAC,GAAG;AAC7B,WAAO,SAAS,UAAa,SAAS,SAAY,OAAO,OAAO;AAAA,EAClE,GAJiB;AAMjB,MAAI,aAAa,KAAK,KAAK,QAAQ;AACnC,MAAI,QAAQ,MAAM;AAChB,QAAI,mBAAmB;AACrB,uBAAiB,YAAY,SAAS,GAAG;AAAA,IAC3C;AACA,iBAAa,WAAW,QAAQ;AAAA,EAClC;AAEA,aAAW,QAAQ,CAAC,QAAQ;AAC1B,UAAMG,UAAS,QAAQ,IAAI,GAAG;AAC9B,QAAI,CAACA,SAAQ;AACX,YAAM,IAAI,MAAM,4BAA4B,GAAG,EAAE;AAAA,IACnD;AACA,QAAI,mBAAmB;AACrB,YAAM,kBAAkBA,SAAQ,KAAK,KAAK,SAAS;AAAA,IACrD;AAEA,UAAM,iBAAiB,kBAAkBA,SAAQ,KAAK,iBAAiB;AAEvE,QAAI,aAAa;AACf,YAAM,YAAY,mBAAmBA,OAAM;AAC3C,YAAM,mBAAmBA,QAAO,cAAcA,QAAO;AACrD,YAAM,cAAc,UAAU,IAAIA,QAAO,MAAM,GAAG,SAAS;AAC3D,uBAAiB,UAAUA,SAAQ,gBAAgB,WAAW,aAAa,gBAAgB;AAC3F,sBAAgB,SAASA,SAAQ,gBAAgB,GAAG;AACpD,qBAAe,SAASA,SAAQ,gBAAgB,GAAG;AAAA,IACrD;AACA,QAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,gBAAU,IAAIA,QAAO,IAAI,EAAE,GAAG,eAAe,GAAG,GAAG,eAAe,cAAc,CAAC;AAAA,IACnF,OAAO;AACL,gBAAU,IAAIA,QAAO,IAAI,EAAE,GAAG,eAAe,eAAe,GAAG,eAAe,EAAE,CAAC;AAAA,IACnF;AACA,UAAM,QAAQ,QAAQ,oBAAoB,MAAM,cAAc,MAAM,cAAc;AAClF,QAAI,MAAM,QAAQ;AAChB,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH,GAzDoB;AA2DpB,IAAM,qBAAqB,wBACzB,SACA,SACA,IACA,IACA,eACG;AACH,QAAM,oBAAoB,QAAQ,QAAQ,QAAQ,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AACjF,QAAM,mBAAmB,oBAAoB,QAAQ,SAAS,QAAQ;AACtE,QAAM,uBAAuB,wBAAC,MAAc,EAAE,WAAW,kBAA5B;AAC7B,QAAM,mBAAmB,wBAAC,MAAc,EAAE,MAAM,QAAQ,OAAO,EAAE,MAAM,QAAQ,KAAtD;AACzB,SAAO,CAAC,GAAG,WAAW,OAAO,CAAC,EAAE,KAAK,CAAC,YAAY;AAChD,WAAO,iBAAiB,OAAO,KAAK,qBAAqB,OAAO;AAAA,EAClE,CAAC;AACH,GAd2B;AAgB3B,IAAM,WAAW,wBAAC,IAAY,IAAY,QAAQ,MAAc;AAC9D,QAAM,YAAY,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI;AAC3C,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAEA,QAAM,KAAK,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,SAAS,KAAK,EAAE;AACjE,MAAI,IAAI;AACN,UAAM,KAAK,SAAS;AACpB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,IAAI,KAAK,EAAE;AAC7B,SAAO,SAAS,IAAI,KAAK,OAAO,GAAG,QAAQ,CAAC;AAC9C,GAbiB;AAejB,IAAM,YAAY,wBAChB,KACA,SACA,SACA,eACG;AACH,QAAM,KAAK,UAAU,IAAI,QAAQ,EAAE;AACnC,QAAM,KAAK,UAAU,IAAI,QAAQ,EAAE;AACnC,MAAI,OAAO,UAAa,OAAO,QAAW;AACxC,UAAM,IAAI,MAAM,0CAA0C,QAAQ,EAAE,QAAQ,QAAQ,EAAE,EAAE;AAAA,EAC1F;AACA,QAAM,sBAAsB,mBAAmB,SAAS,SAAS,IAAI,IAAI,UAAU;AAKnF,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,gBAAgB,UAAU,IAAI,QAAQ,MAAM,GAAG;AACnD,MAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAgB,UAAU,IAAI,QAAQ,MAAM,GAAG;AAAA,EACjD;AAEA,MAAI;AACJ,MAAI,qBAAqB;AACvB,UAAM;AACN,WAAO;AACP,aAAS;AACT,aAAS;AAET,UAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AAEtE,UAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AAEtE,QAAI,QAAQ,MAAM;AAChB,UAAI,GAAG,IAAI,GAAG,GAAG;AAIf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IACtE,GAAG,IAAI,MACT,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MACjF,OAAO;AAIL,wBAAgB,UAAU,IAAI,QAAQ,MAAM,GAAG;AAE/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,UAAI,GAAG,IAAI,GAAG,GAAG;AAIf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K,OAAO;AAIL,wBAAgB,UAAU,IAAI,QAAQ,MAAM,GAAG;AAE/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K;AAAA,IACF,OAAO;AACL,UAAI,GAAG,IAAI,GAAG,GAAG;AAIf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,IAAI,GAAG,IAC5D,GAAG,IAAI,MACT,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC3F,OAAO;AAIL,wBAAgB,UAAU,IAAI,QAAQ,MAAM,GAAG;AAE/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,IAAI,IAAI,IAC7D,GAAG,IAAI,MACT,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1F;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM;AACN,WAAO;AACP,aAAS;AACT,aAAS;AAET,QAAI,QAAQ,MAAM;AAChB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAC5E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IACpE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,cAAM;AACN,eAAO;AACP,iBAAS;AACT,iBAAS;AACT,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAC7E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IACnE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AACA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAC7E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IACnE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AACA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,cAAM;AACN,eAAO;AACP,iBAAS;AACT,iBAAS;AAET,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAC5E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IACnE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IACpE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAC5E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AACA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IACnE,GAAG,IAAI,MACT,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAC7E,GAAG,CACL,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,MACG,OAAO,MAAM,EACb,KAAK,KAAK,OAAO,EACjB,KAAK,SAAS,gBAAiB,gBAAiB,iBAAkB;AACvE,GA/LkB;AAiMlB,IAAM,aAAa,wBACjB,KACA,YACG;AACH,QAAM,UAAU,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC7D,GAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAC,QAAQ;AACnC,UAAMA,UAAS,QAAQ,IAAI,GAAG;AAE9B,QAAIA,QAAQ,WAAWA,QAAQ,QAAQ,SAAS,GAAG;AACjD,MAAAA,QAAQ,QAAQ,QAAQ,CAAC,WAAW;AAClC,kBAAU,SAAS,QAAQ,IAAI,MAAM,GAAIA,SAAS,OAAO;AAAA,MAC3D,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH,GAdmB;AAgBnB,IAAM,eAAe,wBACnB,KACA,aACG;AACH,QAAM,IAAI,IAAI,OAAO,GAAG;AACxB,WAAS,QAAQ,CAACC,SAAQ,UAAU;AAClC,UAAM,sBAAsB,QAAQ;AAEpC,UAAM,MAAM,UAAU,IAAIA,QAAO,IAAI,GAAG;AACxC,QAAI,QAAQ,QAAW;AACrB,YAAM,IAAI,MAAM,iCAAiCA,QAAO,IAAI,EAAE;AAAA,IAChE;AACA,UAAM,OAAO,EAAE,OAAO,MAAM;AAC5B,SAAK,KAAK,MAAM,CAAC;AACjB,SAAK,KAAK,MAAM,GAAG;AACnB,SAAK,KAAK,MAAM,MAAM;AACtB,SAAK,KAAK,MAAM,GAAG;AACnB,SAAK,KAAK,SAAS,kBAAkB,mBAAmB;AAExD,QAAI,QAAQ,MAAM;AAChB,WAAK,KAAK,MAAM,UAAU;AAC1B,WAAK,KAAK,MAAM,GAAG;AACnB,WAAK,KAAK,MAAM,MAAM;AACtB,WAAK,KAAK,MAAM,GAAG;AAAA,IACrB,WAAW,QAAQ,MAAM;AACvB,WAAK,KAAK,MAAM,MAAM;AACtB,WAAK,KAAK,MAAM,GAAG;AACnB,WAAK,KAAK,MAAM,UAAU;AAC1B,WAAK,KAAK,MAAM,GAAG;AAAA,IACrB;AACA,UAAM,KAAK,GAAG;AAEd,UAAM,OAAOA,QAAO;AAGpB,UAAM,eAAe,SAAS,IAAI;AAElC,UAAM,MAAM,EAAE,OAAO,MAAM;AAC3B,UAAM,cAAc,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AAG7D,UAAM,QAAQ,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,uBAAuB,mBAAmB;AAE9F,UAAM,KAAK,EAAG,YAAY,YAAY;AACtC,UAAM,OAAO,aAAa,QAAQ;AAClC,QACG,KAAK,SAAS,yBAAyB,mBAAmB,EAC1D,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC,KAAK,QAAQ,KAAKJ,0BAAyB,sBAAsB,OAAO,KAAK,EAAE,EAC1F,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,CAAC,EAC9B,KAAK,SAAS,KAAK,QAAQ,EAAE,EAC7B,KAAK,UAAU,KAAK,SAAS,CAAC;AACjC,UAAM;AAAA,MACJ;AAAA,MACA,gBACG,CAAC,KAAK,QAAQ,MAAMA,0BAAyB,sBAAsB,OAAO,KAAK,MAChF,QACC,MAAM,KAAK,SAAS,IAAI,KACzB;AAAA,IACJ;AACA,QAAI,QAAQ,MAAM;AAChB,UAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC;AACpD,YAAM,KAAK,aAAa,gBAAgB,MAAM,KAAK,QAAQ,IAAI,KAAK,MAAc;AAAA,IACpF,WAAW,QAAQ,MAAM;AACvB,UAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,MAAM;AACzD,YAAM,KAAK,aAAa,gBAAgB,MAAM,KAAK,QAAQ,IAAI,KAAK,OAAO,SAAS,GAAG;AAAA,IACzF,OAAO;AACL,UAAI,KAAK,aAAa,qBAA6B,MAAM,KAAK,SAAS,KAAK,GAAG;AAAA,IACjF;AAAA,EACF,CAAC;AACH,GAvEqB;AAyErB,IAAM,oBAAoB,gCACxB,MACA,KACA,OACA,MACA,mBACQ;AACR,YAAU,IAAI,MAAM,EAAE,KAAK,MAAM,CAAC;AAClC,SAAO,MAAM,oBAAoB,KAAK,MAAM,QAAQ,QAAQ,QAAQ,OAAO,KAAK,QAAQ,IAAI;AAC5F,SAAO;AACT,GAV0B;AAYnB,IAAM,OAAuB,gCAAU,KAAK,IAAI,KAAK,SAAS;AACnE,EAAAC,OAAM;AAEN,MAAI,MAAM,wBAAwB,MAAM,MAAM,OAAO,IAAI,GAAG;AAC5D,MAAI,CAACD,0BAAyB;AAC5B,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,QAAM,oBAAoBA,yBAAwB,qBAAqB;AACvE,QAAMQ,MAAK,QAAQ;AACnB,mBAAiBA,IAAG,WAAW;AAC/B,QAAM,WAAWA,IAAG,sBAAsB;AAC1C,QAAMA,IAAG,aAAa;AACtB,QAAMC,WAAU,eAAO,QAAQ,EAAE,IAAI;AACrC,MAAI,MAAM;AAEV,WAAS,QAAQ,CAACL,SAAQ,UAAU;AAClC,UAAM,eAAe,SAASA,QAAO,IAAI;AACzC,UAAM,IAAIK,SAAQ,OAAO,GAAG;AAC5B,UAAM,cAAc,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AAC7D,UAAM,QAAQ,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACxE,UAAM,KAAK,GAAG,YAAY,YAAY;AACtC,UAAM,OAAO,aAAa,QAAQ;AAElC,UAAM,kBAAkBL,QAAO,MAAM,KAAK,OAAO,MAAM,iBAAiB;AACxE,UAAM,OAAO;AACb,gBAAY,OAAO;AACnB,MAAE,OAAO;AAAA,EACX,CAAC;AAED,cAAYK,UAAS,gBAAgB,KAAK;AAC1C,MAAIT,yBAAwB,cAAc;AACxC,iBAAaS,UAAS,QAAQ;AAAA,EAChC;AACA,aAAWA,UAAS,cAAc;AAClC,cAAYA,UAAS,gBAAgB,IAAI;AAEzC,gBAAM;AAAA,IACJA;AAAA,IACA;AAAA,IACAT,yBAAwB,kBAAkB;AAAA,IAC1CQ,IAAG,gBAAgB;AAAA,EACrB;AAGA;AAAA,IACE;AAAA,IACAC;AAAA,IACAT,yBAAwB;AAAA,IACxBA,yBAAwB;AAAA,EAC1B;AACF,GAlDoC;AAoDpC,IAAO,2BAAQ;AAAA,EACb;AACF;AAEA,IAAI,QAAoB;AACtB,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AAEjC,WAAS,YAAY,MAAM;AACzB,OAAG,mBAAmB,MAAM;AAC1B,YAAM,WAAW,SAAS,MAAM;AAChC,aAAO,QAAQ,EAAE,YAAY;AAC7B,aAAO,SAAS,SAAS,CAAC,EAAE,SAAS,EAAE,KAAK,MAAM;AAAA,IACpD,CAAC;AAAA,EACH,CAAC;AAED,WAAS,kBAAkB,MAAM;AAC/B,UAAM,OAAgB;AAAA,MACpB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ,6BAAM,IAAN;AAAA,IACV;AAEA,OAAG,kDAAkD,MAAM;AACzD,YAAM;AAEN,YAAM,MAAM,kBAAkB,QAAQ,GAAG,GAAG,MAAM,IAAI;AACtD,aAAO,GAAG,EAAE,KAAK,EAAE;AACnB,aAAO,UAAU,IAAI,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1D,YAAM,UAAU,kBAAkB,WAAW,KAAK,GAAG,MAAM,IAAI;AAC/D,aAAO,OAAO,EAAE,KAAK,GAAG;AACxB,aAAO,UAAU,IAAI,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAU,OAAO,EAAE,CAAC;AAAA,IACjE,CAAC;AAED,OAAG,kDAAkD,MAAM;AACzD,YAAM;AACN,WAAK,QAAQ;AAEb,YAAM,MAAM,kBAAkB,QAAQ,GAAG,GAAG,MAAM,IAAI;AACtD,aAAO,GAAG,EAAE,KAAK,YAAY;AAC7B,aAAO,UAAU,IAAI,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAE1D,WAAK,QAAQ;AACb,YAAM,UAAU,kBAAkB,WAAW,KAAK,GAAG,MAAM,IAAI;AAC/D,aAAO,OAAO,EAAE,KAAK,YAAY;AACjC,aAAO,UAAU,IAAI,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAU,OAAO,EAAE,CAAC;AAAA,IACjE,CAAC;AAAA,EACH,CAAC;AAED,WAAS,kBAAkB,MAAM;AAC/B,UAAM,UAAU,oBAAI,IAAoB;AAAA,MACtC;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC;AAAA,UACV,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,MAAM;AAAA,UAChB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,GAAG;AAAA,UACb,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,QAAQ,GAAG;AAAA,UACrB,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,MAAM;AAAA,UAChB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,KAAK,GAAG;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,KAAK,GAAG;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,MAAM;AACV,cAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,cAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,cAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AAExD,aAAS,MAAM,MAAM;AACnB,YAAM;AACN,YAAM;AACN,YAAM,2BAA2B,oBAAI,IAAkC;AAAA,QACrE,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACjD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACzD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAChD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QACnE,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,MACrE,CAAC;AACD,cAAQ,QAAQ,CAACG,SAAQ,QAAQ;AAC/B,WAAG,+CAA+C,GAAG,IAAI,MAAM;AAC7D,gBAAM,WAAW,kBAAkBA,SAAQ,KAAK,KAAK;AACrD,iBAAO,QAAQ,EAAE,QAAQ,yBAAyB,IAAI,GAAG,CAAC;AAC1D,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,MAAM,MAAM;AACnB,UAAIO,OAAM;AACV,YAAM;AACN,YAAM,2BAA2B,oBAAI,IAAkC;AAAA,QACrE,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACjD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACzD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAChD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QACnE,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,MACrE,CAAC;AACD,cAAQ,QAAQ,CAACP,SAAQ,QAAQ;AAC/B,WAAG,+CAA+C,GAAG,IAAI,MAAM;AAC7D,gBAAM,WAAW,kBAAkBA,SAAQO,MAAK,KAAK;AACrD,iBAAO,QAAQ,EAAE,QAAQ,yBAAyB,IAAI,GAAG,CAAC;AAC1D,UAAAA,QAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,sBAAsB,MAAM;AACnC,YAAM,0BAA0B,oBAAI,IAAoB;AAAA,QACtD,CAAC,cAAc,eAAe;AAAA,QAC9B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,WAAW,cAAc;AAAA,QAC1B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,mBAAmB,oBAAoB;AAAA,QACxC,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,mBAAmB,oBAAoB;AAAA,MAC1C,CAAC;AACD,cAAQ,QAAQ,CAACP,SAAQ,QAAQ;AAC/B,WAAG,iDAAiD,GAAG,IAAI,MAAM;AAC/D,gBAAM,YAAY,mBAAmBA,OAAM;AAC3C,iBAAO,SAAS,EAAE,KAAK,wBAAwB,IAAI,GAAG,CAAC;AAAA,QACzD,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,WAAS,uCAAuC,MAAM;AACpD,UAAM,UAAU,oBAAI,IAAoB;AAAA,MACtC;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC;AAAA,UACV,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,yBAAyB,oBAAI,IAA4B;AAAA,MAC7D,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC7B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC7B,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,IAChC,CAAC;AAED,UAAM,sCAAsC,oBAAI,IAA4B;AAAA,MAC1E,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,GAAG,CAAC;AAAA,MACxC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,GAAG,CAAC;AAAA,MACxC,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC/B,CAAC;AAED,UAAM,gCAAgC,oBAAI,IAAoB;AAAA,MAC5D,CAAC,aAAa,EAAE;AAAA,MAChB,CAAC,aAAa,EAAE;AAAA,MAChB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,IACnB,CAAC;AACD,UAAM,aAAa,CAAC,GAAG,uBAAuB,KAAK,CAAC;AACpD,OAAG,+DAA+D,MAAM;AACtE,YAAM;AACN,UAAI,SAAS;AACb,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,gBAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,MAAAH,yBAAyB,kBAAkB;AAC3C,cAAQ,QAAQ,CAACG,SAAQ,QAAQ;AAC/B,YAAIA,QAAO,QAAQ,SAAS,GAAG;AAC7B,mBAAS,wBAAwBA,OAAM;AAAA,QACzC;AACA,cAAM,WAAW,kBAAkBA,SAAQ,MAAM;AACjD,eAAO,QAAQ,EAAE,QAAQ,uBAAuB,IAAI,GAAG,CAAC;AACxD,eAAO,MAAM,EAAE,QAAQ,8BAA8B,IAAI,GAAG,CAAC;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAED,OAAG,iEAAiE,MAAM;AACxE,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,YAAM;AACN,YAAM,SAAS;AACf,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,gBAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,uBAAiB,YAAY,SAAS,MAAM;AAC5C,iBAAW,QAAQ,CAACA,YAAW;AAC7B,cAAM,WAAW,UAAU,IAAIA,OAAM;AACrC,eAAO,QAAQ,EAAE,QAAQ,oCAAoC,IAAIA,OAAM,CAAC;AAAA,MAC1E,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,EAAAH,yBAAyB,kBAAkB;AAC3C,KAAG,OAAO,MAAM;AACd,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,UAAM;AACN,UAAM,UAAU,CAAC,WAAW,WAAW,SAAS;AAChD,UAAM,gBAAgB,kBAAkB,OAAO;AAE/C,WAAO,aAAa,EAAE,KAAK,SAAS;AACpC,cAAU,MAAM;AAAA,EAClB,CAAC;AACH;;;ACr0CA,IAAM,YAAY,wBAAC,YACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EACtB;AAAA,EACC,CAAC,MACC;AAAA,uBACe,CAAC,YAAY,QAAQ,mBAAmB,CAAC,CAAC;AAAA,iBAChD,CAAC,cAAc,QAAQ,QAAQ,CAAC,CAAC,WAAW,QAAQ,QAAQ,CAAC,CAAC;AAAA,2BACpD,CAAC,cAAc,QAAQ,WAAW,CAAC,CAAC,WAAW,QAAQ,WAAW,CAAC,CAAC;AAAA,gBAC/E,CAAC,aAAa,QAAQ,QAAQ,CAAC,CAAC;AAAA,gBAChC,CAAC,cAAc,QAAQ,QAAQ,CAAC,CAAC;AAAA;AAE7C,EACC,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,cAID,QAAQ,SAAS;AAAA;AAAA;AAAA,+BAGA,QAAQ,mBAAmB,WAAW,QAAQ,gBAAgB;AAAA,mCAC1D,QAAQ,mBAAmB,WAC1D,QAAQ,qBACV;AAAA,4BAC0B,QAAQ,gBAAgB,WAAW,QAAQ,aAAa;AAAA,2BACzD,QAAQ,kBAAkB,aAAa,QAAQ,cAAc;AAAA,sBAClE,QAAQ,SAAS;AAAA;AAAA;AAAA,cAGzB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA,cAGlB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMlB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOpB,QAAQ,SAAS;AAAA;AAAA,GAxDX;AA4DlB,IAAO,iBAAQ;;;ACrDR,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AACV;", "names": ["getConfig", "dir", "commit", "branch", "clear", "db", "parser", "commit", "branch", "merge", "checkout", "cherryPick", "getConfig", "DEFAULT_GITGRAPH_CONFIG", "clear", "defaultPos", "commit", "branch", "yOffset", "dir", "commitPos", "db", "diagram", "pos"]}