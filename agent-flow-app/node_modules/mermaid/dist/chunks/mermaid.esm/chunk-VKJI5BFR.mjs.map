{"version": 3, "sources": ["../../../src/utils/lineWithOffset.ts"], "sourcesContent": ["import type { EdgeData, Point } from '../types.js';\n\n// We need to draw the lines a bit shorter to avoid drawing\n// under any transparent markers.\n// The offsets are calculated from the markers' dimensions.\nconst markerOffsets = {\n  aggregation: 18,\n  extension: 18,\n  composition: 18,\n  dependency: 6,\n  lollipop: 13.5,\n  arrow_point: 4,\n} as const;\n\n/**\n * Calculate the deltas and angle between two points\n * @param point1 - First point\n * @param point2 - Second point\n * @returns The angle, deltaX and deltaY\n */\nfunction calculateDeltaAndAngle(\n  point1?: Point | [number, number],\n  point2?: Point | [number, number]\n): { angle: number; deltaX: number; deltaY: number } {\n  if (point1 === undefined || point2 === undefined) {\n    return { angle: 0, deltaX: 0, deltaY: 0 };\n  }\n  point1 = pointTransformer(point1);\n  point2 = pointTransformer(point2);\n  const [x1, y1] = [point1.x, point1.y];\n  const [x2, y2] = [point2.x, point2.y];\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  return { angle: Math.atan(deltaY / deltaX), deltaX, deltaY };\n}\n\nconst pointTransformer = (data: Point | [number, number]) => {\n  if (Array.isArray(data)) {\n    return { x: data[0], y: data[1] };\n  }\n  return data;\n};\n\nexport const getLineFunctionsWithOffset = (\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>\n) => {\n  return {\n    x: function (\n      this: void,\n      d: Point | [number, number],\n      i: number,\n      data: (Point | [number, number])[]\n    ) {\n      let offset = 0;\n      const DIRECTION =\n        pointTransformer(data[0]).x < pointTransformer(data[data.length - 1]).x ? 'left' : 'right';\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(data[0], data[1]);\n        offset =\n          markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets] *\n          Math.cos(angle) *\n          (deltaX >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaX } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset =\n          markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets] *\n          Math.cos(angle) *\n          (deltaX >= 0 ? 1 : -1);\n      }\n\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceInYEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const differenceInYStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets];\n      const extraRoom = 1;\n\n      // Adjust the offset if the difference is smaller than the marker height\n      if (\n        differenceToEnd < endMarkerHeight &&\n        differenceToEnd > 0 &&\n        differenceInYEnd < endMarkerHeight\n      ) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === 'right' ? -1 : 1;\n        // Adjust the offset by the amount needed to fit the marker\n        offset -= adjustment;\n      }\n\n      if (\n        differenceToStart < startMarkerHeight &&\n        differenceToStart > 0 &&\n        differenceInYStart < startMarkerHeight\n      ) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === 'right' ? -1 : 1;\n        offset += adjustment;\n      }\n\n      return pointTransformer(d).x + offset;\n    },\n    y: function (\n      this: void,\n      d: Point | [number, number],\n      i: number,\n      data: (Point | [number, number])[]\n    ) {\n      let offset = 0;\n      const DIRECTION =\n        pointTransformer(data[0]).y < pointTransformer(data[data.length - 1]).y ? 'down' : 'up';\n      if (i === 0 && Object.hasOwn(markerOffsets, edge.arrowTypeStart)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(data[0], data[1]);\n        offset =\n          markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets] *\n          Math.abs(Math.sin(angle)) *\n          (deltaY >= 0 ? 1 : -1);\n      } else if (i === data.length - 1 && Object.hasOwn(markerOffsets, edge.arrowTypeEnd)) {\n        const { angle, deltaY } = calculateDeltaAndAngle(\n          data[data.length - 1],\n          data[data.length - 2]\n        );\n        offset =\n          markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets] *\n          Math.abs(Math.sin(angle)) *\n          (deltaY >= 0 ? 1 : -1);\n      }\n\n      const differenceToEnd = Math.abs(\n        pointTransformer(d).y - pointTransformer(data[data.length - 1]).y\n      );\n      const differenceInXEnd = Math.abs(\n        pointTransformer(d).x - pointTransformer(data[data.length - 1]).x\n      );\n      const differenceToStart = Math.abs(pointTransformer(d).y - pointTransformer(data[0]).y);\n      const differenceInXStart = Math.abs(pointTransformer(d).x - pointTransformer(data[0]).x);\n      const startMarkerHeight = markerOffsets[edge.arrowTypeStart as keyof typeof markerOffsets];\n      const endMarkerHeight = markerOffsets[edge.arrowTypeEnd as keyof typeof markerOffsets];\n      const extraRoom = 1;\n\n      // Adjust the offset if the difference is smaller than the marker height\n      if (\n        differenceToEnd < endMarkerHeight &&\n        differenceToEnd > 0 &&\n        differenceInXEnd < endMarkerHeight\n      ) {\n        let adjustment = endMarkerHeight + extraRoom - differenceToEnd;\n        adjustment *= DIRECTION === 'up' ? -1 : 1;\n        // Adjust the offset by the amount needed to fit the marker\n        offset -= adjustment;\n      }\n\n      if (\n        differenceToStart < startMarkerHeight &&\n        differenceToStart > 0 &&\n        differenceInXStart < startMarkerHeight\n      ) {\n        let adjustment = startMarkerHeight + extraRoom - differenceToStart;\n        adjustment *= DIRECTION === 'up' ? -1 : 1;\n        offset += adjustment;\n      }\n      return pointTransformer(d).y + offset;\n    },\n  };\n};\n\nif (import.meta.vitest) {\n  const { it, expect, describe } = import.meta.vitest;\n  describe('calculateDeltaAndAngle', () => {\n    it('should calculate the angle and deltas between two points', () => {\n      expect(calculateDeltaAndAngle([0, 0], [0, 1])).toStrictEqual({\n        angle: 1.5707963267948966,\n        deltaX: 0,\n        deltaY: 1,\n      });\n      expect(calculateDeltaAndAngle([1, 0], [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1,\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, [0, -1])).toStrictEqual({\n        angle: 0.7853981633974483,\n        deltaX: -1,\n        deltaY: -1,\n      });\n      expect(calculateDeltaAndAngle({ x: 1, y: 0 }, { x: 1, y: 0 })).toStrictEqual({\n        angle: NaN,\n        deltaX: 0,\n        deltaY: 0,\n      });\n    });\n\n    it('should calculate the angle and deltas if one point in undefined', () => {\n      expect(calculateDeltaAndAngle(undefined, [0, 1])).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0,\n      });\n      expect(calculateDeltaAndAngle([0, 1], undefined)).toStrictEqual({\n        angle: 0,\n        deltaX: 0,\n        deltaY: 0,\n      });\n    });\n  });\n}\n"], "mappings": ";;;;;AAKA,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AACf;AAQA,SAAS,uBACP,QACA,QACmD;AACnD,MAAI,WAAW,UAAa,WAAW,QAAW;AAChD,WAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,EAC1C;AACA,WAAS,iBAAiB,MAAM;AAChC,WAAS,iBAAiB,MAAM;AAChC,QAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,QAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,QAAM,SAAS,KAAK;AACpB,QAAM,SAAS,KAAK;AACpB,SAAO,EAAE,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,QAAQ,OAAO;AAC7D;AAdS;AAgBT,IAAM,mBAAmB,wBAAC,SAAmC;AAC3D,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE;AAAA,EAClC;AACA,SAAO;AACT,GALyB;AAOlB,IAAM,6BAA6B,wBACxC,SACG;AACH,SAAO;AAAA,IACL,GAAG,gCAED,GACA,GACA,MACA;AACA,UAAI,SAAS;AACb,YAAM,YACJ,iBAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,IAAI,SAAS;AACrF,UAAI,MAAM,KAAK,OAAO,OAAO,eAAe,KAAK,cAAc,GAAG;AAChE,cAAM,EAAE,OAAO,OAAO,IAAI,uBAAuB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,iBACE,cAAc,KAAK,cAA4C,IAC/D,KAAK,IAAI,KAAK,KACb,UAAU,IAAI,IAAI;AAAA,MACvB,WAAW,MAAM,KAAK,SAAS,KAAK,OAAO,OAAO,eAAe,KAAK,YAAY,GAAG;AACnF,cAAM,EAAE,OAAO,OAAO,IAAI;AAAA,UACxB,KAAK,KAAK,SAAS,CAAC;AAAA,UACpB,KAAK,KAAK,SAAS,CAAC;AAAA,QACtB;AACA,iBACE,cAAc,KAAK,YAA0C,IAC7D,KAAK,IAAI,KAAK,KACb,UAAU,IAAI,IAAI;AAAA,MACvB;AAEA,YAAM,kBAAkB,KAAK;AAAA,QAC3B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,mBAAmB,KAAK;AAAA,QAC5B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,oBAAoB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACtF,YAAM,qBAAqB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACvF,YAAM,oBAAoB,cAAc,KAAK,cAA4C;AACzF,YAAM,kBAAkB,cAAc,KAAK,YAA0C;AACrF,YAAM,YAAY;AAGlB,UACE,kBAAkB,mBAClB,kBAAkB,KAClB,mBAAmB,iBACnB;AACA,YAAI,aAAa,kBAAkB,YAAY;AAC/C,sBAAc,cAAc,UAAU,KAAK;AAE3C,kBAAU;AAAA,MACZ;AAEA,UACE,oBAAoB,qBACpB,oBAAoB,KACpB,qBAAqB,mBACrB;AACA,YAAI,aAAa,oBAAoB,YAAY;AACjD,sBAAc,cAAc,UAAU,KAAK;AAC3C,kBAAU;AAAA,MACZ;AAEA,aAAO,iBAAiB,CAAC,EAAE,IAAI;AAAA,IACjC,GA7DG;AAAA,IA8DH,GAAG,gCAED,GACA,GACA,MACA;AACA,UAAI,SAAS;AACb,YAAM,YACJ,iBAAiB,KAAK,CAAC,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,IAAI,SAAS;AACrF,UAAI,MAAM,KAAK,OAAO,OAAO,eAAe,KAAK,cAAc,GAAG;AAChE,cAAM,EAAE,OAAO,OAAO,IAAI,uBAAuB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,iBACE,cAAc,KAAK,cAA4C,IAC/D,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KACvB,UAAU,IAAI,IAAI;AAAA,MACvB,WAAW,MAAM,KAAK,SAAS,KAAK,OAAO,OAAO,eAAe,KAAK,YAAY,GAAG;AACnF,cAAM,EAAE,OAAO,OAAO,IAAI;AAAA,UACxB,KAAK,KAAK,SAAS,CAAC;AAAA,UACpB,KAAK,KAAK,SAAS,CAAC;AAAA,QACtB;AACA,iBACE,cAAc,KAAK,YAA0C,IAC7D,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KACvB,UAAU,IAAI,IAAI;AAAA,MACvB;AAEA,YAAM,kBAAkB,KAAK;AAAA,QAC3B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,mBAAmB,KAAK;AAAA,QAC5B,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE;AAAA,MAClE;AACA,YAAM,oBAAoB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACtF,YAAM,qBAAqB,KAAK,IAAI,iBAAiB,CAAC,EAAE,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;AACvF,YAAM,oBAAoB,cAAc,KAAK,cAA4C;AACzF,YAAM,kBAAkB,cAAc,KAAK,YAA0C;AACrF,YAAM,YAAY;AAGlB,UACE,kBAAkB,mBAClB,kBAAkB,KAClB,mBAAmB,iBACnB;AACA,YAAI,aAAa,kBAAkB,YAAY;AAC/C,sBAAc,cAAc,OAAO,KAAK;AAExC,kBAAU;AAAA,MACZ;AAEA,UACE,oBAAoB,qBACpB,oBAAoB,KACpB,qBAAqB,mBACrB;AACA,YAAI,aAAa,oBAAoB,YAAY;AACjD,sBAAc,cAAc,OAAO,KAAK;AACxC,kBAAU;AAAA,MACZ;AACA,aAAO,iBAAiB,CAAC,EAAE,IAAI;AAAA,IACjC,GA5DG;AAAA,EA6DL;AACF,GAhI0C;AAkI1C,IAAI,QAAoB;AACtB,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AACjC,WAAS,0BAA0B,MAAM;AACvC,OAAG,4DAA4D,MAAM;AACnE,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc;AAAA,QAC3D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QAC5D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QACpE,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,cAAc;AAAA,QAC3E,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAED,OAAG,mEAAmE,MAAM;AAC1E,aAAO,uBAAuB,QAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc;AAAA,QAC9D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,uBAAuB,CAAC,GAAG,CAAC,GAAG,MAAS,CAAC,EAAE,cAAc;AAAA,QAC9D,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}