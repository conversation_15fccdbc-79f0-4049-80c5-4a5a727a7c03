{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/shapes/handDrawnShapeStyles.ts"], "sourcesContent": ["import { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport type { Node } from '../../types.js';\n\n// Striped fill like start or fork nodes in state diagrams\nexport const solidStateFill = (color: string) => {\n  const { handDrawnSeed } = getConfig();\n  return {\n    fill: color,\n    hachureAngle: 120, // angle of hachure,\n    hachureGap: 4,\n    fillWeight: 2,\n    roughness: 0.7,\n    stroke: color,\n    seed: handDrawnSeed,\n  };\n};\n\nexport const compileStyles = (node: Node) => {\n  // node.cssCompiledStyles is an array of strings in the form of 'key: value' where key is the css property and value is the value\n  // the array is the styles of node from the classes it is using\n  // node.cssStyles is an array of styles directly set on the node\n  // concat the arrays and remove duplicates such that the values from node.cssStyles are used if there are duplicates\n  const stylesMap = styles2Map([...(node.cssCompiledStyles || []), ...(node.cssStyles || [])]);\n  return { stylesMap, stylesArray: [...stylesMap] };\n};\n\nexport const styles2Map = (styles: string[]) => {\n  const styleMap = new Map<string, string>();\n  styles.forEach((style) => {\n    const [key, value] = style.split(':');\n    styleMap.set(key.trim(), value?.trim());\n  });\n  return styleMap;\n};\nexport const isLabelStyle = (key: string) => {\n  return (\n    key === 'color' ||\n    key === 'font-size' ||\n    key === 'font-family' ||\n    key === 'font-weight' ||\n    key === 'font-style' ||\n    key === 'text-decoration' ||\n    key === 'text-align' ||\n    key === 'text-transform' ||\n    key === 'line-height' ||\n    key === 'letter-spacing' ||\n    key === 'word-spacing' ||\n    key === 'text-shadow' ||\n    key === 'text-overflow' ||\n    key === 'white-space' ||\n    key === 'word-wrap' ||\n    key === 'word-break' ||\n    key === 'overflow-wrap' ||\n    key === 'hyphens'\n  );\n};\nexport const styles2String = (node: Node) => {\n  const { stylesArray } = compileStyles(node);\n  const labelStyles: string[] = [];\n  const nodeStyles: string[] = [];\n  const borderStyles: string[] = [];\n  const backgroundStyles: string[] = [];\n\n  stylesArray.forEach((style) => {\n    const key = style[0];\n    if (isLabelStyle(key)) {\n      labelStyles.push(style.join(':') + ' !important');\n    } else {\n      nodeStyles.push(style.join(':') + ' !important');\n      if (key.includes('stroke')) {\n        borderStyles.push(style.join(':') + ' !important');\n      }\n      if (key === 'fill') {\n        backgroundStyles.push(style.join(':') + ' !important');\n      }\n    }\n  });\n\n  return {\n    labelStyles: labelStyles.join(';'),\n    nodeStyles: nodeStyles.join(';'),\n    stylesArray,\n    borderStyles,\n    backgroundStyles,\n  };\n};\n\n// Striped fill like start or fork nodes in state diagrams\n// TODO remove any\nexport const userNodeOverrides = (node: Node, options: any) => {\n  const { themeVariables, handDrawnSeed } = getConfig();\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  // index the style array to a map object\n  const result = Object.assign(\n    {\n      roughness: 0.7,\n      fill: stylesMap.get('fill') || mainBkg,\n      fillStyle: 'hachure', // solid fill\n      fillWeight: 4,\n      hachureGap: 5.2,\n      stroke: stylesMap.get('stroke') || nodeBorder,\n      seed: handDrawnSeed,\n      strokeWidth: stylesMap.get('stroke-width')?.replace('px', '') || 1.3,\n      fillLineDash: [0, 0],\n    },\n    options\n  );\n  return result;\n};\n"], "mappings": ";;;;;;;;AAIO,IAAM,iBAAiB,wBAAC,UAAkB;AAC/C,QAAM,EAAE,cAAc,IAAI,UAAU;AACpC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,cAAc;AAAA;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF,GAX8B;AAavB,IAAM,gBAAgB,wBAAC,SAAe;AAK3C,QAAM,YAAY,WAAW,CAAC,GAAI,KAAK,qBAAqB,CAAC,GAAI,GAAI,KAAK,aAAa,CAAC,CAAE,CAAC;AAC3F,SAAO,EAAE,WAAW,aAAa,CAAC,GAAG,SAAS,EAAE;AAClD,GAP6B;AAStB,IAAM,aAAa,wBAAC,WAAqB;AAC9C,QAAM,WAAW,oBAAI,IAAoB;AACzC,SAAO,QAAQ,CAAC,UAAU;AACxB,UAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,aAAS,IAAI,IAAI,KAAK,GAAG,OAAO,KAAK,CAAC;AAAA,EACxC,CAAC;AACD,SAAO;AACT,GAP0B;AAQnB,IAAM,eAAe,wBAAC,QAAgB;AAC3C,SACE,QAAQ,WACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,iBACR,QAAQ,gBACR,QAAQ,qBACR,QAAQ,gBACR,QAAQ,oBACR,QAAQ,iBACR,QAAQ,oBACR,QAAQ,kBACR,QAAQ,iBACR,QAAQ,mBACR,QAAQ,iBACR,QAAQ,eACR,QAAQ,gBACR,QAAQ,mBACR,QAAQ;AAEZ,GArB4B;AAsBrB,IAAM,gBAAgB,wBAAC,SAAe;AAC3C,QAAM,EAAE,YAAY,IAAI,cAAc,IAAI;AAC1C,QAAM,cAAwB,CAAC;AAC/B,QAAM,aAAuB,CAAC;AAC9B,QAAM,eAAyB,CAAC;AAChC,QAAM,mBAA6B,CAAC;AAEpC,cAAY,QAAQ,CAAC,UAAU;AAC7B,UAAM,MAAM,MAAM,CAAC;AACnB,QAAI,aAAa,GAAG,GAAG;AACrB,kBAAY,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,IAClD,OAAO;AACL,iBAAW,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAC/C,UAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,qBAAa,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACnD;AACA,UAAI,QAAQ,QAAQ;AAClB,yBAAiB,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;AAAA,MACvD;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL,aAAa,YAAY,KAAK,GAAG;AAAA,IACjC,YAAY,WAAW,KAAK,GAAG;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GA7B6B;AAiCtB,IAAM,oBAAoB,wBAAC,MAAY,YAAiB;AAC7D,QAAM,EAAE,gBAAgB,cAAc,IAAI,UAAU;AACpD,QAAM,EAAE,YAAY,QAAQ,IAAI;AAChC,QAAM,EAAE,UAAU,IAAI,cAAc,IAAI;AAGxC,QAAM,SAAS,OAAO;AAAA,IACpB;AAAA,MACE,WAAW;AAAA,MACX,MAAM,UAAU,IAAI,MAAM,KAAK;AAAA,MAC/B,WAAW;AAAA;AAAA,MACX,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,QAAQ,UAAU,IAAI,QAAQ,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,aAAa,UAAU,IAAI,cAAc,GAAG,QAAQ,MAAM,EAAE,KAAK;AAAA,MACjE,cAAc,CAAC,GAAG,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT,GArBiC;", "names": []}