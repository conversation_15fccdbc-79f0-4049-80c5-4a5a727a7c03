{"version": 3, "sources": ["../../../src/diagrams/common/svgDrawCommon.ts"], "sourcesContent": ["import { sanitizeUrl } from '@braintree/sanitize-url';\nimport type { SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { lineBreakRegex } from './common.js';\nimport type {\n  Bound,\n  D3ImageElement,\n  D3RectElement,\n  D3TSpanElement,\n  D3TextElement,\n  D3UseElement,\n  RectData,\n  TextData,\n  TextObject,\n} from './commonTypes.js';\n\nexport const drawRect = (element: SVG | SVGGroup, rectData: RectData): D3RectElement => {\n  const rectElement: D3RectElement = element.append('rect');\n  rectElement.attr('x', rectData.x);\n  rectElement.attr('y', rectData.y);\n  rectElement.attr('fill', rectData.fill);\n  rectElement.attr('stroke', rectData.stroke);\n  rectElement.attr('width', rectData.width);\n  rectElement.attr('height', rectData.height);\n  if (rectData.name) {\n    rectElement.attr('name', rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr('rx', rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr('ry', rectData.ry);\n  }\n\n  if (rectData.attrs !== undefined) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n\n  if (rectData.class) {\n    rectElement.attr('class', rectData.class);\n  }\n\n  return rectElement;\n};\n\n/**\n * Draws a background rectangle\n *\n * @param element - Diagram (reference for bounds)\n * @param bounds - Shape of the rectangle\n */\nexport const drawBackgroundRect = (element: SVG | SVGGroup, bounds: Bound): void => {\n  const rectData: RectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: 'rect',\n  };\n  const rectElement: D3RectElement = drawRect(element, rectData);\n  rectElement.lower();\n};\n\nexport const drawText = (element: SVG | SVGGroup, textData: TextData): D3TextElement => {\n  const nText: string = textData.text.replace(lineBreakRegex, ' ');\n\n  const textElem: D3TextElement = element.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.attr('class', 'legend');\n\n  textElem.style('text-anchor', textData.anchor);\n  if (textData.class) {\n    textElem.attr('class', textData.class);\n  }\n\n  const tspan: D3TSpanElement = textElem.append('tspan');\n  tspan.attr('x', textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n\n  return textElem;\n};\n\nexport const drawImage = (elem: SVG | SVGGroup, x: number, y: number, link: string): void => {\n  const imageElement: D3ImageElement = elem.append('image');\n  imageElement.attr('x', x);\n  imageElement.attr('y', y);\n  const sanitizedLink: string = sanitizeUrl(link);\n  imageElement.attr('xlink:href', sanitizedLink);\n};\n\nexport const drawEmbeddedImage = (\n  element: SVG | SVGGroup,\n  x: number,\n  y: number,\n  link: string\n): void => {\n  const imageElement: D3UseElement = element.append('use');\n  imageElement.attr('x', x);\n  imageElement.attr('y', y);\n  const sanitizedLink: string = sanitizeUrl(link);\n  imageElement.attr('xlink:href', `#${sanitizedLink}`);\n};\n\nexport const getNoteRect = (): RectData => {\n  const noteRectData: RectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: '#EDF2AE',\n    stroke: '#666',\n    anchor: 'start',\n    rx: 0,\n    ry: 0,\n  };\n  return noteRectData;\n};\n\nexport const getTextObj = (): TextObject => {\n  const testObject: TextObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    'text-anchor': 'start',\n    style: '#666',\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n  };\n  return testObject;\n};\n"], "mappings": ";;;;;;;;;;;;AAAA,0BAA4B;AAerB,IAAM,WAAW,wBAAC,SAAyB,aAAsC;AACtF,QAAM,cAA6B,QAAQ,OAAO,MAAM;AACxD,cAAY,KAAK,KAAK,SAAS,CAAC;AAChC,cAAY,KAAK,KAAK,SAAS,CAAC;AAChC,cAAY,KAAK,QAAQ,SAAS,IAAI;AACtC,cAAY,KAAK,UAAU,SAAS,MAAM;AAC1C,cAAY,KAAK,SAAS,SAAS,KAAK;AACxC,cAAY,KAAK,UAAU,SAAS,MAAM;AAC1C,MAAI,SAAS,MAAM;AACjB,gBAAY,KAAK,QAAQ,SAAS,IAAI;AAAA,EACxC;AACA,MAAI,SAAS,IAAI;AACf,gBAAY,KAAK,MAAM,SAAS,EAAE;AAAA,EACpC;AACA,MAAI,SAAS,IAAI;AACf,gBAAY,KAAK,MAAM,SAAS,EAAE;AAAA,EACpC;AAEA,MAAI,SAAS,UAAU,QAAW;AAChC,eAAW,WAAW,SAAS,OAAO;AACpC,kBAAY,KAAK,SAAS,SAAS,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,EACF;AAEA,MAAI,SAAS,OAAO;AAClB,gBAAY,KAAK,SAAS,SAAS,KAAK;AAAA,EAC1C;AAEA,SAAO;AACT,GA7BwB;AAqCjB,IAAM,qBAAqB,wBAAC,SAAyB,WAAwB;AAClF,QAAM,WAAqB;AAAA,IACzB,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV,OAAO,OAAO,QAAQ,OAAO;AAAA,IAC7B,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC9B,MAAM,OAAO;AAAA,IACb,QAAQ,OAAO;AAAA,IACf,OAAO;AAAA,EACT;AACA,QAAM,cAA6B,SAAS,SAAS,QAAQ;AAC7D,cAAY,MAAM;AACpB,GAZkC;AAc3B,IAAM,WAAW,wBAAC,SAAyB,aAAsC;AACtF,QAAM,QAAgB,SAAS,KAAK,QAAQ,gBAAgB,GAAG;AAE/D,QAAM,WAA0B,QAAQ,OAAO,MAAM;AACrD,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,SAAS,QAAQ;AAE/B,WAAS,MAAM,eAAe,SAAS,MAAM;AAC7C,MAAI,SAAS,OAAO;AAClB,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AAEA,QAAM,QAAwB,SAAS,OAAO,OAAO;AACrD,QAAM,KAAK,KAAK,SAAS,IAAI,SAAS,aAAa,CAAC;AACpD,QAAM,KAAK,KAAK;AAEhB,SAAO;AACT,GAlBwB;AAoBjB,IAAM,YAAY,wBAAC,MAAsB,GAAW,GAAW,SAAuB;AAC3F,QAAM,eAA+B,KAAK,OAAO,OAAO;AACxD,eAAa,KAAK,KAAK,CAAC;AACxB,eAAa,KAAK,KAAK,CAAC;AACxB,QAAM,oBAAwB,iCAAY,IAAI;AAC9C,eAAa,KAAK,cAAc,aAAa;AAC/C,GANyB;AAQlB,IAAM,oBAAoB,wBAC/B,SACA,GACA,GACA,SACS;AACT,QAAM,eAA6B,QAAQ,OAAO,KAAK;AACvD,eAAa,KAAK,KAAK,CAAC;AACxB,eAAa,KAAK,KAAK,CAAC;AACxB,QAAM,oBAAwB,iCAAY,IAAI;AAC9C,eAAa,KAAK,cAAc,IAAI,aAAa,EAAE;AACrD,GAXiC;AAa1B,IAAM,cAAc,6BAAgB;AACzC,QAAM,eAAyB;AAAA,IAC7B,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACA,SAAO;AACT,GAb2B;AAepB,IAAM,aAAa,6BAAkB;AAC1C,QAAM,aAAyB;AAAA,IAC7B,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AACA,SAAO;AACT,GAd0B;", "names": []}