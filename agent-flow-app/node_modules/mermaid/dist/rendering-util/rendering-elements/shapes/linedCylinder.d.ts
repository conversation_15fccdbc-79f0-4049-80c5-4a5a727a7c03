import type { Node } from '../../types.js';
import type { D3Selection } from '../../../types.js';
export declare const createCylinderPathD: (x: number, y: number, width: number, height: number, rx: number, ry: number, outerOffset: number) => string;
export declare const createOuterCylinderPathD: (x: number, y: number, width: number, height: number, rx: number, ry: number, outerOffset: number) => string;
export declare const createInnerCylinderPathD: (x: number, y: number, width: number, height: number, rx: number, ry: number) => string;
export declare function linedCylinder<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node): Promise<import("d3-selection").Selection<SVGGElement, unknown, Element | null, unknown>>;
