'use client';

import React, { useState } from 'react';
import FileUpload from '@/components/FileUpload';
import ProcessResults from '@/components/ProcessResults';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Bot, Zap } from 'lucide-react';

interface ProcessStep {
  id: string;
  title: string;
  description: string;
  type: 'manual' | 'automated' | 'decision';
}

interface AutomationAgent {
  name: string;
  description: string;
  capabilities: string[];
  estimatedEffort: string;
}

interface AnalysisResult {
  mermaidDiagram: string;
  processSteps: ProcessStep[];
  automationAgents: AutomationAgent[];
}

export default function Home() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setAnalysisResult(null);
    setError(null);
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setAnalysisResult(null);
    setError(null);
  };

  const handleAnalyzeProcess = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/analyze-process', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || errorData.error || 'Failed to analyze process');
      }

      const result: AnalysisResult = await response.json();

      // Validate the response structure
      if (!result.mermaidDiagram || !result.processSteps || !result.automationAgents) {
        throw new Error('Invalid response structure from server');
      }

      setAnalysisResult(result);
    } catch (err) {
      console.error('Error analyzing process:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Bot className="h-8 w-8 text-blue-500" />
              <Zap className="h-6 w-6 text-yellow-500" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                AgentFlow
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Transform process diagrams into automated workflows
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!analysisResult ? (
          <div className="space-y-8">
            {/* Upload Section */}
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Upload Your Process Diagram
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Upload an image of your process flow, workflow diagram, or flowchart.
                Claude AI will analyze it and create a Mermaid diagram with automation suggestions.
              </p>
            </div>

            <FileUpload
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              selectedFile={selectedFile}
              isProcessing={isProcessing}
            />

            {selectedFile && !isProcessing && (
              <div className="text-center">
                <button
                  onClick={handleAnalyzeProcess}
                  className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Bot className="h-5 w-5" />
                  <span>Analyze Process</span>
                </button>
              </div>
            )}

            {error && (
              <div className="max-w-2xl mx-auto p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Back to Upload Button */}
            <div className="flex justify-between items-center">
              <button
                onClick={() => {
                  setAnalysisResult(null);
                  setSelectedFile(null);
                }}
                className="inline-flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/20 rounded-lg transition-colors"
              >
                <span>← Upload New Diagram</span>
              </button>
            </div>

            {/* Results */}
            <ErrorBoundary>
              <ProcessResults
                mermaidDiagram={analysisResult.mermaidDiagram}
                processSteps={analysisResult.processSteps}
                automationAgents={analysisResult.automationAgents}
                isLoading={false}
              />
            </ErrorBoundary>
          </div>
        )}

        {isProcessing && (
          <ErrorBoundary>
            <ProcessResults
              mermaidDiagram=""
              processSteps={[]}
              automationAgents={[]}
              isLoading={true}
            />
          </ErrorBoundary>
        )}
      </main>
    </div>
  );
}
