import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { stepTitle, stepDescription, stepType } = await request.json();

    if (!stepTitle || !stepDescription || !stepType) {
      return NextResponse.json(
        { error: 'Missing required fields: stepTitle, stepDescription, stepType' },
        { status: 400 }
      );
    }

    // Generate code example with <PERSON>
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 1500,
      temperature: 0.2,
      messages: [
        {
          role: "user",
          content: `Generate a practical, production-ready code example for this process step:

Title: ${stepTitle}
Description: ${stepDescription}
Type: ${stepType}

Requirements:
1. Create realistic, implementable code (JavaScript/TypeScript preferred)
2. Include proper error handling
3. Add meaningful comments
4. Make it specific to the step description
5. Include relevant imports/dependencies
6. Follow best practices for the step type

For automated steps: Focus on async operations, API calls, data processing
For manual steps: Focus on user interfaces, validation, state management  
For decision steps: Focus on conditional logic, routing, business rules

Return only the code example without any markdown formatting or explanations.`
        }
      ]
    });

    const content = response.content[0];
    if (!content || content.type !== 'text') {
      throw new Error('No response from Claude');
    }

    return NextResponse.json({
      codeExample: content.text.trim()
    });

  } catch (error) {
    console.error('Error generating code example:', error);
    
    // Return a fallback code example
    const fallbackCode = generateFallbackCode(
      await request.json().then(data => data.stepType),
      await request.json().then(data => data.stepTitle),
      await request.json().then(data => data.stepDescription)
    );
    
    return NextResponse.json({
      codeExample: fallbackCode
    });
  }
}

function generateFallbackCode(stepType: string, stepTitle: string, stepDescription: string): string {
  const functionName = stepTitle.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
  
  switch (stepType) {
    case 'automated':
      return `// Automated Step: ${stepTitle}
async function ${functionName}() {
  try {
    console.log('Starting automated step: ${stepTitle}');
    
    // ${stepDescription}
    const result = await performAutomatedTask();
    
    console.log('Completed automated step successfully');
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in ${stepTitle}:', error);
    throw new Error(\`Failed to execute ${stepTitle}: \${error.message}\`);
  }
}

async function performAutomatedTask() {
  // Implement your specific automation logic here
  return { processed: true };
}`;

    case 'decision':
      return `// Decision Step: ${stepTitle}
function ${functionName}(context) {
  console.log('Evaluating decision: ${stepTitle}');
  
  // ${stepDescription}
  try {
    const decision = evaluateCondition(context);
    
    console.log(\`Decision result: \${decision}\`);
    return {
      decision,
      path: decision ? 'success_path' : 'alternative_path',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in decision step:', error);
    return {
      decision: false,
      path: 'error_path',
      error: error.message
    };
  }
}

function evaluateCondition(context) {
  // Implement your decision logic here
  return context && context.isValid;
}`;

    case 'manual':
    default:
      return `// Manual Step: ${stepTitle}
class ${stepTitle.replace(/[^a-zA-Z0-9]/g, '')}Handler {
  constructor() {
    this.stepName = '${stepTitle}';
    this.description = '${stepDescription}';
  }

  async execute(userInput = {}) {
    console.log(\`Starting manual step: \${this.stepName}\`);
    
    try {
      // Validate user input
      const validatedInput = await this.validateInput(userInput);
      
      // Process the step
      const result = await this.processStep(validatedInput);
      
      console.log(\`Completed manual step: \${this.stepName}\`);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(\`Error in manual step \${this.stepName}:\`, error);
      throw error;
    }
  }

  async validateInput(input) {
    // Implement validation logic specific to this step
    if (!input || typeof input !== 'object') {
      throw new Error('Invalid input provided');
    }
    return input;
  }

  async processStep(input) {
    // Implement the core logic for this manual step
    return { processed: true, input };
  }
}`;
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Code generation API endpoint. Use POST to generate code examples.' },
    { status: 200 }
  );
}
