import { NextRequest, NextResponse } from 'next/server';
import Anthropic from '@anthropic-ai/sdk';

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Convert file to base64 for OpenAI Vision API
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Image = buffer.toString('base64');
    const mimeType = file.type;

    // Analyze the image with Claude Vision API
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 2000,
      temperature: 0.3,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this process diagram/flowchart image and provide:

1. A Mermaid flowchart diagram code that represents the process shown in the image
2. A detailed breakdown of each step in the process
3. Suggestions for automation agents that could be built to automate parts of this process

Please respond with a JSON object in this exact format:
{
  "mermaidDiagram": "flowchart TD\\n    A[Start] --> B[Step 1]\\n    B --> C[Step 2]\\n    C --> D[End]",
  "processSteps": [
    {
      "id": "step1",
      "title": "Step Title",
      "description": "Detailed description of what happens in this step",
      "type": "manual" | "automated" | "decision",
      "codeExample": "// Optional: Basic code example for this step"
    }
  ],
  "automationAgents": [
    {
      "name": "Agent Name",
      "description": "What this agent would do",
      "capabilities": ["capability1", "capability2"],
      "estimatedEffort": "Low/Medium/High"
    }
  ]
}

Make sure the Mermaid diagram uses proper syntax and the process steps are comprehensive. For automation agents, focus on realistic AI/software agents that could actually be built to automate manual tasks in the process.`
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: mimeType,
                data: base64Image
              }
            }
          ]
        }
      ]
    });

    const content = response.content[0];
    if (!content || content.type !== 'text') {
      throw new Error('No response from Claude');
    }

    const textContent = content.text;

    // Parse the JSON response
    let analysisResult;
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = textContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0]);
      } else {
        analysisResult = JSON.parse(textContent);
      }
    } catch (parseError) {
      console.error('Failed to parse Claude response:', textContent);
      throw new Error('Failed to parse analysis result');
    }

    // Validate the response structure
    if (!analysisResult.mermaidDiagram || !analysisResult.processSteps || !analysisResult.automationAgents) {
      throw new Error('Invalid response structure from Claude');
    }

    return NextResponse.json(analysisResult);

  } catch (error) {
    console.error('Error analyzing process:', error);
    
    // Return a more specific error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return NextResponse.json(
      { 
        error: 'Failed to analyze process diagram',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Process analysis API endpoint. Use POST to upload and analyze a file.' },
    { status: 200 }
  );
}
