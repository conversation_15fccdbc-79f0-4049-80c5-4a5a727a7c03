'use client';

import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { Copy, Download, Bot, Lightbulb, Code, X } from 'lucide-react';

interface ProcessStep {
  id: string;
  title: string;
  description: string;
  type: 'manual' | 'automated' | 'decision';
  codeExample?: string;
}

interface AutomationAgent {
  name: string;
  description: string;
  capabilities: string[];
  estimatedEffort: string;
}

interface ProcessResultsProps {
  mermaidDiagram: string;
  processSteps: ProcessStep[];
  automationAgents: AutomationAgent[];
  isLoading: boolean;
}

const ProcessResults: React.FC<ProcessResultsProps> = ({
  mermaidDiagram,
  processSteps,
  automationAgents,
  isLoading
}) => {
  const mermaidRef = useRef<HTMLDivElement>(null);
  const [selectedStep, setSelectedStep] = useState<ProcessStep | null>(null);
  const [generatingCode, setGeneratingCode] = useState(false);

  useEffect(() => {
    if (mermaidDiagram && mermaidRef.current) {
      const renderMermaid = async () => {
        try {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'ui-sans-serif, system-ui, sans-serif'
          });

          // Clear previous content
          mermaidRef.current!.innerHTML = '';

          // Generate unique ID for this diagram
          const id = `mermaid-${Date.now()}`;

          // Render the diagram
          const { svg } = await mermaid.render(id, mermaidDiagram);
          mermaidRef.current!.innerHTML = svg;
        } catch (error) {
          console.error('Error rendering Mermaid diagram:', error);
          mermaidRef.current!.innerHTML = `
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p class="text-red-700">Error rendering diagram. Please check the Mermaid syntax.</p>
              <pre class="mt-2 text-sm text-gray-600 overflow-auto">${mermaidDiagram}</pre>
            </div>
          `;
        }
      };

      renderMermaid();
    }
  }, [mermaidDiagram]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateCodeExample = async (step: ProcessStep) => {
    if (step.codeExample) {
      setSelectedStep(step);
      return;
    }

    setGeneratingCode(true);
    try {
      const response = await fetch('/api/generate-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stepTitle: step.title,
          stepDescription: step.description,
          stepType: step.type,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate code example');
      }

      const data = await response.json();
      const updatedStep = { ...step, codeExample: data.codeExample };
      setSelectedStep(updatedStep);
    } catch (error) {
      console.error('Error generating code:', error);
      // Show a fallback code example
      const fallbackCode = generateFallbackCode(step);
      const updatedStep = { ...step, codeExample: fallbackCode };
      setSelectedStep(updatedStep);
    } finally {
      setGeneratingCode(false);
    }
  };

  const generateFallbackCode = (step: ProcessStep): string => {
    switch (step.type) {
      case 'automated':
        return `// Automated step: ${step.title}
async function ${step.id.replace(/[^a-zA-Z0-9]/g, '')}() {
  try {
    // ${step.description}
    console.log('Executing: ${step.title}');

    // Add your automation logic here
    const result = await performAutomatedTask();

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in ${step.title}:', error);
    throw error;
  }
}`;

      case 'decision':
        return `// Decision step: ${step.title}
function ${step.id.replace(/[^a-zA-Z0-9]/g, '')}(input) {
  // ${step.description}

  if (input.condition) {
    console.log('Condition met for: ${step.title}');
    return 'path_a';
  } else {
    console.log('Alternative path for: ${step.title}');
    return 'path_b';
  }
}

// Usage example:
const decision = ${step.id.replace(/[^a-zA-Z0-9]/g, '')}({
  condition: true // Replace with your actual condition
});`;

      case 'manual':
      default:
        return `// Manual step: ${step.title}
// ${step.description}

class ${step.id.replace(/[^a-zA-Z0-9]/g, '').charAt(0).toUpperCase() + step.id.replace(/[^a-zA-Z0-9]/g, '').slice(1)}Handler {
  async execute() {
    console.log('Starting manual step: ${step.title}');

    // This step requires human intervention
    // Consider implementing:
    // - User interface for data input
    // - Validation logic
    // - Progress tracking
    // - Error handling

    const userInput = await this.getUserInput();
    const validated = this.validateInput(userInput);

    if (validated) {
      return this.processInput(userInput);
    } else {
      throw new Error('Invalid input for ${step.title}');
    }
  }

  async getUserInput() {
    // Implement user input collection
    return {};
  }

  validateInput(input) {
    // Implement validation logic
    return true;
  }

  processInput(input) {
    // Process the validated input
    return { success: true, data: input };
  }
}`;
    }
  };

  const downloadDiagram = () => {
    const svg = mermaidRef.current?.querySelector('svg');
    if (svg) {
      const svgData = new XMLSerializer().serializeToString(svg);
      const blob = new Blob([svgData], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'process-diagram.svg';
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Mermaid Diagram Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Process Flow Diagram
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => copyToClipboard(mermaidDiagram)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
              <span>Copy Code</span>
            </button>
            <button
              onClick={downloadDiagram}
              className="flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Download SVG</span>
            </button>
          </div>
        </div>
        
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
          <div ref={mermaidRef} className="mermaid-diagram"></div>
        </div>
      </div>

      {/* Process Steps Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Process Steps Analysis
        </h2>
        
        <div className="space-y-4">
          {processSteps.map((step, index) => (
            <div
              key={step.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => generateCodeExample(step)}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                    ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}
                    ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                    ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                  `}>
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                        {step.title}
                      </h3>
                      <span className={`
                        px-2 py-1 text-xs rounded-full
                        ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}
                        ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                        ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                      `}>
                        {step.type}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Code className="h-4 w-4" />
                      <span>Click for code</span>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    {step.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Automation Agents Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Bot className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Suggested Automation Agents
          </h2>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          {automationAgents.map((agent, index) => (
            <div
              key={index}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-3">
                <Lightbulb className="h-6 w-6 text-yellow-500 flex-shrink-0 mt-1" />
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {agent.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-3">
                    {agent.description}
                  </p>
                  
                  <div className="mb-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Capabilities:
                    </h4>
                    <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 space-y-1">
                      {agent.capabilities.map((capability, capIndex) => (
                        <li key={capIndex}>{capability}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Estimated effort: {agent.estimatedEffort}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Code Example Modal */}
      {selectedStep && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <Code className="h-6 w-6 text-blue-500" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    Code Example: {selectedStep.title}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedStep.type} step implementation
                  </p>
                </div>
              </div>
              <button
                onClick={() => setSelectedStep(null)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
              <div className="mb-4">
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {selectedStep.description}
                </p>
              </div>

              <div className="relative">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Implementation Example
                  </span>
                  <button
                    onClick={() => copyToClipboard(selectedStep.codeExample || '')}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <Copy className="h-3 w-3" />
                    <span>Copy</span>
                  </button>
                </div>

                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{selectedStep.codeExample}</code>
                </pre>
              </div>

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  💡 Implementation Notes:
                </h4>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  {selectedStep.type === 'automated' && (
                    <>
                      <li>• Consider adding error handling and retry logic</li>
                      <li>• Implement logging for monitoring and debugging</li>
                      <li>• Add input validation and sanitization</li>
                      <li>• Consider rate limiting for external API calls</li>
                    </>
                  )}
                  {selectedStep.type === 'manual' && (
                    <>
                      <li>• Design user-friendly interfaces for data input</li>
                      <li>• Implement comprehensive validation</li>
                      <li>• Add progress indicators for long processes</li>
                      <li>• Consider workflow state management</li>
                    </>
                  )}
                  {selectedStep.type === 'decision' && (
                    <>
                      <li>• Ensure all decision paths are handled</li>
                      <li>• Add logging for decision audit trails</li>
                      <li>• Consider making conditions configurable</li>
                      <li>• Test edge cases and boundary conditions</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading overlay for code generation */}
      {generatingCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-900 dark:text-gray-100">Generating code example...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessResults;
