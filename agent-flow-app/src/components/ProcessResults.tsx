'use client';

import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import { Copy, Download, Bot, Lightbulb } from 'lucide-react';

interface ProcessStep {
  id: string;
  title: string;
  description: string;
  type: 'manual' | 'automated' | 'decision';
}

interface AutomationAgent {
  name: string;
  description: string;
  capabilities: string[];
  estimatedEffort: string;
}

interface ProcessResultsProps {
  mermaidDiagram: string;
  processSteps: ProcessStep[];
  automationAgents: AutomationAgent[];
  isLoading: boolean;
}

const ProcessResults: React.FC<ProcessResultsProps> = ({
  mermaidDiagram,
  processSteps,
  automationAgents,
  isLoading
}) => {
  const mermaidRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (mermaidDiagram && mermaidRef.current) {
      const renderMermaid = async () => {
        try {
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'ui-sans-serif, system-ui, sans-serif'
          });

          // Clear previous content
          mermaidRef.current!.innerHTML = '';

          // Generate unique ID for this diagram
          const id = `mermaid-${Date.now()}`;

          // Render the diagram
          const { svg } = await mermaid.render(id, mermaidDiagram);
          mermaidRef.current!.innerHTML = svg;
        } catch (error) {
          console.error('Error rendering Mermaid diagram:', error);
          mermaidRef.current!.innerHTML = `
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p class="text-red-700">Error rendering diagram. Please check the Mermaid syntax.</p>
              <pre class="mt-2 text-sm text-gray-600 overflow-auto">${mermaidDiagram}</pre>
            </div>
          `;
        }
      };

      renderMermaid();
    }
  }, [mermaidDiagram]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadDiagram = () => {
    const svg = mermaidRef.current?.querySelector('svg');
    if (svg) {
      const svgData = new XMLSerializer().serializeToString(svg);
      const blob = new Blob([svgData], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'process-diagram.svg';
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Mermaid Diagram Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Process Flow Diagram
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => copyToClipboard(mermaidDiagram)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Copy className="h-4 w-4" />
              <span>Copy Code</span>
            </button>
            <button
              onClick={downloadDiagram}
              className="flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Download SVG</span>
            </button>
          </div>
        </div>
        
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
          <div ref={mermaidRef} className="mermaid-diagram"></div>
        </div>
      </div>

      {/* Process Steps Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Process Steps Analysis
        </h2>
        
        <div className="space-y-4">
          {processSteps.map((step, index) => (
            <div
              key={step.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                    ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}
                    ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                    ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                  `}>
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      {step.title}
                    </h3>
                    <span className={`
                      px-2 py-1 text-xs rounded-full
                      ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}
                      ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                      ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                    `}>
                      {step.type}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">
                    {step.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Automation Agents Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Bot className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Suggested Automation Agents
          </h2>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          {automationAgents.map((agent, index) => (
            <div
              key={index}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-3">
                <Lightbulb className="h-6 w-6 text-yellow-500 flex-shrink-0 mt-1" />
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {agent.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-3">
                    {agent.description}
                  </p>
                  
                  <div className="mb-3">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Capabilities:
                    </h4>
                    <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 space-y-1">
                      {agent.capabilities.map((capability, capIndex) => (
                        <li key={capIndex}>{capability}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Estimated effort: {agent.estimatedEffort}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProcessResults;
