# AgentFlow - Process Diagram Analyzer

AgentFlow is a web application that transforms process diagrams into automated workflows. Upload an image of your process flow, workflow diagram, or flowchart, and our AI will analyze it to create a Mermaid diagram with automation suggestions.

## Features

- **File Upload**: Support for drag-and-drop and browse file upload
- **AI Analysis**: Uses OpenAI's GPT-4 Vision API to analyze process diagrams
- **Mermaid Diagrams**: Generates interactive Mermaid flowchart diagrams
- **Process Breakdown**: Detailed analysis of each step in the process
- **Automation Suggestions**: AI-powered recommendations for automation agents
- **Modern UI**: Clean, responsive interface built with Next.js and Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **File Handling**: react-dropzone for drag-and-drop functionality
- **AI Integration**: OpenAI GPT-4 Vision API
- **Diagram Rendering**: Mermaid.js for interactive flowcharts
- **Icons**: Lucide React icons

## Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agent-flow-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

   You can get an API key from: https://platform.openai.com/api-keys

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open the application**
   Navigate to [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

1. **Upload a Process Diagram**
   - Drag and drop an image file onto the upload area, or
   - Click to browse and select an image file
   - Supported formats: PNG, JPG, JPEG, GIF, SVG, WebP (max 10MB)

2. **Analyze the Process**
   - Click the "Analyze Process" button
   - Wait for the AI to process your diagram (usually takes 10-30 seconds)

3. **View Results**
   - **Mermaid Diagram**: Interactive flowchart representation of your process
   - **Process Steps**: Detailed breakdown of each step with type classification
   - **Automation Agents**: AI-suggested agents that could automate parts of your process

4. **Export Results**
   - Copy the Mermaid diagram code to use in other tools
   - Download the diagram as an SVG file

## File Structure

```
agent-flow-app/
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── analyze-process/
│   │   │       └── route.ts          # API endpoint for file processing
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   └── page.tsx                  # Main page component
│   └── components/
│       ├── FileUpload.tsx            # File upload component
│       └── ProcessResults.tsx        # Results display component
├── public/                           # Static assets
├── .env.example                      # Environment variables template
└── package.json                      # Dependencies and scripts
```

## API Endpoints

### POST /api/analyze-process

Analyzes an uploaded process diagram image.

**Request**: Multipart form data with a `file` field containing the image.

**Response**: JSON object with:
- `mermaidDiagram`: Mermaid flowchart code
- `processSteps`: Array of process step objects
- `automationAgents`: Array of suggested automation agents

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Adding New Features

1. **Frontend Components**: Add new React components in `src/components/`
2. **API Routes**: Add new API endpoints in `src/app/api/`
3. **Styling**: Use Tailwind CSS classes for styling

## Deployment

The application can be deployed to any platform that supports Next.js:

- **Vercel** (recommended): Connect your GitHub repository
- **Netlify**: Use the Next.js build command
- **Docker**: Build a container with the included Dockerfile (if added)

Make sure to set the `OPENAI_API_KEY` environment variable in your deployment platform.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please open an issue on the GitHub repository.
