{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/AgentFlow/agent-flow-app/src/app/api/analyze-process/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport Anthropic from '@anthropic-ai/sdk';\n\n// Initialize Anthropic client\nconst anthropic = new Anthropic({\n  apiKey: process.env.ANTHROPIC_API_KEY,\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No file provided' },\n        { status: 400 }\n      );\n    }\n\n    // Convert file to base64 for OpenAI Vision API\n    const bytes = await file.arrayBuffer();\n    const buffer = Buffer.from(bytes);\n    const base64Image = buffer.toString('base64');\n    const mimeType = file.type;\n\n    // Analyze the image with Claude Vision API\n    const response = await anthropic.messages.create({\n      model: \"claude-3-5-sonnet-20241022\",\n      max_tokens: 2000,\n      temperature: 0.3,\n      messages: [\n        {\n          role: \"user\",\n          content: [\n            {\n              type: \"text\",\n              text: `Analyze this process diagram/flowchart image and provide:\n\n1. A Mermaid flowchart diagram code that represents the process shown in the image\n2. A detailed breakdown of each step in the process\n3. Suggestions for automation agents that could be built to automate parts of this process\n\nPlease respond with a JSON object in this exact format:\n{\n  \"mermaidDiagram\": \"flowchart TD\\\\n    A[Start] --> B[Step 1]\\\\n    B --> C[Step 2]\\\\n    C --> D[End]\",\n  \"processSteps\": [\n    {\n      \"id\": \"step1\",\n      \"title\": \"Step Title\",\n      \"description\": \"Detailed description of what happens in this step\",\n      \"type\": \"manual\" | \"automated\" | \"decision\"\n    }\n  ],\n  \"automationAgents\": [\n    {\n      \"name\": \"Agent Name\",\n      \"description\": \"What this agent would do\",\n      \"capabilities\": [\"capability1\", \"capability2\"],\n      \"estimatedEffort\": \"Low/Medium/High\"\n    }\n  ]\n}\n\nMake sure the Mermaid diagram uses proper syntax and the process steps are comprehensive. For automation agents, focus on realistic AI/software agents that could actually be built to automate manual tasks in the process.`\n            },\n            {\n              type: \"image\",\n              source: {\n                type: \"base64\",\n                media_type: mimeType,\n                data: base64Image\n              }\n            }\n          ]\n        }\n      ]\n    });\n\n    const content = response.content[0];\n    if (!content || content.type !== 'text') {\n      throw new Error('No response from Claude');\n    }\n\n    const textContent = content.text;\n\n    // Parse the JSON response\n    let analysisResult;\n    try {\n      // Extract JSON from the response (in case there's extra text)\n      const jsonMatch = textContent.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        analysisResult = JSON.parse(jsonMatch[0]);\n      } else {\n        analysisResult = JSON.parse(textContent);\n      }\n    } catch (parseError) {\n      console.error('Failed to parse Claude response:', textContent);\n      throw new Error('Failed to parse analysis result');\n    }\n\n    // Validate the response structure\n    if (!analysisResult.mermaidDiagram || !analysisResult.processSteps || !analysisResult.automationAgents) {\n      throw new Error('Invalid response structure from Claude');\n    }\n\n    return NextResponse.json(analysisResult);\n\n  } catch (error) {\n    console.error('Error analyzing process:', error);\n    \n    // Return a more specific error message\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n    \n    return NextResponse.json(\n      { \n        error: 'Failed to analyze process diagram',\n        details: errorMessage\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json(\n    { message: 'Process analysis API endpoint. Use POST to upload and analyze a file.' },\n    { status: 200 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,8BAA8B;AAC9B,MAAM,YAAY,IAAI,6LAAA,CAAA,UAAS,CAAC;IAC9B,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;AACvC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAC3B,MAAM,cAAc,OAAO,QAAQ,CAAC;QACpC,MAAM,WAAW,KAAK,IAAI;QAE1B,2CAA2C;QAC3C,MAAM,WAAW,MAAM,UAAU,QAAQ,CAAC,MAAM,CAAC;YAC/C,OAAO;YACP,YAAY;YACZ,aAAa;YACb,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;wBACP;4BACE,MAAM;4BACN,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;4NA2BuM,CAAC;wBACjN;wBACA;4BACE,MAAM;4BACN,QAAQ;gCACN,MAAM;gCACN,YAAY;gCACZ,MAAM;4BACR;wBACF;qBACD;gBACH;aACD;QACH;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE;QACnC,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,QAAQ;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,QAAQ,IAAI;QAEhC,0BAA0B;QAC1B,IAAI;QACJ,IAAI;YACF,8DAA8D;YAC9D,MAAM,YAAY,YAAY,KAAK,CAAC;YACpC,IAAI,WAAW;gBACb,iBAAiB,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;YAC1C,OAAO;gBACL,iBAAiB,KAAK,KAAK,CAAC;YAC9B;QACF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,kCAAkC;QAClC,IAAI,CAAC,eAAe,cAAc,IAAI,CAAC,eAAe,YAAY,IAAI,CAAC,eAAe,gBAAgB,EAAE;YACtG,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,uCAAuC;QACvC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;IAAwE,GACnF;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}