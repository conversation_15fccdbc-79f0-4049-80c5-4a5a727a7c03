(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-353BL4L5.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "populateCommonDb": ()=>populateCommonDb
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs [app-client] (ecmascript)");
;
// src/diagrams/common/populateCommonDb.ts
function populateCommonDb(ast, db) {
    if (ast.accDescr) {
        var _db_setAccDescription;
        (_db_setAccDescription = db.setAccDescription) === null || _db_setAccDescription === void 0 ? void 0 : _db_setAccDescription.call(db, ast.accDescr);
    }
    if (ast.accTitle) {
        var _db_setAccTitle;
        (_db_setAccTitle = db.setAccTitle) === null || _db_setAccTitle === void 0 ? void 0 : _db_setAccTitle.call(db, ast.accTitle);
    }
    if (ast.title) {
        var _db_setDiagramTitle;
        (_db_setDiagramTitle = db.setDiagramTitle) === null || _db_setDiagramTitle === void 0 ? void 0 : _db_setDiagramTitle.call(db, ast.title);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(populateCommonDb, "populateCommonDb");
;
}),
"[project]/node_modules/mermaid/dist/chunks/mermaid.core/diagram-ZTM2IBQH.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "diagram": ()=>diagram
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$353BL4L5$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-353BL4L5.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-P3VETL53.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs [app-client] (ecmascript)");
// src/diagrams/radar/parser.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mermaid$2d$js$2f$parser$2f$dist$2f$mermaid$2d$parser$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@mermaid-js/parser/dist/mermaid-parser.core.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mermaid$2d$js$2f$parser$2f$dist$2f$mermaid$2d$parser$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@mermaid-js/parser/dist/mermaid-parser.core.mjs [app-client] (ecmascript) <locals>");
;
;
;
;
// src/diagrams/radar/db.ts
var defaultOptions = {
    showLegend: true,
    ticks: 5,
    max: null,
    min: 0,
    graticule: "circle"
};
var defaultRadarData = {
    axes: [],
    curves: [],
    options: defaultOptions
};
var data = structuredClone(defaultRadarData);
var DEFAULT_RADAR_CONFIG = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].radar;
var getConfig2 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>{
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanAndMerge"])({
        ...DEFAULT_RADAR_CONFIG,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])().radar
    });
    return config;
}, "getConfig");
var getAxes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>data.axes, "getAxes");
var getCurves = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>data.curves, "getCurves");
var getOptions = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>data.options, "getOptions");
var setAxes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((axes)=>{
    data.axes = axes.map((axis)=>{
        var _axis_label;
        return {
            name: axis.name,
            label: (_axis_label = axis.label) !== null && _axis_label !== void 0 ? _axis_label : axis.name
        };
    });
}, "setAxes");
var setCurves = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((curves)=>{
    data.curves = curves.map((curve)=>{
        var _curve_label;
        return {
            name: curve.name,
            label: (_curve_label = curve.label) !== null && _curve_label !== void 0 ? _curve_label : curve.name,
            entries: computeCurveEntries(curve.entries)
        };
    });
}, "setCurves");
var computeCurveEntries = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((entries)=>{
    if (entries[0].axis == void 0) {
        return entries.map((entry)=>entry.value);
    }
    const axes = getAxes();
    if (axes.length === 0) {
        throw new Error("Axes must be populated before curves for reference entries");
    }
    return axes.map((axis)=>{
        const entry = entries.find((entry2)=>{
            var _entry2_axis;
            return ((_entry2_axis = entry2.axis) === null || _entry2_axis === void 0 ? void 0 : _entry2_axis.$refText) === axis.name;
        });
        if (entry === void 0) {
            throw new Error("Missing entry for axis " + axis.label);
        }
        return entry.value;
    });
}, "computeCurveEntries");
var setOptions = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((options)=>{
    var _optionMap_showLegend, _optionMap_ticks, _optionMap_max, _optionMap_min, _optionMap_graticule;
    const optionMap = options.reduce((acc, option)=>{
        acc[option.name] = option;
        return acc;
    }, {});
    var _optionMap_showLegend_value, _optionMap_ticks_value, _optionMap_max_value, _optionMap_min_value, _optionMap_graticule_value;
    data.options = {
        showLegend: (_optionMap_showLegend_value = (_optionMap_showLegend = optionMap.showLegend) === null || _optionMap_showLegend === void 0 ? void 0 : _optionMap_showLegend.value) !== null && _optionMap_showLegend_value !== void 0 ? _optionMap_showLegend_value : defaultOptions.showLegend,
        ticks: (_optionMap_ticks_value = (_optionMap_ticks = optionMap.ticks) === null || _optionMap_ticks === void 0 ? void 0 : _optionMap_ticks.value) !== null && _optionMap_ticks_value !== void 0 ? _optionMap_ticks_value : defaultOptions.ticks,
        max: (_optionMap_max_value = (_optionMap_max = optionMap.max) === null || _optionMap_max === void 0 ? void 0 : _optionMap_max.value) !== null && _optionMap_max_value !== void 0 ? _optionMap_max_value : defaultOptions.max,
        min: (_optionMap_min_value = (_optionMap_min = optionMap.min) === null || _optionMap_min === void 0 ? void 0 : _optionMap_min.value) !== null && _optionMap_min_value !== void 0 ? _optionMap_min_value : defaultOptions.min,
        graticule: (_optionMap_graticule_value = (_optionMap_graticule = optionMap.graticule) === null || _optionMap_graticule === void 0 ? void 0 : _optionMap_graticule.value) !== null && _optionMap_graticule_value !== void 0 ? _optionMap_graticule_value : defaultOptions.graticule
    };
}, "setOptions");
var clear2 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clear"])();
    data = structuredClone(defaultRadarData);
}, "clear");
var db = {
    getAxes,
    getCurves,
    getOptions,
    setAxes,
    setCurves,
    setOptions,
    getConfig: getConfig2,
    clear: clear2,
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccTitle"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccTitle"],
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setDiagramTitle"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDiagramTitle"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccDescription"],
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccDescription"]
};
;
var populate = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((ast)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$353BL4L5$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["populateCommonDb"])(ast, db);
    const { axes, curves, options } = ast;
    db.setAxes(axes);
    db.setCurves(curves);
    db.setOptions(options);
}, "populate");
var parser = {
    parse: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(async (input)=>{
        const ast = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mermaid$2d$js$2f$parser$2f$dist$2f$mermaid$2d$parser$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["parse"])("radar", input);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug(ast);
        populate(ast);
    }, "parse")
};
// src/diagrams/radar/renderer.ts
var draw = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((_text, id, _version, diagram2)=>{
    const db2 = diagram2.db;
    const axes = db2.getAxes();
    const curves = db2.getCurves();
    const options = db2.getOptions();
    const config = db2.getConfig();
    const title = db2.getDiagramTitle();
    const svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectSvgElement"])(id);
    const g = drawFrame(svg, config);
    var _options_max;
    const maxValue = (_options_max = options.max) !== null && _options_max !== void 0 ? _options_max : Math.max(...curves.map((curve)=>Math.max(...curve.entries)));
    const minValue = options.min;
    const radius = Math.min(config.width, config.height) / 2;
    drawGraticule(g, axes, radius, options.ticks, options.graticule);
    drawAxes(g, axes, radius, config);
    drawCurves(g, axes, curves, minValue, maxValue, options.graticule, config);
    drawLegend(g, curves, options.showLegend, config);
    g.append("text").attr("class", "radarTitle").text(title).attr("x", 0).attr("y", -config.height / 2 - config.marginTop);
}, "draw");
var drawFrame = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((svg, config)=>{
    const totalWidth = config.width + config.marginLeft + config.marginRight;
    const totalHeight = config.height + config.marginTop + config.marginBottom;
    const center = {
        x: config.marginLeft + config.width / 2,
        y: config.marginTop + config.height / 2
    };
    svg.attr("viewbox", "0 0 ".concat(totalWidth, " ").concat(totalHeight)).attr("width", totalWidth).attr("height", totalHeight);
    return svg.append("g").attr("transform", "translate(".concat(center.x, ", ").concat(center.y, ")"));
}, "drawFrame");
var drawGraticule = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((g, axes, radius, ticks, graticule)=>{
    if (graticule === "circle") {
        for(let i = 0; i < ticks; i++){
            const r = radius * (i + 1) / ticks;
            g.append("circle").attr("r", r).attr("class", "radarGraticule");
        }
    } else if (graticule === "polygon") {
        const numAxes = axes.length;
        for(let i = 0; i < ticks; i++){
            const r = radius * (i + 1) / ticks;
            const points = axes.map((_, j)=>{
                const angle = 2 * j * Math.PI / numAxes - Math.PI / 2;
                const x = r * Math.cos(angle);
                const y = r * Math.sin(angle);
                return "".concat(x, ",").concat(y);
            }).join(" ");
            g.append("polygon").attr("points", points).attr("class", "radarGraticule");
        }
    }
}, "drawGraticule");
var drawAxes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((g, axes, radius, config)=>{
    const numAxes = axes.length;
    for(let i = 0; i < numAxes; i++){
        const label = axes[i].label;
        const angle = 2 * i * Math.PI / numAxes - Math.PI / 2;
        g.append("line").attr("x1", 0).attr("y1", 0).attr("x2", radius * config.axisScaleFactor * Math.cos(angle)).attr("y2", radius * config.axisScaleFactor * Math.sin(angle)).attr("class", "radarAxisLine");
        g.append("text").text(label).attr("x", radius * config.axisLabelFactor * Math.cos(angle)).attr("y", radius * config.axisLabelFactor * Math.sin(angle)).attr("class", "radarAxisLabel");
    }
}, "drawAxes");
function drawCurves(g, axes, curves, minValue, maxValue, graticule, config) {
    const numAxes = axes.length;
    const radius = Math.min(config.width, config.height) / 2;
    curves.forEach((curve, index)=>{
        if (curve.entries.length !== numAxes) {
            return;
        }
        const points = curve.entries.map((entry, i)=>{
            const angle = 2 * Math.PI * i / numAxes - Math.PI / 2;
            const r = relativeRadius(entry, minValue, maxValue, radius);
            const x = r * Math.cos(angle);
            const y = r * Math.sin(angle);
            return {
                x,
                y
            };
        });
        if (graticule === "circle") {
            g.append("path").attr("d", closedRoundCurve(points, config.curveTension)).attr("class", "radarCurve-".concat(index));
        } else if (graticule === "polygon") {
            g.append("polygon").attr("points", points.map((p)=>"".concat(p.x, ",").concat(p.y)).join(" ")).attr("class", "radarCurve-".concat(index));
        }
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawCurves, "drawCurves");
function relativeRadius(value, minValue, maxValue, radius) {
    const clippedValue = Math.min(Math.max(value, minValue), maxValue);
    return radius * (clippedValue - minValue) / (maxValue - minValue);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(relativeRadius, "relativeRadius");
function closedRoundCurve(points, tension) {
    const numPoints = points.length;
    let d = "M".concat(points[0].x, ",").concat(points[0].y);
    for(let i = 0; i < numPoints; i++){
        const p0 = points[(i - 1 + numPoints) % numPoints];
        const p1 = points[i];
        const p2 = points[(i + 1) % numPoints];
        const p3 = points[(i + 2) % numPoints];
        const cp1 = {
            x: p1.x + (p2.x - p0.x) * tension,
            y: p1.y + (p2.y - p0.y) * tension
        };
        const cp2 = {
            x: p2.x - (p3.x - p1.x) * tension,
            y: p2.y - (p3.y - p1.y) * tension
        };
        d += " C".concat(cp1.x, ",").concat(cp1.y, " ").concat(cp2.x, ",").concat(cp2.y, " ").concat(p2.x, ",").concat(p2.y);
    }
    return "".concat(d, " Z");
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(closedRoundCurve, "closedRoundCurve");
function drawLegend(g, curves, showLegend, config) {
    if (!showLegend) {
        return;
    }
    const legendX = (config.width / 2 + config.marginRight) * 3 / 4;
    const legendY = -(config.height / 2 + config.marginTop) * 3 / 4;
    const lineHeight = 20;
    curves.forEach((curve, index)=>{
        const itemGroup = g.append("g").attr("transform", "translate(".concat(legendX, ", ").concat(legendY + index * lineHeight, ")"));
        itemGroup.append("rect").attr("width", 12).attr("height", 12).attr("class", "radarLegendBox-".concat(index));
        itemGroup.append("text").attr("x", 16).attr("y", 0).attr("class", "radarLegendText").text(curve.label);
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawLegend, "drawLegend");
var renderer = {
    draw
};
// src/diagrams/radar/styles.ts
var genIndexStyles = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((themeVariables, radarOptions)=>{
    let sections = "";
    for(let i = 0; i < themeVariables.THEME_COLOR_LIMIT; i++){
        const indexColor = themeVariables["cScale".concat(i)];
        sections += "\n		.radarCurve-".concat(i, " {\n			color: ").concat(indexColor, ";\n			fill: ").concat(indexColor, ";\n			fill-opacity: ").concat(radarOptions.curveOpacity, ";\n			stroke: ").concat(indexColor, ";\n			stroke-width: ").concat(radarOptions.curveStrokeWidth, ";\n		}\n		.radarLegendBox-").concat(i, " {\n			fill: ").concat(indexColor, ";\n			fill-opacity: ").concat(radarOptions.curveOpacity, ";\n			stroke: ").concat(indexColor, ";\n		}\n		");
    }
    return sections;
}, "genIndexStyles");
var buildRadarStyleOptions = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((radar)=>{
    const defaultThemeVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeVariables"])();
    const currentConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])();
    const themeVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanAndMerge"])(defaultThemeVariables, currentConfig.themeVariables);
    const radarOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanAndMerge"])(themeVariables.radar, radar);
    return {
        themeVariables,
        radarOptions
    };
}, "buildRadarStyleOptions");
var styles = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    let { radar } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    const { themeVariables, radarOptions } = buildRadarStyleOptions(radar);
    return "\n	.radarTitle {\n		font-size: ".concat(themeVariables.fontSize, ";\n		color: ").concat(themeVariables.titleColor, ";\n		dominant-baseline: hanging;\n		text-anchor: middle;\n	}\n	.radarAxisLine {\n		stroke: ").concat(radarOptions.axisColor, ";\n		stroke-width: ").concat(radarOptions.axisStrokeWidth, ";\n	}\n	.radarAxisLabel {\n		dominant-baseline: middle;\n		text-anchor: middle;\n		font-size: ").concat(radarOptions.axisLabelFontSize, "px;\n		color: ").concat(radarOptions.axisColor, ";\n	}\n	.radarGraticule {\n		fill: ").concat(radarOptions.graticuleColor, ";\n		fill-opacity: ").concat(radarOptions.graticuleOpacity, ";\n		stroke: ").concat(radarOptions.graticuleColor, ";\n		stroke-width: ").concat(radarOptions.graticuleStrokeWidth, ";\n	}\n	.radarLegendText {\n		text-anchor: start;\n		font-size: ").concat(radarOptions.legendFontSize, "px;\n		dominant-baseline: hanging;\n	}\n	").concat(genIndexStyles(themeVariables, radarOptions), "\n	");
}, "styles");
// src/diagrams/radar/diagram.ts
var diagram = {
    parser,
    db,
    renderer,
    styles
};
;
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ASSERT_EXISTS": ()=>ASSERT_EXISTS,
    "ASSERT_NEVER_REACH_HERE": ()=>ASSERT_NEVER_REACH_HERE,
    "addFlag": ()=>addFlag,
    "cc": ()=>cc,
    "insertToSet": ()=>insertToSet,
    "isCharacter": ()=>isCharacter
});
function cc(char) {
    return char.charCodeAt(0);
}
function insertToSet(item, set) {
    if (Array.isArray(item)) {
        item.forEach(function(subItem) {
            set.push(subItem);
        });
    } else {
        set.push(item);
    }
}
function addFlag(flagObj, flagKey) {
    if (flagObj[flagKey] === true) {
        throw "duplicate flag " + flagKey;
    }
    const x = flagObj[flagKey];
    flagObj[flagKey] = true;
}
function ASSERT_EXISTS(obj) {
    // istanbul ignore next
    if (obj === undefined) {
        throw Error("Internal Error - Should never get here!");
    }
    return true;
}
function ASSERT_NEVER_REACH_HERE() {
    throw Error("Internal Error - Should never get here!");
}
function isCharacter(obj) {
    return obj["type"] === "Character";
} //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "digitsCharCodes": ()=>digitsCharCodes,
    "whitespaceCodes": ()=>whitespaceCodes,
    "wordCharCodes": ()=>wordCharCodes
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js [app-client] (ecmascript)");
;
const digitsCharCodes = [];
for(let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("0"); i <= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("9"); i++){
    digitsCharCodes.push(i);
}
const wordCharCodes = [
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("_")
].concat(digitsCharCodes);
for(let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("a"); i <= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("z"); i++){
    wordCharCodes.push(i);
}
for(let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("A"); i <= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("Z"); i++){
    wordCharCodes.push(i);
}
const whitespaceCodes = [
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])(" "),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\f"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\n"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\r"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\t"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\v"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\t"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u00a0"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u1680"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2000"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2001"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2002"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2003"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2004"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2005"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2006"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2007"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2008"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2009"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u200a"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2028"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2029"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u202f"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u205f"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u3000"),
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\ufeff")
]; //# sourceMappingURL=character-classes.js.map
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "RegExpParser": ()=>RegExpParser
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js [app-client] (ecmascript)");
;
;
// consts and utilities
const hexDigitPattern = /[0-9a-fA-F]/;
const decimalPattern = /[0-9]/;
const decimalPatternNoZero = /[1-9]/;
class RegExpParser {
    saveState() {
        return {
            idx: this.idx,
            input: this.input,
            groupIdx: this.groupIdx
        };
    }
    restoreState(newState) {
        this.idx = newState.idx;
        this.input = newState.input;
        this.groupIdx = newState.groupIdx;
    }
    pattern(input) {
        // parser state
        this.idx = 0;
        this.input = input;
        this.groupIdx = 0;
        this.consumeChar("/");
        const value = this.disjunction();
        this.consumeChar("/");
        const flags = {
            type: "Flags",
            loc: {
                begin: this.idx,
                end: input.length
            },
            global: false,
            ignoreCase: false,
            multiLine: false,
            unicode: false,
            sticky: false
        };
        while(this.isRegExpFlag()){
            switch(this.popChar()){
                case "g":
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addFlag"])(flags, "global");
                    break;
                case "i":
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addFlag"])(flags, "ignoreCase");
                    break;
                case "m":
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addFlag"])(flags, "multiLine");
                    break;
                case "u":
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addFlag"])(flags, "unicode");
                    break;
                case "y":
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addFlag"])(flags, "sticky");
                    break;
            }
        }
        if (this.idx !== this.input.length) {
            throw Error("Redundant input: " + this.input.substring(this.idx));
        }
        return {
            type: "Pattern",
            flags: flags,
            value: value,
            loc: this.loc(0)
        };
    }
    disjunction() {
        const alts = [];
        const begin = this.idx;
        alts.push(this.alternative());
        while(this.peekChar() === "|"){
            this.consumeChar("|");
            alts.push(this.alternative());
        }
        return {
            type: "Disjunction",
            value: alts,
            loc: this.loc(begin)
        };
    }
    alternative() {
        const terms = [];
        const begin = this.idx;
        while(this.isTerm()){
            terms.push(this.term());
        }
        return {
            type: "Alternative",
            value: terms,
            loc: this.loc(begin)
        };
    }
    term() {
        if (this.isAssertion()) {
            return this.assertion();
        } else {
            return this.atom();
        }
    }
    assertion() {
        const begin = this.idx;
        switch(this.popChar()){
            case "^":
                return {
                    type: "StartAnchor",
                    loc: this.loc(begin)
                };
            case "$":
                return {
                    type: "EndAnchor",
                    loc: this.loc(begin)
                };
            // '\b' or '\B'
            case "\\":
                switch(this.popChar()){
                    case "b":
                        return {
                            type: "WordBoundary",
                            loc: this.loc(begin)
                        };
                    case "B":
                        return {
                            type: "NonWordBoundary",
                            loc: this.loc(begin)
                        };
                }
                // istanbul ignore next
                throw Error("Invalid Assertion Escape");
            // '(?=' or '(?!'
            case "(":
                this.consumeChar("?");
                let type;
                switch(this.popChar()){
                    case "=":
                        type = "Lookahead";
                        break;
                    case "!":
                        type = "NegativeLookahead";
                        break;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(type);
                const disjunction = this.disjunction();
                this.consumeChar(")");
                return {
                    type: type,
                    value: disjunction,
                    loc: this.loc(begin)
                };
        }
        // istanbul ignore next
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_NEVER_REACH_HERE"])();
    }
    quantifier() {
        let isBacktracking = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
        let range = undefined;
        const begin = this.idx;
        switch(this.popChar()){
            case "*":
                range = {
                    atLeast: 0,
                    atMost: Infinity
                };
                break;
            case "+":
                range = {
                    atLeast: 1,
                    atMost: Infinity
                };
                break;
            case "?":
                range = {
                    atLeast: 0,
                    atMost: 1
                };
                break;
            case "{":
                const atLeast = this.integerIncludingZero();
                switch(this.popChar()){
                    case "}":
                        range = {
                            atLeast: atLeast,
                            atMost: atLeast
                        };
                        break;
                    case ",":
                        let atMost;
                        if (this.isDigit()) {
                            atMost = this.integerIncludingZero();
                            range = {
                                atLeast: atLeast,
                                atMost: atMost
                            };
                        } else {
                            range = {
                                atLeast: atLeast,
                                atMost: Infinity
                            };
                        }
                        this.consumeChar("}");
                        break;
                }
                // throwing exceptions from "ASSERT_EXISTS" during backtracking
                // causes severe performance degradations
                if (isBacktracking === true && range === undefined) {
                    return undefined;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(range);
                break;
        }
        // throwing exceptions from "ASSERT_EXISTS" during backtracking
        // causes severe performance degradations
        if (isBacktracking === true && range === undefined) {
            return undefined;
        }
        // istanbul ignore else
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(range)) {
            if (this.peekChar(0) === "?") {
                this.consumeChar("?");
                range.greedy = false;
            } else {
                range.greedy = true;
            }
            range.type = "Quantifier";
            range.loc = this.loc(begin);
            return range;
        }
    }
    atom() {
        let atom;
        const begin = this.idx;
        switch(this.peekChar()){
            case ".":
                atom = this.dotAll();
                break;
            case "\\":
                atom = this.atomEscape();
                break;
            case "[":
                atom = this.characterClass();
                break;
            case "(":
                atom = this.group();
                break;
        }
        if (atom === undefined && this.isPatternCharacter()) {
            atom = this.patternCharacter();
        }
        // istanbul ignore else
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(atom)) {
            atom.loc = this.loc(begin);
            if (this.isQuantifier()) {
                atom.quantifier = this.quantifier();
            }
            return atom;
        }
        // istanbul ignore next
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_NEVER_REACH_HERE"])();
    }
    dotAll() {
        this.consumeChar(".");
        return {
            type: "Set",
            complement: true,
            value: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\n"),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\r"),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2028"),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u2029")
            ]
        };
    }
    atomEscape() {
        this.consumeChar("\\");
        switch(this.peekChar()){
            case "1":
            case "2":
            case "3":
            case "4":
            case "5":
            case "6":
            case "7":
            case "8":
            case "9":
                return this.decimalEscapeAtom();
            case "d":
            case "D":
            case "s":
            case "S":
            case "w":
            case "W":
                return this.characterClassEscape();
            case "f":
            case "n":
            case "r":
            case "t":
            case "v":
                return this.controlEscapeAtom();
            case "c":
                return this.controlLetterEscapeAtom();
            case "0":
                return this.nulCharacterAtom();
            case "x":
                return this.hexEscapeSequenceAtom();
            case "u":
                return this.regExpUnicodeEscapeSequenceAtom();
            default:
                return this.identityEscapeAtom();
        }
    }
    decimalEscapeAtom() {
        const value = this.positiveInteger();
        return {
            type: "GroupBackReference",
            value: value
        };
    }
    characterClassEscape() {
        let set;
        let complement = false;
        switch(this.popChar()){
            case "d":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["digitsCharCodes"];
                break;
            case "D":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["digitsCharCodes"];
                complement = true;
                break;
            case "s":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["whitespaceCodes"];
                break;
            case "S":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["whitespaceCodes"];
                complement = true;
                break;
            case "w":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wordCharCodes"];
                break;
            case "W":
                set = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$character$2d$classes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wordCharCodes"];
                complement = true;
                break;
        }
        // istanbul ignore else
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(set)) {
            return {
                type: "Set",
                value: set,
                complement: complement
            };
        }
        // istanbul ignore next
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_NEVER_REACH_HERE"])();
    }
    controlEscapeAtom() {
        let escapeCode;
        switch(this.popChar()){
            case "f":
                escapeCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\f");
                break;
            case "n":
                escapeCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\n");
                break;
            case "r":
                escapeCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\r");
                break;
            case "t":
                escapeCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\t");
                break;
            case "v":
                escapeCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\v");
                break;
        }
        // istanbul ignore else
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_EXISTS"])(escapeCode)) {
            return {
                type: "Character",
                value: escapeCode
            };
        }
        // istanbul ignore next
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ASSERT_NEVER_REACH_HERE"])();
    }
    controlLetterEscapeAtom() {
        this.consumeChar("c");
        const letter = this.popChar();
        if (/[a-zA-Z]/.test(letter) === false) {
            throw Error("Invalid ");
        }
        const letterCode = letter.toUpperCase().charCodeAt(0) - 64;
        return {
            type: "Character",
            value: letterCode
        };
    }
    nulCharacterAtom() {
        // TODO implement '[lookahead ∉ DecimalDigit]'
        // TODO: for the deprecated octal escape sequence
        this.consumeChar("0");
        return {
            type: "Character",
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\0")
        };
    }
    hexEscapeSequenceAtom() {
        this.consumeChar("x");
        return this.parseHexDigits(2);
    }
    regExpUnicodeEscapeSequenceAtom() {
        this.consumeChar("u");
        return this.parseHexDigits(4);
    }
    identityEscapeAtom() {
        // TODO: implement "SourceCharacter but not UnicodeIDContinue"
        // // http://unicode.org/reports/tr31/#Specific_Character_Adjustments
        const escapedChar = this.popChar();
        return {
            type: "Character",
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])(escapedChar)
        };
    }
    classPatternCharacterAtom() {
        switch(this.peekChar()){
            // istanbul ignore next
            case "\n":
            // istanbul ignore next
            case "\r":
            // istanbul ignore next
            case "\u2028":
            // istanbul ignore next
            case "\u2029":
            // istanbul ignore next
            case "\\":
            // istanbul ignore next
            case "]":
                throw Error("TBD");
            default:
                const nextChar = this.popChar();
                return {
                    type: "Character",
                    value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])(nextChar)
                };
        }
    }
    characterClass() {
        const set = [];
        let complement = false;
        this.consumeChar("[");
        if (this.peekChar(0) === "^") {
            this.consumeChar("^");
            complement = true;
        }
        while(this.isClassAtom()){
            const from = this.classAtom();
            const isFromSingleChar = from.type === "Character";
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCharacter"])(from) && this.isRangeDash()) {
                this.consumeChar("-");
                const to = this.classAtom();
                const isToSingleChar = to.type === "Character";
                // a range can only be used when both sides are single characters
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCharacter"])(to)) {
                    if (to.value < from.value) {
                        throw Error("Range out of order in character class");
                    }
                    set.push({
                        from: from.value,
                        to: to.value
                    });
                } else {
                    // literal dash
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insertToSet"])(from.value, set);
                    set.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("-"));
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insertToSet"])(to.value, set);
                }
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insertToSet"])(from.value, set);
            }
        }
        this.consumeChar("]");
        return {
            type: "Set",
            complement: complement,
            value: set
        };
    }
    classAtom() {
        switch(this.peekChar()){
            // istanbul ignore next
            case "]":
            // istanbul ignore next
            case "\n":
            // istanbul ignore next
            case "\r":
            // istanbul ignore next
            case "\u2028":
            // istanbul ignore next
            case "\u2029":
                throw Error("TBD");
            case "\\":
                return this.classEscape();
            default:
                return this.classPatternCharacterAtom();
        }
    }
    classEscape() {
        this.consumeChar("\\");
        switch(this.peekChar()){
            // Matches a backspace.
            // (Not to be confused with \b word boundary outside characterClass)
            case "b":
                this.consumeChar("b");
                return {
                    type: "Character",
                    value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])("\u0008")
                };
            case "d":
            case "D":
            case "s":
            case "S":
            case "w":
            case "W":
                return this.characterClassEscape();
            case "f":
            case "n":
            case "r":
            case "t":
            case "v":
                return this.controlEscapeAtom();
            case "c":
                return this.controlLetterEscapeAtom();
            case "0":
                return this.nulCharacterAtom();
            case "x":
                return this.hexEscapeSequenceAtom();
            case "u":
                return this.regExpUnicodeEscapeSequenceAtom();
            default:
                return this.identityEscapeAtom();
        }
    }
    group() {
        let capturing = true;
        this.consumeChar("(");
        switch(this.peekChar(0)){
            case "?":
                this.consumeChar("?");
                this.consumeChar(":");
                capturing = false;
                break;
            default:
                this.groupIdx++;
                break;
        }
        const value = this.disjunction();
        this.consumeChar(")");
        const groupAst = {
            type: "Group",
            capturing: capturing,
            value: value
        };
        if (capturing) {
            groupAst["idx"] = this.groupIdx;
        }
        return groupAst;
    }
    positiveInteger() {
        let number = this.popChar();
        // istanbul ignore next - can't ever get here due to previous lookahead checks
        // still implementing this error checking in case this ever changes.
        if (decimalPatternNoZero.test(number) === false) {
            throw Error("Expecting a positive integer");
        }
        while(decimalPattern.test(this.peekChar(0))){
            number += this.popChar();
        }
        return parseInt(number, 10);
    }
    integerIncludingZero() {
        let number = this.popChar();
        if (decimalPattern.test(number) === false) {
            throw Error("Expecting an integer");
        }
        while(decimalPattern.test(this.peekChar(0))){
            number += this.popChar();
        }
        return parseInt(number, 10);
    }
    patternCharacter() {
        const nextChar = this.popChar();
        switch(nextChar){
            // istanbul ignore next
            case "\n":
            // istanbul ignore next
            case "\r":
            // istanbul ignore next
            case "\u2028":
            // istanbul ignore next
            case "\u2029":
            // istanbul ignore next
            case "^":
            // istanbul ignore next
            case "$":
            // istanbul ignore next
            case "\\":
            // istanbul ignore next
            case ".":
            // istanbul ignore next
            case "*":
            // istanbul ignore next
            case "+":
            // istanbul ignore next
            case "?":
            // istanbul ignore next
            case "(":
            // istanbul ignore next
            case ")":
            // istanbul ignore next
            case "[":
            // istanbul ignore next
            case "|":
                // istanbul ignore next
                throw Error("TBD");
            default:
                return {
                    type: "Character",
                    value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cc"])(nextChar)
                };
        }
    }
    isRegExpFlag() {
        switch(this.peekChar(0)){
            case "g":
            case "i":
            case "m":
            case "u":
            case "y":
                return true;
            default:
                return false;
        }
    }
    isRangeDash() {
        return this.peekChar() === "-" && this.isClassAtom(1);
    }
    isDigit() {
        return decimalPattern.test(this.peekChar(0));
    }
    isClassAtom() {
        let howMuch = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;
        switch(this.peekChar(howMuch)){
            case "]":
            case "\n":
            case "\r":
            case "\u2028":
            case "\u2029":
                return false;
            default:
                return true;
        }
    }
    isTerm() {
        return this.isAtom() || this.isAssertion();
    }
    isAtom() {
        if (this.isPatternCharacter()) {
            return true;
        }
        switch(this.peekChar(0)){
            case ".":
            case "\\":
            case "[":
            // TODO: isAtom must be called before isAssertion - disambiguate
            case "(":
                return true;
            default:
                return false;
        }
    }
    isAssertion() {
        switch(this.peekChar(0)){
            case "^":
            case "$":
                return true;
            // '\b' or '\B'
            case "\\":
                switch(this.peekChar(1)){
                    case "b":
                    case "B":
                        return true;
                    default:
                        return false;
                }
            // '(?=' or '(?!'
            case "(":
                return this.peekChar(1) === "?" && (this.peekChar(2) === "=" || this.peekChar(2) === "!");
            default:
                return false;
        }
    }
    isQuantifier() {
        const prevState = this.saveState();
        try {
            return this.quantifier(true) !== undefined;
        } catch (e) {
            return false;
        } finally{
            this.restoreState(prevState);
        }
    }
    isPatternCharacter() {
        switch(this.peekChar()){
            case "^":
            case "$":
            case "\\":
            case ".":
            case "*":
            case "+":
            case "?":
            case "(":
            case ")":
            case "[":
            case "|":
            case "/":
            case "\n":
            case "\r":
            case "\u2028":
            case "\u2029":
                return false;
            default:
                return true;
        }
    }
    parseHexDigits(howMany) {
        let hexString = "";
        for(let i = 0; i < howMany; i++){
            const hexChar = this.popChar();
            if (hexDigitPattern.test(hexChar) === false) {
                throw Error("Expecting a HexDecimal digits");
            }
            hexString += hexChar;
        }
        const charCode = parseInt(hexString, 16);
        return {
            type: "Character",
            value: charCode
        };
    }
    peekChar() {
        let howMuch = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;
        return this.input[this.idx + howMuch];
    }
    popChar() {
        const nextChar = this.peekChar(0);
        this.consumeChar(undefined);
        return nextChar;
    }
    consumeChar(char) {
        if (char !== undefined && this.input[this.idx] !== char) {
            throw Error("Expected: '" + char + "' but found: '" + this.input[this.idx] + "' at offset: " + this.idx);
        }
        if (this.idx >= this.input.length) {
            throw Error("Unexpected end of input");
        }
        this.idx++;
    }
    loc(begin) {
        return {
            begin: begin,
            end: this.idx
        };
    }
    constructor(){
        this.idx = 0;
        this.input = "";
        this.groupIdx = 0;
    }
} //# sourceMappingURL=regexp-parser.js.map
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BaseRegExpVisitor": ()=>BaseRegExpVisitor
});
class BaseRegExpVisitor {
    visitChildren(node) {
        for(const key in node){
            const child = node[key];
            /* istanbul ignore else */ if (node.hasOwnProperty(key)) {
                if (child.type !== undefined) {
                    this.visit(child);
                } else if (Array.isArray(child)) {
                    child.forEach((subChild)=>{
                        this.visit(subChild);
                    }, this);
                }
            }
        }
    }
    visit(node) {
        switch(node.type){
            case "Pattern":
                this.visitPattern(node);
                break;
            case "Flags":
                this.visitFlags(node);
                break;
            case "Disjunction":
                this.visitDisjunction(node);
                break;
            case "Alternative":
                this.visitAlternative(node);
                break;
            case "StartAnchor":
                this.visitStartAnchor(node);
                break;
            case "EndAnchor":
                this.visitEndAnchor(node);
                break;
            case "WordBoundary":
                this.visitWordBoundary(node);
                break;
            case "NonWordBoundary":
                this.visitNonWordBoundary(node);
                break;
            case "Lookahead":
                this.visitLookahead(node);
                break;
            case "NegativeLookahead":
                this.visitNegativeLookahead(node);
                break;
            case "Character":
                this.visitCharacter(node);
                break;
            case "Set":
                this.visitSet(node);
                break;
            case "Group":
                this.visitGroup(node);
                break;
            case "GroupBackReference":
                this.visitGroupBackReference(node);
                break;
            case "Quantifier":
                this.visitQuantifier(node);
                break;
        }
        this.visitChildren(node);
    }
    visitPattern(node) {}
    visitFlags(node) {}
    visitDisjunction(node) {}
    visitAlternative(node) {}
    // Assertion
    visitStartAnchor(node) {}
    visitEndAnchor(node) {}
    visitWordBoundary(node) {}
    visitNonWordBoundary(node) {}
    visitLookahead(node) {}
    visitNegativeLookahead(node) {}
    // atoms
    visitCharacter(node) {}
    visitSet(node) {}
    visitGroup(node) {}
    visitGroupBackReference(node) {}
    visitQuantifier(node) {}
} //# sourceMappingURL=base-regexp-visitor.js.map
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/api.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$regexp$2d$parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$base$2d$regexp$2d$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js [app-client] (ecmascript)"); //# sourceMappingURL=api.js.map
;
;
}),
"[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/api.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$regexp$2d$parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$base$2d$regexp$2d$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$regexp$2d$to$2d$ast$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/regexp-to-ast/lib/src/api.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@chevrotain/utils/lib/src/print.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PRINT_ERROR": ()=>PRINT_ERROR,
    "PRINT_WARNING": ()=>PRINT_WARNING
});
function PRINT_ERROR(msg) {
    /* istanbul ignore else - can't override global.console in node.js */ if (console && console.error) {
        console.error("Error: ".concat(msg));
    }
}
function PRINT_WARNING(msg) {
    /* istanbul ignore else - can't override global.console in node.js*/ if (console && console.warn) {
        // TODO: modify docs accordingly
        console.warn("Warning: ".concat(msg));
    }
} //# sourceMappingURL=print.js.map
}),
"[project]/node_modules/@chevrotain/utils/lib/src/timer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timer": ()=>timer
});
function timer(func) {
    const start = new Date().getTime();
    const val = func();
    const end = new Date().getTime();
    const total = end - start;
    return {
        time: total,
        value: val
    };
} //# sourceMappingURL=timer.js.map
}),
"[project]/node_modules/@chevrotain/utils/lib/src/to-fast-properties.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// based on: https://github.com/petkaantonov/bluebird/blob/b97c0d2d487e8c5076e8bd897e0dcd4622d31846/src/util.js#L201-L216
__turbopack_context__.s({
    "toFastProperties": ()=>toFastProperties
});
function toFastProperties(toBecomeFast) {
    function FakeConstructor() {}
    // If our object is used as a constructor, it would receive
    FakeConstructor.prototype = toBecomeFast;
    const fakeInstance = new FakeConstructor();
    function fakeAccess() {
        return typeof fakeInstance.bar;
    }
    // help V8 understand this is a "real" prototype by actually using
    // the fake instance.
    fakeAccess();
    fakeAccess();
    // Always true condition to suppress the Firefox warning of unreachable
    // code after a return statement.
    if ("TURBOPACK compile-time truthy", 1) return toBecomeFast;
    //TURBOPACK unreachable
    ;
} //# sourceMappingURL=to-fast-properties.js.map
}),
"[project]/node_modules/@chevrotain/utils/lib/src/api.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$print$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/print.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$timer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/timer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$to$2d$fast$2d$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/to-fast-properties.js [app-client] (ecmascript)"); //# sourceMappingURL=api.js.map
;
;
;
}),
"[project]/node_modules/@chevrotain/utils/lib/src/api.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$print$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/print.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$timer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/timer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$to$2d$fast$2d$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/to-fast-properties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$utils$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/utils/lib/src/api.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AbstractProduction": ()=>AbstractProduction,
    "Alternation": ()=>Alternation,
    "Alternative": ()=>Alternative,
    "NonTerminal": ()=>NonTerminal,
    "Option": ()=>Option,
    "Repetition": ()=>Repetition,
    "RepetitionMandatory": ()=>RepetitionMandatory,
    "RepetitionMandatoryWithSeparator": ()=>RepetitionMandatoryWithSeparator,
    "RepetitionWithSeparator": ()=>RepetitionWithSeparator,
    "Rule": ()=>Rule,
    "Terminal": ()=>Terminal,
    "serializeGrammar": ()=>serializeGrammar,
    "serializeProduction": ()=>serializeProduction
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/assign.js [app-client] (ecmascript) <export default as assign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__forEach$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/forEach.js [app-client] (ecmascript) <export default as forEach>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isRegExp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isRegExp$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isRegExp.js [app-client] (ecmascript) <export default as isRegExp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isString$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isString$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isString.js [app-client] (ecmascript) <export default as isString>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript) <export default as map>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/pickBy.js [app-client] (ecmascript) <export default as pickBy>");
;
// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?
function tokenLabel(tokType) {
    if (hasTokenLabel(tokType)) {
        return tokType.LABEL;
    } else {
        return tokType.name;
    }
}
// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?
function hasTokenLabel(obj) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isString$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isString$3e$__["isString"])(obj.LABEL) && obj.LABEL !== "";
}
class AbstractProduction {
    get definition() {
        return this._definition;
    }
    set definition(value) {
        this._definition = value;
    }
    accept(visitor) {
        visitor.visit(this);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__forEach$3e$__["forEach"])(this.definition, (prod)=>{
            prod.accept(visitor);
        });
    }
    constructor(_definition){
        this._definition = _definition;
    }
}
class NonTerminal extends AbstractProduction {
    set definition(definition) {
    // immutable
    }
    get definition() {
        if (this.referencedRule !== undefined) {
            return this.referencedRule.definition;
        }
        return [];
    }
    accept(visitor) {
        visitor.visit(this);
    // don't visit children of a reference, we will get cyclic infinite loops if we do so
    }
    constructor(options){
        super([]);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Rule extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.orgText = "";
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Alternative extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.ignoreAmbiguities = false;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Option extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class RepetitionMandatory extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class RepetitionMandatoryWithSeparator extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Repetition extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class RepetitionWithSeparator extends AbstractProduction {
    constructor(options){
        super(options.definition);
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Alternation extends AbstractProduction {
    get definition() {
        return this._definition;
    }
    set definition(value) {
        this._definition = value;
    }
    constructor(options){
        super(options.definition);
        this.idx = 1;
        this.ignoreAmbiguities = false;
        this.hasPredicates = false;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
class Terminal {
    accept(visitor) {
        visitor.visit(this);
    }
    constructor(options){
        this.idx = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$pickBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pickBy$3e$__["pickBy"])(options, (v)=>v !== undefined));
    }
}
function serializeGrammar(topRules) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(topRules, serializeProduction);
}
function serializeProduction(node) {
    function convertDefinition(definition) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(definition, serializeProduction);
    }
    /* istanbul ignore else */ if (node instanceof NonTerminal) {
        const serializedNonTerminal = {
            type: "NonTerminal",
            name: node.nonTerminalName,
            idx: node.idx
        };
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isString$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isString$3e$__["isString"])(node.label)) {
            serializedNonTerminal.label = node.label;
        }
        return serializedNonTerminal;
    } else if (node instanceof Alternative) {
        return {
            type: "Alternative",
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof Option) {
        return {
            type: "Option",
            idx: node.idx,
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof RepetitionMandatory) {
        return {
            type: "RepetitionMandatory",
            idx: node.idx,
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof RepetitionMandatoryWithSeparator) {
        return {
            type: "RepetitionMandatoryWithSeparator",
            idx: node.idx,
            separator: serializeProduction(new Terminal({
                terminalType: node.separator
            })),
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof RepetitionWithSeparator) {
        return {
            type: "RepetitionWithSeparator",
            idx: node.idx,
            separator: serializeProduction(new Terminal({
                terminalType: node.separator
            })),
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof Repetition) {
        return {
            type: "Repetition",
            idx: node.idx,
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof Alternation) {
        return {
            type: "Alternation",
            idx: node.idx,
            definition: convertDefinition(node.definition)
        };
    } else if (node instanceof Terminal) {
        const serializedTerminal = {
            type: "Terminal",
            name: node.terminalType.name,
            label: tokenLabel(node.terminalType),
            idx: node.idx
        };
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isString$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isString$3e$__["isString"])(node.label)) {
            serializedTerminal.terminalLabel = node.label;
        }
        const pattern = node.terminalType.PATTERN;
        if (node.terminalType.PATTERN) {
            serializedTerminal.pattern = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isRegExp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isRegExp$3e$__["isRegExp"])(pattern) ? pattern.source : pattern;
        }
        return serializedTerminal;
    } else if (node instanceof Rule) {
        return {
            type: "Rule",
            name: node.name,
            orgText: node.orgText,
            definition: convertDefinition(node.definition)
        };
    /* c8 ignore next 3 */ } else {
        throw Error("non exhaustive match");
    }
} //# sourceMappingURL=model.js.map
}),
"[project]/node_modules/@chevrotain/gast/lib/src/visitor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GAstVisitor": ()=>GAstVisitor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
;
class GAstVisitor {
    visit(node) {
        const nodeAny = node;
        switch(nodeAny.constructor){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]:
                return this.visitNonTerminal(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternative"]:
                return this.visitAlternative(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"]:
                return this.visitOption(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"]:
                return this.visitRepetitionMandatory(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"]:
                return this.visitRepetitionMandatoryWithSeparator(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"]:
                return this.visitRepetitionWithSeparator(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"]:
                return this.visitRepetition(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]:
                return this.visitAlternation(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Terminal"]:
                return this.visitTerminal(nodeAny);
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rule"]:
                return this.visitRule(nodeAny);
            /* c8 ignore next 2 */ default:
                throw Error("non exhaustive match");
        }
    }
    /* c8 ignore next */ visitNonTerminal(node) {}
    /* c8 ignore next */ visitAlternative(node) {}
    /* c8 ignore next */ visitOption(node) {}
    /* c8 ignore next */ visitRepetition(node) {}
    /* c8 ignore next */ visitRepetitionMandatory(node) {}
    /* c8 ignore next 3 */ visitRepetitionMandatoryWithSeparator(node) {}
    /* c8 ignore next */ visitRepetitionWithSeparator(node) {}
    /* c8 ignore next */ visitAlternation(node) {}
    /* c8 ignore next */ visitTerminal(node) {}
    /* c8 ignore next */ visitRule(node) {}
} //# sourceMappingURL=visitor.js.map
}),
"[project]/node_modules/@chevrotain/gast/lib/src/helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getProductionDslName": ()=>getProductionDslName,
    "isBranchingProd": ()=>isBranchingProd,
    "isOptionalProd": ()=>isOptionalProd,
    "isSequenceProd": ()=>isSequenceProd
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$every$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__every$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/every.js [app-client] (ecmascript) <export default as every>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$includes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__includes$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/includes.js [app-client] (ecmascript) <export default as includes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$some$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__some$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/some.js [app-client] (ecmascript) <export default as some>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
;
;
function isSequenceProd(prod) {
    return prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternative"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Terminal"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rule"];
}
function isOptionalProd(prod) {
    let alreadyVisited = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
    const isDirectlyOptional = prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"] || prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"];
    if (isDirectlyOptional) {
        return true;
    }
    // note that this can cause infinite loop if one optional empty TOP production has a cyclic dependency with another
    // empty optional top rule
    // may be indirectly optional ((A?B?C?) | (D?E?F?))
    if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]) {
        // for OR its enough for just one of the alternatives to be optional
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$some$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__some$3e$__["some"])(prod.definition, (subProd)=>{
            return isOptionalProd(subProd, alreadyVisited);
        });
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$includes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__includes$3e$__["includes"])(alreadyVisited, prod)) {
        // avoiding stack overflow due to infinite recursion
        return false;
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AbstractProduction"]) {
        if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]) {
            alreadyVisited.push(prod);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$every$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__every$3e$__["every"])(prod.definition, (subProd)=>{
            return isOptionalProd(subProd, alreadyVisited);
        });
    } else {
        return false;
    }
}
function isBranchingProd(prod) {
    return prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"];
}
function getProductionDslName(prod) {
    /* istanbul ignore else */ if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]) {
        return "SUBRULE";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"]) {
        return "OPTION";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]) {
        return "OR";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"]) {
        return "AT_LEAST_ONE";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"]) {
        return "AT_LEAST_ONE_SEP";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"]) {
        return "MANY_SEP";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"]) {
        return "MANY";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Terminal"]) {
        return "CONSUME";
    /* c8 ignore next 3 */ } else {
        throw Error("non exhaustive match");
    }
} //# sourceMappingURL=helpers.js.map
}),
"[project]/node_modules/@chevrotain/gast/lib/src/api.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/visitor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/helpers.js [app-client] (ecmascript)"); //# sourceMappingURL=api.js.map
;
;
;
}),
"[project]/node_modules/@chevrotain/gast/lib/src/api.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/visitor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/api.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@chevrotain/cst-dts-gen/lib/src/model.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "buildModel": ()=>buildModel
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/api.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/visitor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/assign.js [app-client] (ecmascript) <export default as assign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__flatten$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/flatten.js [app-client] (ecmascript) <export default as flatten>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$groupBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__groupBy$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/groupBy.js [app-client] (ecmascript) <export default as groupBy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript) <export default as map>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$some$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__some$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/some.js [app-client] (ecmascript) <export default as some>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$values$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__values$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/values.js [app-client] (ecmascript) <export default as values>");
;
;
function buildModel(productions) {
    const generator = new CstNodeDefinitionGenerator();
    const allRules = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$values$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__values$3e$__["values"])(productions);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(allRules, (rule)=>generator.visitRule(rule));
}
class CstNodeDefinitionGenerator extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$visitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GAstVisitor"] {
    visitRule(node) {
        const rawElements = this.visitEach(node.definition);
        const grouped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$groupBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__groupBy$3e$__["groupBy"])(rawElements, (el)=>el.propertyName);
        const properties = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(grouped, (group, propertyName)=>{
            const allNullable = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$some$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__some$3e$__["some"])(group, (el)=>!el.canBeNull);
            // In an alternation with a label a property name can have
            // multiple types.
            let propertyType = group[0].type;
            if (group.length > 1) {
                propertyType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(group, (g)=>g.type);
            }
            return {
                name: propertyName,
                type: propertyType,
                optional: allNullable
            };
        });
        return {
            name: node.name,
            properties: properties
        };
    }
    visitAlternative(node) {
        return this.visitEachAndOverrideWith(node.definition, {
            canBeNull: true
        });
    }
    visitOption(node) {
        return this.visitEachAndOverrideWith(node.definition, {
            canBeNull: true
        });
    }
    visitRepetition(node) {
        return this.visitEachAndOverrideWith(node.definition, {
            canBeNull: true
        });
    }
    visitRepetitionMandatory(node) {
        return this.visitEach(node.definition);
    }
    visitRepetitionMandatoryWithSeparator(node) {
        return this.visitEach(node.definition).concat({
            propertyName: node.separator.name,
            canBeNull: true,
            type: getType(node.separator)
        });
    }
    visitRepetitionWithSeparator(node) {
        return this.visitEachAndOverrideWith(node.definition, {
            canBeNull: true
        }).concat({
            propertyName: node.separator.name,
            canBeNull: true,
            type: getType(node.separator)
        });
    }
    visitAlternation(node) {
        return this.visitEachAndOverrideWith(node.definition, {
            canBeNull: true
        });
    }
    visitTerminal(node) {
        return [
            {
                propertyName: node.label || node.terminalType.name,
                canBeNull: false,
                type: getType(node)
            }
        ];
    }
    visitNonTerminal(node) {
        return [
            {
                propertyName: node.label || node.nonTerminalName,
                canBeNull: false,
                type: getType(node)
            }
        ];
    }
    visitEachAndOverrideWith(definition, override) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(this.visitEach(definition), (definition)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__assign$3e$__["assign"])({}, definition, override));
    }
    visitEach(definition) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__flatten$3e$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(definition, (definition)=>this.visit(definition)));
    }
}
function getType(production) {
    if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]) {
        return {
            kind: "rule",
            name: production.referencedRule.name
        };
    }
    return {
        kind: "token"
    };
} //# sourceMappingURL=model.js.map
}),
"[project]/node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "genDts": ()=>genDts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__flatten$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/flatten.js [app-client] (ecmascript) <export default as flatten>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isArray$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isArray.js [app-client] (ecmascript) <export default as isArray>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript) <export default as map>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$reduce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__reduce$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/reduce.js [app-client] (ecmascript) <export default as reduce>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$uniq$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__uniq$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/uniq.js [app-client] (ecmascript) <export default as uniq>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$upperFirst$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__upperFirst$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/upperFirst.js [app-client] (ecmascript) <export default as upperFirst>");
;
function genDts(model, options) {
    let contentParts = [];
    contentParts = contentParts.concat('import type { CstNode, ICstVisitor, IToken } from "chevrotain";');
    contentParts = contentParts.concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__flatten$3e$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(model, (node)=>genCstNodeTypes(node))));
    if (options.includeVisitorInterface) {
        contentParts = contentParts.concat(genVisitor(options.visitorInterfaceName, model));
    }
    return contentParts.join("\n\n") + "\n";
}
function genCstNodeTypes(node) {
    const nodeCstInterface = genNodeInterface(node);
    const nodeChildrenInterface = genNodeChildrenType(node);
    return [
        nodeCstInterface,
        nodeChildrenInterface
    ];
}
function genNodeInterface(node) {
    const nodeInterfaceName = getNodeInterfaceName(node.name);
    const childrenTypeName = getNodeChildrenTypeName(node.name);
    return "export interface ".concat(nodeInterfaceName, ' extends CstNode {\n  name: "').concat(node.name, '";\n  children: ').concat(childrenTypeName, ";\n}");
}
function genNodeChildrenType(node) {
    const typeName = getNodeChildrenTypeName(node.name);
    return "export type ".concat(typeName, " = {\n  ").concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(node.properties, (property)=>genChildProperty(property)).join("\n  "), "\n};");
}
function genChildProperty(prop) {
    const typeName = buildTypeString(prop.type);
    return "".concat(prop.name).concat(prop.optional ? "?" : "", ": ").concat(typeName, "[];");
}
function genVisitor(name, nodes) {
    return "export interface ".concat(name, "<IN, OUT> extends ICstVisitor<IN, OUT> {\n  ").concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(nodes, (node)=>genVisitorFunction(node)).join("\n  "), "\n}");
}
function genVisitorFunction(node) {
    const childrenTypeName = getNodeChildrenTypeName(node.name);
    return "".concat(node.name, "(children: ").concat(childrenTypeName, ", param?: IN): OUT;");
}
function buildTypeString(type) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isArray$3e$__["isArray"])(type)) {
        const typeNames = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$uniq$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__uniq$3e$__["uniq"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__map$3e$__["map"])(type, (t)=>getTypeString(t)));
        const typeString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$reduce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__reduce$3e$__["reduce"])(typeNames, (sum, t)=>sum + " | " + t);
        return "(" + typeString + ")";
    } else {
        return getTypeString(type);
    }
}
function getTypeString(type) {
    if (type.kind === "token") {
        return "IToken";
    }
    return getNodeInterfaceName(type.name);
}
function getNodeInterfaceName(ruleName) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$upperFirst$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__upperFirst$3e$__["upperFirst"])(ruleName) + "CstNode";
}
function getNodeChildrenTypeName(ruleName) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$upperFirst$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__upperFirst$3e$__["upperFirst"])(ruleName) + "CstChildren";
} //# sourceMappingURL=generate.js.map
}),
"[project]/node_modules/@chevrotain/cst-dts-gen/lib/src/api.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "generateCstDts": ()=>generateCstDts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$cst$2d$dts$2d$gen$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/cst-dts-gen/lib/src/model.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$cst$2d$dts$2d$gen$2f$lib$2f$src$2f$generate$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js [app-client] (ecmascript)");
;
;
const defaultOptions = {
    includeVisitorInterface: true,
    visitorInterfaceName: "ICstNodeVisitor"
};
function generateCstDts(productions, options) {
    const effectiveOptions = Object.assign(Object.assign({}, defaultOptions), options);
    const model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$cst$2d$dts$2d$gen$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildModel"])(productions);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$cst$2d$dts$2d$gen$2f$lib$2f$src$2f$generate$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genDts"])(model, effectiveOptions);
} //# sourceMappingURL=api.js.map
}),
"[project]/node_modules/chevrotain-allstar/lib/atn.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/ __turbopack_context__.s({
    "ATN_BASIC": ()=>ATN_BASIC,
    "ATN_BLOCK_END": ()=>ATN_BLOCK_END,
    "ATN_INVALID_TYPE": ()=>ATN_INVALID_TYPE,
    "ATN_LOOP_END": ()=>ATN_LOOP_END,
    "ATN_PLUS_BLOCK_START": ()=>ATN_PLUS_BLOCK_START,
    "ATN_PLUS_LOOP_BACK": ()=>ATN_PLUS_LOOP_BACK,
    "ATN_RULE_START": ()=>ATN_RULE_START,
    "ATN_RULE_STOP": ()=>ATN_RULE_STOP,
    "ATN_STAR_BLOCK_START": ()=>ATN_STAR_BLOCK_START,
    "ATN_STAR_LOOP_BACK": ()=>ATN_STAR_LOOP_BACK,
    "ATN_STAR_LOOP_ENTRY": ()=>ATN_STAR_LOOP_ENTRY,
    "ATN_TOKEN_START": ()=>ATN_TOKEN_START,
    "AbstractTransition": ()=>AbstractTransition,
    "AtomTransition": ()=>AtomTransition,
    "EpsilonTransition": ()=>EpsilonTransition,
    "RuleTransition": ()=>RuleTransition,
    "buildATNKey": ()=>buildATNKey,
    "createATN": ()=>createATN
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$filter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/filter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/chevrotain/lib/src/api.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
;
;
;
function buildATNKey(rule, type, occurrence) {
    return "".concat(rule.name, "_").concat(type, "_").concat(occurrence);
}
const ATN_INVALID_TYPE = 0;
const ATN_BASIC = 1;
const ATN_RULE_START = 2;
const ATN_PLUS_BLOCK_START = 4;
const ATN_STAR_BLOCK_START = 5;
const ATN_TOKEN_START = 6;
const ATN_RULE_STOP = 7;
const ATN_BLOCK_END = 8;
const ATN_STAR_LOOP_BACK = 9;
const ATN_STAR_LOOP_ENTRY = 10;
const ATN_PLUS_LOOP_BACK = 11;
const ATN_LOOP_END = 12;
class AbstractTransition {
    isEpsilon() {
        return false;
    }
    constructor(target){
        this.target = target;
    }
}
class AtomTransition extends AbstractTransition {
    constructor(target, tokenType){
        super(target);
        this.tokenType = tokenType;
    }
}
class EpsilonTransition extends AbstractTransition {
    isEpsilon() {
        return true;
    }
    constructor(target){
        super(target);
    }
}
class RuleTransition extends AbstractTransition {
    isEpsilon() {
        return true;
    }
    constructor(ruleStart, rule, followState){
        super(ruleStart);
        this.rule = rule;
        this.followState = followState;
    }
}
function createATN(rules) {
    const atn = {
        decisionMap: {},
        decisionStates: [],
        ruleToStartState: new Map(),
        ruleToStopState: new Map(),
        states: []
    };
    createRuleStartAndStopATNStates(atn, rules);
    const ruleLength = rules.length;
    for(let i = 0; i < ruleLength; i++){
        const rule = rules[i];
        const ruleBlock = block(atn, rule, rule);
        if (ruleBlock === undefined) {
            continue;
        }
        buildRuleHandle(atn, rule, ruleBlock);
    }
    return atn;
}
function createRuleStartAndStopATNStates(atn, rules) {
    const ruleLength = rules.length;
    for(let i = 0; i < ruleLength; i++){
        const rule = rules[i];
        const start = newState(atn, rule, undefined, {
            type: ATN_RULE_START
        });
        const stop = newState(atn, rule, undefined, {
            type: ATN_RULE_STOP
        });
        start.stop = stop;
        atn.ruleToStartState.set(rule, start);
        atn.ruleToStopState.set(rule, stop);
    }
}
function atom(atn, rule, production) {
    if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Terminal"]) {
        return tokenRef(atn, rule, production.terminalType, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]) {
        return ruleRef(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]) {
        return alternation(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"]) {
        return option(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"]) {
        return repetition(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"]) {
        return repetitionSep(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"]) {
        return repetitionMandatory(atn, rule, production);
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"]) {
        return repetitionMandatorySep(atn, rule, production);
    } else {
        return block(atn, rule, production);
    }
}
function repetition(atn, rule, repetition) {
    const starState = newState(atn, rule, repetition, {
        type: ATN_STAR_BLOCK_START
    });
    defineDecisionState(atn, starState);
    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));
    return star(atn, rule, repetition, handle);
}
function repetitionSep(atn, rule, repetition) {
    const starState = newState(atn, rule, repetition, {
        type: ATN_STAR_BLOCK_START
    });
    defineDecisionState(atn, starState);
    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));
    const sep = tokenRef(atn, rule, repetition.separator, repetition);
    return star(atn, rule, repetition, handle, sep);
}
function repetitionMandatory(atn, rule, repetition) {
    const plusState = newState(atn, rule, repetition, {
        type: ATN_PLUS_BLOCK_START
    });
    defineDecisionState(atn, plusState);
    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));
    return plus(atn, rule, repetition, handle);
}
function repetitionMandatorySep(atn, rule, repetition) {
    const plusState = newState(atn, rule, repetition, {
        type: ATN_PLUS_BLOCK_START
    });
    defineDecisionState(atn, plusState);
    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));
    const sep = tokenRef(atn, rule, repetition.separator, repetition);
    return plus(atn, rule, repetition, handle, sep);
}
function alternation(atn, rule, alternation) {
    const start = newState(atn, rule, alternation, {
        type: ATN_BASIC
    });
    defineDecisionState(atn, start);
    const alts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(alternation.definition, (e)=>atom(atn, rule, e));
    const handle = makeAlts(atn, rule, start, alternation, ...alts);
    return handle;
}
function option(atn, rule, option) {
    const start = newState(atn, rule, option, {
        type: ATN_BASIC
    });
    defineDecisionState(atn, start);
    const handle = makeAlts(atn, rule, start, option, block(atn, rule, option));
    return optional(atn, rule, option, handle);
}
function block(atn, rule, block) {
    const handles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$filter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(block.definition, (e)=>atom(atn, rule, e)), (e)=>e !== undefined);
    if (handles.length === 1) {
        return handles[0];
    } else if (handles.length === 0) {
        return undefined;
    } else {
        return makeBlock(atn, handles);
    }
}
function plus(atn, rule, plus, handle, sep) {
    const blkStart = handle.left;
    const blkEnd = handle.right;
    const loop = newState(atn, rule, plus, {
        type: ATN_PLUS_LOOP_BACK
    });
    defineDecisionState(atn, loop);
    const end = newState(atn, rule, plus, {
        type: ATN_LOOP_END
    });
    blkStart.loopback = loop;
    end.loopback = loop;
    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionMandatoryWithSeparator' : 'RepetitionMandatory', plus.idx)] = loop;
    epsilon(blkEnd, loop); // block can see loop back
    // Depending on whether we have a separator we put the exit transition at index 1 or 0
    // This influences the chosen option in the lookahead DFA
    if (sep === undefined) {
        epsilon(loop, blkStart); // loop back to start
        epsilon(loop, end); // exit
    } else {
        epsilon(loop, end); // exit
        // loop back to start with separator
        epsilon(loop, sep.left);
        epsilon(sep.right, blkStart);
    }
    return {
        left: blkStart,
        right: end
    };
}
function star(atn, rule, star, handle, sep) {
    const start = handle.left;
    const end = handle.right;
    const entry = newState(atn, rule, star, {
        type: ATN_STAR_LOOP_ENTRY
    });
    defineDecisionState(atn, entry);
    const loopEnd = newState(atn, rule, star, {
        type: ATN_LOOP_END
    });
    const loop = newState(atn, rule, star, {
        type: ATN_STAR_LOOP_BACK
    });
    entry.loopback = loop;
    loopEnd.loopback = loop;
    epsilon(entry, start); // loop enter edge (alt 2)
    epsilon(entry, loopEnd); // bypass loop edge (alt 1)
    epsilon(end, loop); // block end hits loop back
    if (sep !== undefined) {
        epsilon(loop, loopEnd); // end loop
        // loop back to start of handle using separator
        epsilon(loop, sep.left);
        epsilon(sep.right, start);
    } else {
        epsilon(loop, entry); // loop back to entry/exit decision
    }
    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionWithSeparator' : 'Repetition', star.idx)] = entry;
    return {
        left: entry,
        right: loopEnd
    };
}
function optional(atn, rule, optional, handle) {
    const start = handle.left;
    const end = handle.right;
    epsilon(start, end);
    atn.decisionMap[buildATNKey(rule, 'Option', optional.idx)] = start;
    return handle;
}
function defineDecisionState(atn, state) {
    atn.decisionStates.push(state);
    state.decision = atn.decisionStates.length - 1;
    return state.decision;
}
function makeAlts(atn, rule, start, production) {
    for(var _len = arguments.length, alts = new Array(_len > 4 ? _len - 4 : 0), _key = 4; _key < _len; _key++){
        alts[_key - 4] = arguments[_key];
    }
    const end = newState(atn, rule, production, {
        type: ATN_BLOCK_END,
        start
    });
    start.end = end;
    for (const alt of alts){
        if (alt !== undefined) {
            // hook alts up to decision block
            epsilon(start, alt.left);
            epsilon(alt.right, end);
        } else {
            epsilon(start, end);
        }
    }
    const handle = {
        left: start,
        right: end
    };
    atn.decisionMap[buildATNKey(rule, getProdType(production), production.idx)] = start;
    return handle;
}
function getProdType(production) {
    if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]) {
        return 'Alternation';
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"]) {
        return 'Option';
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"]) {
        return 'Repetition';
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"]) {
        return 'RepetitionWithSeparator';
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"]) {
        return 'RepetitionMandatory';
    } else if (production instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"]) {
        return 'RepetitionMandatoryWithSeparator';
    } else {
        throw new Error('Invalid production type encountered');
    }
}
function makeBlock(atn, alts) {
    const altsLength = alts.length;
    for(let i = 0; i < altsLength - 1; i++){
        const handle = alts[i];
        let transition;
        if (handle.left.transitions.length === 1) {
            transition = handle.left.transitions[0];
        }
        const isRuleTransition = transition instanceof RuleTransition;
        const ruleTransition = transition;
        const next = alts[i + 1].left;
        if (handle.left.type === ATN_BASIC && handle.right.type === ATN_BASIC && transition !== undefined && (isRuleTransition && ruleTransition.followState === handle.right || transition.target === handle.right)) {
            // we can avoid epsilon edge to next element
            if (isRuleTransition) {
                ruleTransition.followState = next;
            } else {
                transition.target = next;
            }
            removeState(atn, handle.right); // we skipped over this state
        } else {
            // need epsilon if previous block's right end node is complex
            epsilon(handle.right, next);
        }
    }
    const first = alts[0];
    const last = alts[altsLength - 1];
    return {
        left: first.left,
        right: last.right
    };
}
function tokenRef(atn, rule, tokenType, production) {
    const left = newState(atn, rule, production, {
        type: ATN_BASIC
    });
    const right = newState(atn, rule, production, {
        type: ATN_BASIC
    });
    addTransition(left, new AtomTransition(right, tokenType));
    return {
        left,
        right
    };
}
function ruleRef(atn, currentRule, nonTerminal) {
    const rule = nonTerminal.referencedRule;
    const start = atn.ruleToStartState.get(rule);
    const left = newState(atn, currentRule, nonTerminal, {
        type: ATN_BASIC
    });
    const right = newState(atn, currentRule, nonTerminal, {
        type: ATN_BASIC
    });
    const call = new RuleTransition(start, rule, right);
    addTransition(left, call);
    return {
        left,
        right
    };
}
function buildRuleHandle(atn, rule, block) {
    const start = atn.ruleToStartState.get(rule);
    epsilon(start, block.left);
    const stop = atn.ruleToStopState.get(rule);
    epsilon(block.right, stop);
    const handle = {
        left: start,
        right: stop
    };
    return handle;
}
function epsilon(a, b) {
    const transition = new EpsilonTransition(b);
    addTransition(a, transition);
}
function newState(atn, rule, production, partial) {
    const t = Object.assign({
        atn,
        production,
        epsilonOnlyTransitions: false,
        rule,
        transitions: [],
        nextTokenWithinRule: [],
        stateNumber: atn.states.length
    }, partial);
    atn.states.push(t);
    return t;
}
function addTransition(state, transition) {
    // A single ATN state can only contain epsilon transitions or non-epsilon transitions
    // Because they are never mixed, only setting the property for the first transition is fine
    if (state.transitions.length === 0) {
        state.epsilonOnlyTransitions = transition.isEpsilon();
    }
    state.transitions.push(transition);
}
function removeState(atn, state) {
    atn.states.splice(atn.states.indexOf(state), 1);
} //# sourceMappingURL=atn.js.map
}),
"[project]/node_modules/chevrotain-allstar/lib/dfa.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/ __turbopack_context__.s({
    "ATNConfigSet": ()=>ATNConfigSet,
    "DFA_ERROR": ()=>DFA_ERROR,
    "getATNConfigKey": ()=>getATNConfigKey
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript)");
;
const DFA_ERROR = {};
class ATNConfigSet {
    get size() {
        return this.configs.length;
    }
    finalize() {
        // Empties the map to free up memory
        this.map = {};
    }
    add(config) {
        const key = getATNConfigKey(config);
        // Only add configs which don't exist in our map already
        // While this does not influence the actual algorithm, adding them anyway would massively increase memory consumption
        if (!(key in this.map)) {
            this.map[key] = this.configs.length;
            this.configs.push(config);
        }
    }
    get elements() {
        return this.configs;
    }
    get alts() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.configs, (e)=>e.alt);
    }
    get key() {
        let value = "";
        for(const k in this.map){
            value += k + ":";
        }
        return value;
    }
    constructor(){
        this.map = {};
        this.configs = [];
    }
}
function getATNConfigKey(config) {
    let alt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
    return "".concat(alt ? "a".concat(config.alt) : "", "s").concat(config.state.stateNumber, ":").concat(config.stack.map((e)=>e.stateNumber.toString()).join("_"));
} //# sourceMappingURL=dfa.js.map
}),
"[project]/node_modules/chevrotain-allstar/lib/all-star-lookahead.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/ __turbopack_context__.s({
    "LLStarLookaheadStrategy": ()=>LLStarLookaheadStrategy
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/chevrotain/lib/src/api.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$scan$2f$tokens_public$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain/lib/src/scan/tokens_public.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@chevrotain/gast/lib/src/model.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$parse$2f$grammar$2f$llk_lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain/lib/src/parse/grammar/llk_lookahead.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$parse$2f$grammar$2f$lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain/lib/src/parse/grammar/lookahead.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain-allstar/lib/atn.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain-allstar/lib/dfa.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/flatMap.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$uniqBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/uniqBy.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/map.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/flatten.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/forEach.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEmpty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isEmpty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$reduce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/reduce.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
function createDFACache(startState, decision) {
    const map = {};
    return (predicateSet)=>{
        const key = predicateSet.toString();
        let existing = map[key];
        if (existing !== undefined) {
            return existing;
        } else {
            existing = {
                atnStartState: startState,
                decision,
                states: {}
            };
            map[key] = existing;
            return existing;
        }
    };
}
class PredicateSet {
    is(index) {
        return index >= this.predicates.length || this.predicates[index];
    }
    set(index, value) {
        this.predicates[index] = value;
    }
    toString() {
        let value = "";
        const size = this.predicates.length;
        for(let i = 0; i < size; i++){
            value += this.predicates[i] === true ? "1" : "0";
        }
        return value;
    }
    constructor(){
        this.predicates = [];
    }
}
const EMPTY_PREDICATES = new PredicateSet();
class LLStarLookaheadStrategy extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$parse$2f$grammar$2f$llk_lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LLkLookaheadStrategy"] {
    initialize(options) {
        this.atn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createATN"])(options.rules);
        this.dfas = initATNSimulator(this.atn);
    }
    validateAmbiguousAlternationAlternatives() {
        return [];
    }
    validateEmptyOrAlternatives() {
        return [];
    }
    buildLookaheadForAlternation(options) {
        const { prodOccurrence, rule, hasPredicates, dynamicTokensEnabled } = options;
        const dfas = this.dfas;
        const logging = this.logging;
        const key = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildATNKey"])(rule, 'Alternation', prodOccurrence);
        const decisionState = this.atn.decisionMap[key];
        const decisionIndex = decisionState.decision;
        const partialAlts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$parse$2f$grammar$2f$lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLookaheadPaths"])({
            maxLookahead: 1,
            occurrence: prodOccurrence,
            prodType: "Alternation",
            rule: rule
        }), (currAlt)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currAlt, (path)=>path[0]));
        if (isLL1Sequence(partialAlts, false) && !dynamicTokensEnabled) {
            const choiceToAlt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$reduce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(partialAlts, (result, currAlt, idx)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currAlt, (currTokType)=>{
                    if (currTokType) {
                        result[currTokType.tokenTypeIdx] = idx;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currTokType.categoryMatches, (currExtendingType)=>{
                            result[currExtendingType] = idx;
                        });
                    }
                });
                return result;
            }, {});
            if (hasPredicates) {
                return function(orAlts) {
                    var _a;
                    const nextToken = this.LA(1);
                    const prediction = choiceToAlt[nextToken.tokenTypeIdx];
                    if (orAlts !== undefined && prediction !== undefined) {
                        const gate = (_a = orAlts[prediction]) === null || _a === void 0 ? void 0 : _a.GATE;
                        if (gate !== undefined && gate.call(this) === false) {
                            return undefined;
                        }
                    }
                    return prediction;
                };
            } else {
                return function() {
                    const nextToken = this.LA(1);
                    return choiceToAlt[nextToken.tokenTypeIdx];
                };
            }
        } else if (hasPredicates) {
            return function(orAlts) {
                const predicates = new PredicateSet();
                const length = orAlts === undefined ? 0 : orAlts.length;
                for(let i = 0; i < length; i++){
                    const gate = orAlts === null || orAlts === void 0 ? void 0 : orAlts[i].GATE;
                    predicates.set(i, gate === undefined || gate.call(this));
                }
                const result = adaptivePredict.call(this, dfas, decisionIndex, predicates, logging);
                return typeof result === 'number' ? result : undefined;
            };
        } else {
            return function() {
                const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);
                return typeof result === 'number' ? result : undefined;
            };
        }
    }
    buildLookaheadForOptional(options) {
        const { prodOccurrence, rule, prodType, dynamicTokensEnabled } = options;
        const dfas = this.dfas;
        const logging = this.logging;
        const key = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildATNKey"])(rule, prodType, prodOccurrence);
        const decisionState = this.atn.decisionMap[key];
        const decisionIndex = decisionState.decision;
        const alts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$parse$2f$grammar$2f$lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLookaheadPaths"])({
            maxLookahead: 1,
            occurrence: prodOccurrence,
            prodType,
            rule
        }), (e)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e, (g)=>g[0]);
        });
        if (isLL1Sequence(alts) && alts[0][0] && !dynamicTokensEnabled) {
            const alt = alts[0];
            const singleTokensTypes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(alt);
            if (singleTokensTypes.length === 1 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEmpty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(singleTokensTypes[0].categoryMatches)) {
                const expectedTokenType = singleTokensTypes[0];
                const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx;
                return function() {
                    return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;
                };
            } else {
                const choiceToAlt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$reduce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(singleTokensTypes, (result, currTokType)=>{
                    if (currTokType !== undefined) {
                        result[currTokType.tokenTypeIdx] = true;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$forEach$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currTokType.categoryMatches, (currExtendingType)=>{
                            result[currExtendingType] = true;
                        });
                    }
                    return result;
                }, {});
                return function() {
                    const nextToken = this.LA(1);
                    return choiceToAlt[nextToken.tokenTypeIdx] === true;
                };
            }
        }
        return function() {
            const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);
            return typeof result === "object" ? false : result === 0;
        };
    }
    constructor(options){
        var _a;
        super();
        this.logging = (_a = options === null || options === void 0 ? void 0 : options.logging) !== null && _a !== void 0 ? _a : (message)=>console.log(message);
    }
}
function isLL1Sequence(sequences) {
    let allowEmpty = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
    const fullSet = new Set();
    for (const alt of sequences){
        const altSet = new Set();
        for (const tokType of alt){
            if (tokType === undefined) {
                if (allowEmpty) {
                    break;
                } else {
                    return false;
                }
            }
            const indices = [
                tokType.tokenTypeIdx
            ].concat(tokType.categoryMatches);
            for (const index of indices){
                if (fullSet.has(index)) {
                    if (!altSet.has(index)) {
                        return false;
                    }
                } else {
                    fullSet.add(index);
                    altSet.add(index);
                }
            }
        }
    }
    return true;
}
function initATNSimulator(atn) {
    const decisionLength = atn.decisionStates.length;
    const decisionToDFA = Array(decisionLength);
    for(let i = 0; i < decisionLength; i++){
        decisionToDFA[i] = createDFACache(atn.decisionStates[i], i);
    }
    return decisionToDFA;
}
function adaptivePredict(dfaCaches, decision, predicateSet, logging) {
    const dfa = dfaCaches[decision](predicateSet);
    let start = dfa.start;
    if (start === undefined) {
        const closure = computeStartState(dfa.atnStartState);
        start = addDFAState(dfa, newDFAState(closure));
        dfa.start = start;
    }
    const alt = performLookahead.apply(this, [
        dfa,
        start,
        predicateSet,
        logging
    ]);
    return alt;
}
function performLookahead(dfa, s0, predicateSet, logging) {
    let previousD = s0;
    let i = 1;
    const path = [];
    let t = this.LA(i++);
    while(true){
        let d = getExistingTargetState(previousD, t);
        if (d === undefined) {
            d = computeLookaheadTarget.apply(this, [
                dfa,
                previousD,
                t,
                i,
                predicateSet,
                logging
            ]);
        }
        if (d === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DFA_ERROR"]) {
            return buildAdaptivePredictError(path, previousD, t);
        }
        if (d.isAcceptState === true) {
            return d.prediction;
        }
        previousD = d;
        path.push(t);
        t = this.LA(i++);
    }
}
function computeLookaheadTarget(dfa, previousD, token, lookahead, predicateSet, logging) {
    const reach = computeReachSet(previousD.configs, token, predicateSet);
    if (reach.size === 0) {
        addDFAEdge(dfa, previousD, token, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DFA_ERROR"]);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DFA_ERROR"];
    }
    let newState = newDFAState(reach);
    const predictedAlt = getUniqueAlt(reach, predicateSet);
    if (predictedAlt !== undefined) {
        newState.isAcceptState = true;
        newState.prediction = predictedAlt;
        newState.configs.uniqueAlt = predictedAlt;
    } else if (hasConflictTerminatingPrediction(reach)) {
        const prediction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(reach.alts);
        newState.isAcceptState = true;
        newState.prediction = prediction;
        newState.configs.uniqueAlt = prediction;
        reportLookaheadAmbiguity.apply(this, [
            dfa,
            lookahead,
            reach.alts,
            logging
        ]);
    }
    newState = addDFAEdge(dfa, previousD, token, newState);
    return newState;
}
function reportLookaheadAmbiguity(dfa, lookahead, ambiguityIndices, logging) {
    const prefixPath = [];
    for(let i = 1; i <= lookahead; i++){
        prefixPath.push(this.LA(i).tokenType);
    }
    const atnState = dfa.atnStartState;
    const topLevelRule = atnState.rule;
    const production = atnState.production;
    const message = buildAmbiguityError({
        topLevelRule,
        ambiguityIndices,
        production,
        prefixPath
    });
    logging(message);
}
function buildAmbiguityError(options) {
    const pathMsg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.prefixPath, (currtok)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$scan$2f$tokens_public$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tokenLabel"])(currtok)).join(", ");
    const occurrence = options.production.idx === 0 ? "" : options.production.idx;
    let currMessage = "Ambiguous Alternatives Detected: <".concat(options.ambiguityIndices.join(", "), "> in <").concat(getProductionDslName(options.production)).concat(occurrence, ">") + " inside <".concat(options.topLevelRule.name, "> Rule,\n") + "<".concat(pathMsg, "> may appears as a prefix path in all these alternatives.\n");
    currMessage = currMessage + "See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\n" + "For Further details.";
    return currMessage;
}
function getProductionDslName(prod) {
    if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NonTerminal"]) {
        return "SUBRULE";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Option"]) {
        return "OPTION";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alternation"]) {
        return "OR";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatory"]) {
        return "AT_LEAST_ONE";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionMandatoryWithSeparator"]) {
        return "AT_LEAST_ONE_SEP";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RepetitionWithSeparator"]) {
        return "MANY_SEP";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Repetition"]) {
        return "MANY";
    } else if (prod instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$chevrotain$2f$gast$2f$lib$2f$src$2f$model$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Terminal"]) {
        return "CONSUME";
    } else {
        throw Error("non exhaustive match");
    }
}
function buildAdaptivePredictError(path, previous, current) {
    const nextTransitions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$flatMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(previous.configs.elements, (e)=>e.state.transitions);
    const nextTokenTypes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$uniqBy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(nextTransitions.filter((e)=>e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AtomTransition"]).map((e)=>e.tokenType), (e)=>e.tokenTypeIdx);
    return {
        actualToken: current,
        possibleTokenTypes: nextTokenTypes,
        tokenPath: path
    };
}
function getExistingTargetState(state, token) {
    return state.edges[token.tokenTypeIdx];
}
function computeReachSet(configs, token, predicateSet) {
    const intermediate = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATNConfigSet"]();
    const skippedStopStates = [];
    for (const c of configs.elements){
        if (predicateSet.is(c.alt) === false) {
            continue;
        }
        if (c.state.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATN_RULE_STOP"]) {
            skippedStopStates.push(c);
            continue;
        }
        const transitionLength = c.state.transitions.length;
        for(let i = 0; i < transitionLength; i++){
            const transition = c.state.transitions[i];
            const target = getReachableTarget(transition, token);
            if (target !== undefined) {
                intermediate.add({
                    state: target,
                    alt: c.alt,
                    stack: c.stack
                });
            }
        }
    }
    let reach;
    if (skippedStopStates.length === 0 && intermediate.size === 1) {
        reach = intermediate;
    }
    if (reach === undefined) {
        reach = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATNConfigSet"]();
        for (const c of intermediate.elements){
            closure(c, reach);
        }
    }
    if (skippedStopStates.length > 0 && !hasConfigInRuleStopState(reach)) {
        for (const c of skippedStopStates){
            reach.add(c);
        }
    }
    return reach;
}
function getReachableTarget(transition, token) {
    if (transition instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AtomTransition"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2f$lib$2f$src$2f$scan$2f$tokens_public$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tokenMatcher"])(token, transition.tokenType)) {
        return transition.target;
    }
    return undefined;
}
function getUniqueAlt(configs, predicateSet) {
    let alt;
    for (const c of configs.elements){
        if (predicateSet.is(c.alt) === true) {
            if (alt === undefined) {
                alt = c.alt;
            } else if (alt !== c.alt) {
                return undefined;
            }
        }
    }
    return alt;
}
function newDFAState(closure) {
    return {
        configs: closure,
        edges: {},
        isAcceptState: false,
        prediction: -1
    };
}
function addDFAEdge(dfa, from, token, to) {
    to = addDFAState(dfa, to);
    from.edges[token.tokenTypeIdx] = to;
    return to;
}
function addDFAState(dfa, state) {
    if (state === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DFA_ERROR"]) {
        return state;
    }
    // Repetitions have the same config set
    // Therefore, storing the key of the config in a map allows us to create a loop in our DFA
    const mapKey = state.configs.key;
    const existing = dfa.states[mapKey];
    if (existing !== undefined) {
        return existing;
    }
    state.configs.finalize();
    dfa.states[mapKey] = state;
    return state;
}
function computeStartState(atnState) {
    const configs = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATNConfigSet"]();
    const numberOfTransitions = atnState.transitions.length;
    for(let i = 0; i < numberOfTransitions; i++){
        const target = atnState.transitions[i].target;
        const config = {
            state: target,
            alt: i,
            stack: []
        };
        closure(config, configs);
    }
    return configs;
}
function closure(config, configs) {
    const p = config.state;
    if (p.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATN_RULE_STOP"]) {
        if (config.stack.length > 0) {
            const atnStack = [
                ...config.stack
            ];
            const followState = atnStack.pop();
            const followConfig = {
                state: followState,
                alt: config.alt,
                stack: atnStack
            };
            closure(followConfig, configs);
        } else {
            // Dipping into outer context, simply add the config
            // This will stop computation once every config is at the rule stop state
            configs.add(config);
        }
        return;
    }
    if (!p.epsilonOnlyTransitions) {
        configs.add(config);
    }
    const transitionLength = p.transitions.length;
    for(let i = 0; i < transitionLength; i++){
        const transition = p.transitions[i];
        const c = getEpsilonTarget(config, transition);
        if (c !== undefined) {
            closure(c, configs);
        }
    }
}
function getEpsilonTarget(config, transition) {
    if (transition instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EpsilonTransition"]) {
        return {
            state: transition.target,
            alt: config.alt,
            stack: config.stack
        };
    } else if (transition instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RuleTransition"]) {
        const stack = [
            ...config.stack,
            transition.followState
        ];
        return {
            state: transition.target,
            alt: config.alt,
            stack
        };
    }
    return undefined;
}
function hasConfigInRuleStopState(configs) {
    for (const c of configs.elements){
        if (c.state.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATN_RULE_STOP"]) {
            return true;
        }
    }
    return false;
}
function allConfigsInRuleStopStates(configs) {
    for (const c of configs.elements){
        if (c.state.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$atn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ATN_RULE_STOP"]) {
            return false;
        }
    }
    return true;
}
function hasConflictTerminatingPrediction(configs) {
    if (allConfigsInRuleStopStates(configs)) {
        return true;
    }
    const altSets = getConflictingAltSets(configs.elements);
    const heuristic = hasConflictingAltSet(altSets) && !hasStateAssociatedWithOneAlt(altSets);
    return heuristic;
}
function getConflictingAltSets(configs) {
    const configToAlts = new Map();
    for (const c of configs){
        const key = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$dfa$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getATNConfigKey"])(c, false);
        let alts = configToAlts.get(key);
        if (alts === undefined) {
            alts = {};
            configToAlts.set(key, alts);
        }
        alts[c.alt] = true;
    }
    return configToAlts;
}
function hasConflictingAltSet(altSets) {
    for (const value of Array.from(altSets.values())){
        if (Object.keys(value).length > 1) {
            return true;
        }
    }
    return false;
}
function hasStateAssociatedWithOneAlt(altSets) {
    for (const value of Array.from(altSets.values())){
        if (Object.keys(value).length === 1) {
            return true;
        }
    }
    return false;
} //# sourceMappingURL=all-star-lookahead.js.map
}),
"[project]/node_modules/chevrotain-allstar/lib/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$all$2d$star$2d$lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain-allstar/lib/all-star-lookahead.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
}),
"[project]/node_modules/chevrotain-allstar/lib/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$all$2d$star$2d$lookahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/chevrotain-allstar/lib/all-star-lookahead.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$chevrotain$2d$allstar$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/chevrotain-allstar/lib/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/vscode-languageserver-types/lib/esm/main.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */ __turbopack_context__.s({
    "AnnotatedTextEdit": ()=>AnnotatedTextEdit,
    "ChangeAnnotation": ()=>ChangeAnnotation,
    "ChangeAnnotationIdentifier": ()=>ChangeAnnotationIdentifier,
    "CodeAction": ()=>CodeAction,
    "CodeActionContext": ()=>CodeActionContext,
    "CodeActionKind": ()=>CodeActionKind,
    "CodeActionTriggerKind": ()=>CodeActionTriggerKind,
    "CodeDescription": ()=>CodeDescription,
    "CodeLens": ()=>CodeLens,
    "Color": ()=>Color,
    "ColorInformation": ()=>ColorInformation,
    "ColorPresentation": ()=>ColorPresentation,
    "Command": ()=>Command,
    "CompletionItem": ()=>CompletionItem,
    "CompletionItemKind": ()=>CompletionItemKind,
    "CompletionItemLabelDetails": ()=>CompletionItemLabelDetails,
    "CompletionItemTag": ()=>CompletionItemTag,
    "CompletionList": ()=>CompletionList,
    "CreateFile": ()=>CreateFile,
    "DeleteFile": ()=>DeleteFile,
    "Diagnostic": ()=>Diagnostic,
    "DiagnosticRelatedInformation": ()=>DiagnosticRelatedInformation,
    "DiagnosticSeverity": ()=>DiagnosticSeverity,
    "DiagnosticTag": ()=>DiagnosticTag,
    "DocumentHighlight": ()=>DocumentHighlight,
    "DocumentHighlightKind": ()=>DocumentHighlightKind,
    "DocumentLink": ()=>DocumentLink,
    "DocumentSymbol": ()=>DocumentSymbol,
    "DocumentUri": ()=>DocumentUri,
    "EOL": ()=>EOL,
    "FoldingRange": ()=>FoldingRange,
    "FoldingRangeKind": ()=>FoldingRangeKind,
    "FormattingOptions": ()=>FormattingOptions,
    "Hover": ()=>Hover,
    "InlayHint": ()=>InlayHint,
    "InlayHintKind": ()=>InlayHintKind,
    "InlayHintLabelPart": ()=>InlayHintLabelPart,
    "InlineCompletionContext": ()=>InlineCompletionContext,
    "InlineCompletionItem": ()=>InlineCompletionItem,
    "InlineCompletionList": ()=>InlineCompletionList,
    "InlineCompletionTriggerKind": ()=>InlineCompletionTriggerKind,
    "InlineValueContext": ()=>InlineValueContext,
    "InlineValueEvaluatableExpression": ()=>InlineValueEvaluatableExpression,
    "InlineValueText": ()=>InlineValueText,
    "InlineValueVariableLookup": ()=>InlineValueVariableLookup,
    "InsertReplaceEdit": ()=>InsertReplaceEdit,
    "InsertTextFormat": ()=>InsertTextFormat,
    "InsertTextMode": ()=>InsertTextMode,
    "Location": ()=>Location,
    "LocationLink": ()=>LocationLink,
    "MarkedString": ()=>MarkedString,
    "MarkupContent": ()=>MarkupContent,
    "MarkupKind": ()=>MarkupKind,
    "OptionalVersionedTextDocumentIdentifier": ()=>OptionalVersionedTextDocumentIdentifier,
    "ParameterInformation": ()=>ParameterInformation,
    "Position": ()=>Position,
    "Range": ()=>Range,
    "RenameFile": ()=>RenameFile,
    "SelectedCompletionInfo": ()=>SelectedCompletionInfo,
    "SelectionRange": ()=>SelectionRange,
    "SemanticTokenModifiers": ()=>SemanticTokenModifiers,
    "SemanticTokenTypes": ()=>SemanticTokenTypes,
    "SemanticTokens": ()=>SemanticTokens,
    "SignatureInformation": ()=>SignatureInformation,
    "StringValue": ()=>StringValue,
    "SymbolInformation": ()=>SymbolInformation,
    "SymbolKind": ()=>SymbolKind,
    "SymbolTag": ()=>SymbolTag,
    "TextDocument": ()=>TextDocument,
    "TextDocumentEdit": ()=>TextDocumentEdit,
    "TextDocumentIdentifier": ()=>TextDocumentIdentifier,
    "TextDocumentItem": ()=>TextDocumentItem,
    "TextEdit": ()=>TextEdit,
    "URI": ()=>URI,
    "VersionedTextDocumentIdentifier": ()=>VersionedTextDocumentIdentifier,
    "WorkspaceChange": ()=>WorkspaceChange,
    "WorkspaceEdit": ()=>WorkspaceEdit,
    "WorkspaceFolder": ()=>WorkspaceFolder,
    "WorkspaceSymbol": ()=>WorkspaceSymbol,
    "integer": ()=>integer,
    "uinteger": ()=>uinteger
});
'use strict';
var DocumentUri;
(function(DocumentUri) {
    function is(value) {
        return typeof value === 'string';
    }
    DocumentUri.is = is;
})(DocumentUri || (DocumentUri = {}));
var URI;
(function(URI) {
    function is(value) {
        return typeof value === 'string';
    }
    URI.is = is;
})(URI || (URI = {}));
var integer;
(function(integer) {
    integer.MIN_VALUE = -2147483648;
    integer.MAX_VALUE = 2147483647;
    function is(value) {
        return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;
    }
    integer.is = is;
})(integer || (integer = {}));
var uinteger;
(function(uinteger) {
    uinteger.MIN_VALUE = 0;
    uinteger.MAX_VALUE = 2147483647;
    function is(value) {
        return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;
    }
    uinteger.is = is;
})(uinteger || (uinteger = {}));
var Position;
(function(Position) {
    /**
     * Creates a new Position literal from the given line and character.
     * @param line The position's line.
     * @param character The position's character.
     */ function create(line, character) {
        if (line === Number.MAX_VALUE) {
            line = uinteger.MAX_VALUE;
        }
        if (character === Number.MAX_VALUE) {
            character = uinteger.MAX_VALUE;
        }
        return {
            line,
            character
        };
    }
    Position.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Position} interface.
     */ function is(value) {
        let candidate = value;
        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);
    }
    Position.is = is;
})(Position || (Position = {}));
var Range;
(function(Range) {
    function create(one, two, three, four) {
        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {
            return {
                start: Position.create(one, two),
                end: Position.create(three, four)
            };
        } else if (Position.is(one) && Position.is(two)) {
            return {
                start: one,
                end: two
            };
        } else {
            throw new Error("Range#create called with invalid arguments[".concat(one, ", ").concat(two, ", ").concat(three, ", ").concat(four, "]"));
        }
    }
    Range.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Range} interface.
     */ function is(value) {
        let candidate = value;
        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);
    }
    Range.is = is;
})(Range || (Range = {}));
var Location;
(function(Location) {
    /**
     * Creates a Location literal.
     * @param uri The location's uri.
     * @param range The location's range.
     */ function create(uri, range) {
        return {
            uri,
            range
        };
    }
    Location.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Location} interface.
     */ function is(value) {
        let candidate = value;
        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));
    }
    Location.is = is;
})(Location || (Location = {}));
var LocationLink;
(function(LocationLink) {
    /**
     * Creates a LocationLink literal.
     * @param targetUri The definition's uri.
     * @param targetRange The full range of the definition.
     * @param targetSelectionRange The span of the symbol definition at the target.
     * @param originSelectionRange The span of the symbol being defined in the originating source file.
     */ function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {
        return {
            targetUri,
            targetRange,
            targetSelectionRange,
            originSelectionRange
        };
    }
    LocationLink.create = create;
    /**
     * Checks whether the given literal conforms to the {@link LocationLink} interface.
     */ function is(value) {
        let candidate = value;
        return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));
    }
    LocationLink.is = is;
})(LocationLink || (LocationLink = {}));
var Color;
(function(Color) {
    /**
     * Creates a new Color literal.
     */ function create(red, green, blue, alpha) {
        return {
            red,
            green,
            blue,
            alpha
        };
    }
    Color.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Color} interface.
     */ function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);
    }
    Color.is = is;
})(Color || (Color = {}));
var ColorInformation;
(function(ColorInformation) {
    /**
     * Creates a new ColorInformation literal.
     */ function create(range, color) {
        return {
            range,
            color
        };
    }
    ColorInformation.create = create;
    /**
     * Checks whether the given literal conforms to the {@link ColorInformation} interface.
     */ function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);
    }
    ColorInformation.is = is;
})(ColorInformation || (ColorInformation = {}));
var ColorPresentation;
(function(ColorPresentation) {
    /**
     * Creates a new ColorInformation literal.
     */ function create(label, textEdit, additionalTextEdits) {
        return {
            label,
            textEdit,
            additionalTextEdits
        };
    }
    ColorPresentation.create = create;
    /**
     * Checks whether the given literal conforms to the {@link ColorInformation} interface.
     */ function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));
    }
    ColorPresentation.is = is;
})(ColorPresentation || (ColorPresentation = {}));
var FoldingRangeKind;
(function(FoldingRangeKind) {
    /**
     * Folding range for a comment
     */ FoldingRangeKind.Comment = 'comment';
    /**
     * Folding range for an import or include
     */ FoldingRangeKind.Imports = 'imports';
    /**
     * Folding range for a region (e.g. `#region`)
     */ FoldingRangeKind.Region = 'region';
})(FoldingRangeKind || (FoldingRangeKind = {}));
var FoldingRange;
(function(FoldingRange) {
    /**
     * Creates a new FoldingRange literal.
     */ function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {
        const result = {
            startLine,
            endLine
        };
        if (Is.defined(startCharacter)) {
            result.startCharacter = startCharacter;
        }
        if (Is.defined(endCharacter)) {
            result.endCharacter = endCharacter;
        }
        if (Is.defined(kind)) {
            result.kind = kind;
        }
        if (Is.defined(collapsedText)) {
            result.collapsedText = collapsedText;
        }
        return result;
    }
    FoldingRange.create = create;
    /**
     * Checks whether the given literal conforms to the {@link FoldingRange} interface.
     */ function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));
    }
    FoldingRange.is = is;
})(FoldingRange || (FoldingRange = {}));
var DiagnosticRelatedInformation;
(function(DiagnosticRelatedInformation) {
    /**
     * Creates a new DiagnosticRelatedInformation literal.
     */ function create(location, message) {
        return {
            location,
            message
        };
    }
    DiagnosticRelatedInformation.create = create;
    /**
     * Checks whether the given literal conforms to the {@link DiagnosticRelatedInformation} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);
    }
    DiagnosticRelatedInformation.is = is;
})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));
var DiagnosticSeverity;
(function(DiagnosticSeverity) {
    /**
     * Reports an error.
     */ DiagnosticSeverity.Error = 1;
    /**
     * Reports a warning.
     */ DiagnosticSeverity.Warning = 2;
    /**
     * Reports an information.
     */ DiagnosticSeverity.Information = 3;
    /**
     * Reports a hint.
     */ DiagnosticSeverity.Hint = 4;
})(DiagnosticSeverity || (DiagnosticSeverity = {}));
var DiagnosticTag;
(function(DiagnosticTag) {
    /**
     * Unused or unnecessary code.
     *
     * Clients are allowed to render diagnostics with this tag faded out instead of having
     * an error squiggle.
     */ DiagnosticTag.Unnecessary = 1;
    /**
     * Deprecated or obsolete code.
     *
     * Clients are allowed to rendered diagnostics with this tag strike through.
     */ DiagnosticTag.Deprecated = 2;
})(DiagnosticTag || (DiagnosticTag = {}));
var CodeDescription;
(function(CodeDescription) {
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.string(candidate.href);
    }
    CodeDescription.is = is;
})(CodeDescription || (CodeDescription = {}));
var Diagnostic;
(function(Diagnostic) {
    /**
     * Creates a new Diagnostic literal.
     */ function create(range, message, severity, code, source, relatedInformation) {
        let result = {
            range,
            message
        };
        if (Is.defined(severity)) {
            result.severity = severity;
        }
        if (Is.defined(code)) {
            result.code = code;
        }
        if (Is.defined(source)) {
            result.source = source;
        }
        if (Is.defined(relatedInformation)) {
            result.relatedInformation = relatedInformation;
        }
        return result;
    }
    Diagnostic.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Diagnostic} interface.
     */ function is(value) {
        var _a;
        let candidate = value;
        return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));
    }
    Diagnostic.is = is;
})(Diagnostic || (Diagnostic = {}));
var Command;
(function(Command) {
    /**
     * Creates a new Command literal.
     */ function create(title, command) {
        for(var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++){
            args[_key - 2] = arguments[_key];
        }
        let result = {
            title,
            command
        };
        if (Is.defined(args) && args.length > 0) {
            result.arguments = args;
        }
        return result;
    }
    Command.create = create;
    /**
     * Checks whether the given literal conforms to the {@link Command} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);
    }
    Command.is = is;
})(Command || (Command = {}));
var TextEdit;
(function(TextEdit) {
    /**
     * Creates a replace text edit.
     * @param range The range of text to be replaced.
     * @param newText The new text.
     */ function replace(range, newText) {
        return {
            range,
            newText
        };
    }
    TextEdit.replace = replace;
    /**
     * Creates an insert text edit.
     * @param position The position to insert the text at.
     * @param newText The text to be inserted.
     */ function insert(position, newText) {
        return {
            range: {
                start: position,
                end: position
            },
            newText
        };
    }
    TextEdit.insert = insert;
    /**
     * Creates a delete text edit.
     * @param range The range of text to be deleted.
     */ function del(range) {
        return {
            range,
            newText: ''
        };
    }
    TextEdit.del = del;
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);
    }
    TextEdit.is = is;
})(TextEdit || (TextEdit = {}));
var ChangeAnnotation;
(function(ChangeAnnotation) {
    function create(label, needsConfirmation, description) {
        const result = {
            label
        };
        if (needsConfirmation !== undefined) {
            result.needsConfirmation = needsConfirmation;
        }
        if (description !== undefined) {
            result.description = description;
        }
        return result;
    }
    ChangeAnnotation.create = create;
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) && (Is.string(candidate.description) || candidate.description === undefined);
    }
    ChangeAnnotation.is = is;
})(ChangeAnnotation || (ChangeAnnotation = {}));
var ChangeAnnotationIdentifier;
(function(ChangeAnnotationIdentifier) {
    function is(value) {
        const candidate = value;
        return Is.string(candidate);
    }
    ChangeAnnotationIdentifier.is = is;
})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));
var AnnotatedTextEdit;
(function(AnnotatedTextEdit) {
    /**
     * Creates an annotated replace text edit.
     *
     * @param range The range of text to be replaced.
     * @param newText The new text.
     * @param annotation The annotation.
     */ function replace(range, newText, annotation) {
        return {
            range,
            newText,
            annotationId: annotation
        };
    }
    AnnotatedTextEdit.replace = replace;
    /**
     * Creates an annotated insert text edit.
     *
     * @param position The position to insert the text at.
     * @param newText The text to be inserted.
     * @param annotation The annotation.
     */ function insert(position, newText, annotation) {
        return {
            range: {
                start: position,
                end: position
            },
            newText,
            annotationId: annotation
        };
    }
    AnnotatedTextEdit.insert = insert;
    /**
     * Creates an annotated delete text edit.
     *
     * @param range The range of text to be deleted.
     * @param annotation The annotation.
     */ function del(range, annotation) {
        return {
            range,
            newText: '',
            annotationId: annotation
        };
    }
    AnnotatedTextEdit.del = del;
    function is(value) {
        const candidate = value;
        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));
    }
    AnnotatedTextEdit.is = is;
})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));
var TextDocumentEdit;
(function(TextDocumentEdit) {
    /**
     * Creates a new `TextDocumentEdit`
     */ function create(textDocument, edits) {
        return {
            textDocument,
            edits
        };
    }
    TextDocumentEdit.create = create;
    function is(value) {
        let candidate = value;
        return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);
    }
    TextDocumentEdit.is = is;
})(TextDocumentEdit || (TextDocumentEdit = {}));
var CreateFile;
(function(CreateFile) {
    function create(uri, options, annotation) {
        let result = {
            kind: 'create',
            uri
        };
        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {
            result.options = options;
        }
        if (annotation !== undefined) {
            result.annotationId = annotation;
        }
        return result;
    }
    CreateFile.create = create;
    function is(value) {
        let candidate = value;
        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined || (candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));
    }
    CreateFile.is = is;
})(CreateFile || (CreateFile = {}));
var RenameFile;
(function(RenameFile) {
    function create(oldUri, newUri, options, annotation) {
        let result = {
            kind: 'rename',
            oldUri,
            newUri
        };
        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {
            result.options = options;
        }
        if (annotation !== undefined) {
            result.annotationId = annotation;
        }
        return result;
    }
    RenameFile.create = create;
    function is(value) {
        let candidate = value;
        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined || (candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));
    }
    RenameFile.is = is;
})(RenameFile || (RenameFile = {}));
var DeleteFile;
(function(DeleteFile) {
    function create(uri, options, annotation) {
        let result = {
            kind: 'delete',
            uri
        };
        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {
            result.options = options;
        }
        if (annotation !== undefined) {
            result.annotationId = annotation;
        }
        return result;
    }
    DeleteFile.create = create;
    function is(value) {
        let candidate = value;
        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined || (candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));
    }
    DeleteFile.is = is;
})(DeleteFile || (DeleteFile = {}));
var WorkspaceEdit;
(function(WorkspaceEdit) {
    function is(value) {
        let candidate = value;
        return candidate && (candidate.changes !== undefined || candidate.documentChanges !== undefined) && (candidate.documentChanges === undefined || candidate.documentChanges.every((change)=>{
            if (Is.string(change.kind)) {
                return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);
            } else {
                return TextDocumentEdit.is(change);
            }
        }));
    }
    WorkspaceEdit.is = is;
})(WorkspaceEdit || (WorkspaceEdit = {}));
class TextEditChangeImpl {
    insert(position, newText, annotation) {
        let edit;
        let id;
        if (annotation === undefined) {
            edit = TextEdit.insert(position, newText);
        } else if (ChangeAnnotationIdentifier.is(annotation)) {
            id = annotation;
            edit = AnnotatedTextEdit.insert(position, newText, annotation);
        } else {
            this.assertChangeAnnotations(this.changeAnnotations);
            id = this.changeAnnotations.manage(annotation);
            edit = AnnotatedTextEdit.insert(position, newText, id);
        }
        this.edits.push(edit);
        if (id !== undefined) {
            return id;
        }
    }
    replace(range, newText, annotation) {
        let edit;
        let id;
        if (annotation === undefined) {
            edit = TextEdit.replace(range, newText);
        } else if (ChangeAnnotationIdentifier.is(annotation)) {
            id = annotation;
            edit = AnnotatedTextEdit.replace(range, newText, annotation);
        } else {
            this.assertChangeAnnotations(this.changeAnnotations);
            id = this.changeAnnotations.manage(annotation);
            edit = AnnotatedTextEdit.replace(range, newText, id);
        }
        this.edits.push(edit);
        if (id !== undefined) {
            return id;
        }
    }
    delete(range, annotation) {
        let edit;
        let id;
        if (annotation === undefined) {
            edit = TextEdit.del(range);
        } else if (ChangeAnnotationIdentifier.is(annotation)) {
            id = annotation;
            edit = AnnotatedTextEdit.del(range, annotation);
        } else {
            this.assertChangeAnnotations(this.changeAnnotations);
            id = this.changeAnnotations.manage(annotation);
            edit = AnnotatedTextEdit.del(range, id);
        }
        this.edits.push(edit);
        if (id !== undefined) {
            return id;
        }
    }
    add(edit) {
        this.edits.push(edit);
    }
    all() {
        return this.edits;
    }
    clear() {
        this.edits.splice(0, this.edits.length);
    }
    assertChangeAnnotations(value) {
        if (value === undefined) {
            throw new Error("Text edit change is not configured to manage change annotations.");
        }
    }
    constructor(edits, changeAnnotations){
        this.edits = edits;
        this.changeAnnotations = changeAnnotations;
    }
}
/**
 * A helper class
 */ class ChangeAnnotations {
    all() {
        return this._annotations;
    }
    get size() {
        return this._size;
    }
    manage(idOrAnnotation, annotation) {
        let id;
        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {
            id = idOrAnnotation;
        } else {
            id = this.nextId();
            annotation = idOrAnnotation;
        }
        if (this._annotations[id] !== undefined) {
            throw new Error("Id ".concat(id, " is already in use."));
        }
        if (annotation === undefined) {
            throw new Error("No annotation provided for id ".concat(id));
        }
        this._annotations[id] = annotation;
        this._size++;
        return id;
    }
    nextId() {
        this._counter++;
        return this._counter.toString();
    }
    constructor(annotations){
        this._annotations = annotations === undefined ? Object.create(null) : annotations;
        this._counter = 0;
        this._size = 0;
    }
}
class WorkspaceChange {
    /**
     * Returns the underlying {@link WorkspaceEdit} literal
     * use to be returned from a workspace edit operation like rename.
     */ get edit() {
        this.initDocumentChanges();
        if (this._changeAnnotations !== undefined) {
            if (this._changeAnnotations.size === 0) {
                this._workspaceEdit.changeAnnotations = undefined;
            } else {
                this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();
            }
        }
        return this._workspaceEdit;
    }
    getTextEditChange(key) {
        if (OptionalVersionedTextDocumentIdentifier.is(key)) {
            this.initDocumentChanges();
            if (this._workspaceEdit.documentChanges === undefined) {
                throw new Error('Workspace edit is not configured for document changes.');
            }
            const textDocument = {
                uri: key.uri,
                version: key.version
            };
            let result = this._textEditChanges[textDocument.uri];
            if (!result) {
                const edits = [];
                const textDocumentEdit = {
                    textDocument,
                    edits
                };
                this._workspaceEdit.documentChanges.push(textDocumentEdit);
                result = new TextEditChangeImpl(edits, this._changeAnnotations);
                this._textEditChanges[textDocument.uri] = result;
            }
            return result;
        } else {
            this.initChanges();
            if (this._workspaceEdit.changes === undefined) {
                throw new Error('Workspace edit is not configured for normal text edit changes.');
            }
            let result = this._textEditChanges[key];
            if (!result) {
                let edits = [];
                this._workspaceEdit.changes[key] = edits;
                result = new TextEditChangeImpl(edits);
                this._textEditChanges[key] = result;
            }
            return result;
        }
    }
    initDocumentChanges() {
        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {
            this._changeAnnotations = new ChangeAnnotations();
            this._workspaceEdit.documentChanges = [];
            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();
        }
    }
    initChanges() {
        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {
            this._workspaceEdit.changes = Object.create(null);
        }
    }
    createFile(uri, optionsOrAnnotation, options) {
        this.initDocumentChanges();
        if (this._workspaceEdit.documentChanges === undefined) {
            throw new Error('Workspace edit is not configured for document changes.');
        }
        let annotation;
        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {
            annotation = optionsOrAnnotation;
        } else {
            options = optionsOrAnnotation;
        }
        let operation;
        let id;
        if (annotation === undefined) {
            operation = CreateFile.create(uri, options);
        } else {
            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);
            operation = CreateFile.create(uri, options, id);
        }
        this._workspaceEdit.documentChanges.push(operation);
        if (id !== undefined) {
            return id;
        }
    }
    renameFile(oldUri, newUri, optionsOrAnnotation, options) {
        this.initDocumentChanges();
        if (this._workspaceEdit.documentChanges === undefined) {
            throw new Error('Workspace edit is not configured for document changes.');
        }
        let annotation;
        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {
            annotation = optionsOrAnnotation;
        } else {
            options = optionsOrAnnotation;
        }
        let operation;
        let id;
        if (annotation === undefined) {
            operation = RenameFile.create(oldUri, newUri, options);
        } else {
            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);
            operation = RenameFile.create(oldUri, newUri, options, id);
        }
        this._workspaceEdit.documentChanges.push(operation);
        if (id !== undefined) {
            return id;
        }
    }
    deleteFile(uri, optionsOrAnnotation, options) {
        this.initDocumentChanges();
        if (this._workspaceEdit.documentChanges === undefined) {
            throw new Error('Workspace edit is not configured for document changes.');
        }
        let annotation;
        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {
            annotation = optionsOrAnnotation;
        } else {
            options = optionsOrAnnotation;
        }
        let operation;
        let id;
        if (annotation === undefined) {
            operation = DeleteFile.create(uri, options);
        } else {
            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);
            operation = DeleteFile.create(uri, options, id);
        }
        this._workspaceEdit.documentChanges.push(operation);
        if (id !== undefined) {
            return id;
        }
    }
    constructor(workspaceEdit){
        this._textEditChanges = Object.create(null);
        if (workspaceEdit !== undefined) {
            this._workspaceEdit = workspaceEdit;
            if (workspaceEdit.documentChanges) {
                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);
                workspaceEdit.changeAnnotations = this._changeAnnotations.all();
                workspaceEdit.documentChanges.forEach((change)=>{
                    if (TextDocumentEdit.is(change)) {
                        const textEditChange = new TextEditChangeImpl(change.edits, this._changeAnnotations);
                        this._textEditChanges[change.textDocument.uri] = textEditChange;
                    }
                });
            } else if (workspaceEdit.changes) {
                Object.keys(workspaceEdit.changes).forEach((key)=>{
                    const textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);
                    this._textEditChanges[key] = textEditChange;
                });
            }
        } else {
            this._workspaceEdit = {};
        }
    }
}
var TextDocumentIdentifier;
(function(TextDocumentIdentifier) {
    /**
     * Creates a new TextDocumentIdentifier literal.
     * @param uri The document's uri.
     */ function create(uri) {
        return {
            uri
        };
    }
    TextDocumentIdentifier.create = create;
    /**
     * Checks whether the given literal conforms to the {@link TextDocumentIdentifier} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.uri);
    }
    TextDocumentIdentifier.is = is;
})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));
var VersionedTextDocumentIdentifier;
(function(VersionedTextDocumentIdentifier) {
    /**
     * Creates a new VersionedTextDocumentIdentifier literal.
     * @param uri The document's uri.
     * @param version The document's version.
     */ function create(uri, version) {
        return {
            uri,
            version
        };
    }
    VersionedTextDocumentIdentifier.create = create;
    /**
     * Checks whether the given literal conforms to the {@link VersionedTextDocumentIdentifier} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);
    }
    VersionedTextDocumentIdentifier.is = is;
})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));
var OptionalVersionedTextDocumentIdentifier;
(function(OptionalVersionedTextDocumentIdentifier) {
    /**
     * Creates a new OptionalVersionedTextDocumentIdentifier literal.
     * @param uri The document's uri.
     * @param version The document's version.
     */ function create(uri, version) {
        return {
            uri,
            version
        };
    }
    OptionalVersionedTextDocumentIdentifier.create = create;
    /**
     * Checks whether the given literal conforms to the {@link OptionalVersionedTextDocumentIdentifier} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));
    }
    OptionalVersionedTextDocumentIdentifier.is = is;
})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));
var TextDocumentItem;
(function(TextDocumentItem) {
    /**
     * Creates a new TextDocumentItem literal.
     * @param uri The document's uri.
     * @param languageId The document's language identifier.
     * @param version The document's version number.
     * @param text The document's text.
     */ function create(uri, languageId, version, text) {
        return {
            uri,
            languageId,
            version,
            text
        };
    }
    TextDocumentItem.create = create;
    /**
     * Checks whether the given literal conforms to the {@link TextDocumentItem} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);
    }
    TextDocumentItem.is = is;
})(TextDocumentItem || (TextDocumentItem = {}));
var MarkupKind;
(function(MarkupKind) {
    /**
     * Plain text is supported as a content format
     */ MarkupKind.PlainText = 'plaintext';
    /**
     * Markdown is supported as a content format
     */ MarkupKind.Markdown = 'markdown';
    /**
     * Checks whether the given value is a value of the {@link MarkupKind} type.
     */ function is(value) {
        const candidate = value;
        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;
    }
    MarkupKind.is = is;
})(MarkupKind || (MarkupKind = {}));
var MarkupContent;
(function(MarkupContent) {
    /**
     * Checks whether the given value conforms to the {@link MarkupContent} interface.
     */ function is(value) {
        const candidate = value;
        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);
    }
    MarkupContent.is = is;
})(MarkupContent || (MarkupContent = {}));
var CompletionItemKind;
(function(CompletionItemKind) {
    CompletionItemKind.Text = 1;
    CompletionItemKind.Method = 2;
    CompletionItemKind.Function = 3;
    CompletionItemKind.Constructor = 4;
    CompletionItemKind.Field = 5;
    CompletionItemKind.Variable = 6;
    CompletionItemKind.Class = 7;
    CompletionItemKind.Interface = 8;
    CompletionItemKind.Module = 9;
    CompletionItemKind.Property = 10;
    CompletionItemKind.Unit = 11;
    CompletionItemKind.Value = 12;
    CompletionItemKind.Enum = 13;
    CompletionItemKind.Keyword = 14;
    CompletionItemKind.Snippet = 15;
    CompletionItemKind.Color = 16;
    CompletionItemKind.File = 17;
    CompletionItemKind.Reference = 18;
    CompletionItemKind.Folder = 19;
    CompletionItemKind.EnumMember = 20;
    CompletionItemKind.Constant = 21;
    CompletionItemKind.Struct = 22;
    CompletionItemKind.Event = 23;
    CompletionItemKind.Operator = 24;
    CompletionItemKind.TypeParameter = 25;
})(CompletionItemKind || (CompletionItemKind = {}));
var InsertTextFormat;
(function(InsertTextFormat) {
    /**
     * The primary text to be inserted is treated as a plain string.
     */ InsertTextFormat.PlainText = 1;
    /**
     * The primary text to be inserted is treated as a snippet.
     *
     * A snippet can define tab stops and placeholders with `$1`, `$2`
     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to
     * the end of the snippet. Placeholders with equal identifiers are linked,
     * that is typing in one will update others too.
     *
     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax
     */ InsertTextFormat.Snippet = 2;
})(InsertTextFormat || (InsertTextFormat = {}));
var CompletionItemTag;
(function(CompletionItemTag) {
    /**
     * Render a completion as obsolete, usually using a strike-out.
     */ CompletionItemTag.Deprecated = 1;
})(CompletionItemTag || (CompletionItemTag = {}));
var InsertReplaceEdit;
(function(InsertReplaceEdit) {
    /**
     * Creates a new insert / replace edit
     */ function create(newText, insert, replace) {
        return {
            newText,
            insert,
            replace
        };
    }
    InsertReplaceEdit.create = create;
    /**
     * Checks whether the given literal conforms to the {@link InsertReplaceEdit} interface.
     */ function is(value) {
        const candidate = value;
        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);
    }
    InsertReplaceEdit.is = is;
})(InsertReplaceEdit || (InsertReplaceEdit = {}));
var InsertTextMode;
(function(InsertTextMode) {
    /**
     * The insertion or replace strings is taken as it is. If the
     * value is multi line the lines below the cursor will be
     * inserted using the indentation defined in the string value.
     * The client will not apply any kind of adjustments to the
     * string.
     */ InsertTextMode.asIs = 1;
    /**
     * The editor adjusts leading whitespace of new lines so that
     * they match the indentation up to the cursor of the line for
     * which the item is accepted.
     *
     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a
     * multi line completion item is indented using 2 tabs and all
     * following lines inserted will be indented using 2 tabs as well.
     */ InsertTextMode.adjustIndentation = 2;
})(InsertTextMode || (InsertTextMode = {}));
var CompletionItemLabelDetails;
(function(CompletionItemLabelDetails) {
    function is(value) {
        const candidate = value;
        return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) && (Is.string(candidate.description) || candidate.description === undefined);
    }
    CompletionItemLabelDetails.is = is;
})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));
var CompletionItem;
(function(CompletionItem) {
    /**
     * Create a completion item and seed it with a label.
     * @param label The completion item's label
     */ function create(label) {
        return {
            label
        };
    }
    CompletionItem.create = create;
})(CompletionItem || (CompletionItem = {}));
var CompletionList;
(function(CompletionList) {
    /**
     * Creates a new completion list.
     *
     * @param items The completion items.
     * @param isIncomplete The list is not complete.
     */ function create(items, isIncomplete) {
        return {
            items: items ? items : [],
            isIncomplete: !!isIncomplete
        };
    }
    CompletionList.create = create;
})(CompletionList || (CompletionList = {}));
var MarkedString;
(function(MarkedString) {
    /**
     * Creates a marked string from plain text.
     *
     * @param plainText The plain text.
     */ function fromPlainText(plainText) {
        return plainText.replace(/[\\`*_{}[\]()#+\-.!]/g, '\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash
    }
    MarkedString.fromPlainText = fromPlainText;
    /**
     * Checks whether the given value conforms to the {@link MarkedString} type.
     */ function is(value) {
        const candidate = value;
        return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);
    }
    MarkedString.is = is;
})(MarkedString || (MarkedString = {}));
var Hover;
(function(Hover) {
    /**
     * Checks whether the given value conforms to the {@link Hover} interface.
     */ function is(value) {
        let candidate = value;
        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));
    }
    Hover.is = is;
})(Hover || (Hover = {}));
var ParameterInformation;
(function(ParameterInformation) {
    /**
     * Creates a new parameter information literal.
     *
     * @param label A label string.
     * @param documentation A doc string.
     */ function create(label, documentation) {
        return documentation ? {
            label,
            documentation
        } : {
            label
        };
    }
    ParameterInformation.create = create;
})(ParameterInformation || (ParameterInformation = {}));
var SignatureInformation;
(function(SignatureInformation) {
    function create(label, documentation) {
        for(var _len = arguments.length, parameters = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++){
            parameters[_key - 2] = arguments[_key];
        }
        let result = {
            label
        };
        if (Is.defined(documentation)) {
            result.documentation = documentation;
        }
        if (Is.defined(parameters)) {
            result.parameters = parameters;
        } else {
            result.parameters = [];
        }
        return result;
    }
    SignatureInformation.create = create;
})(SignatureInformation || (SignatureInformation = {}));
var DocumentHighlightKind;
(function(DocumentHighlightKind) {
    /**
     * A textual occurrence.
     */ DocumentHighlightKind.Text = 1;
    /**
     * Read-access of a symbol, like reading a variable.
     */ DocumentHighlightKind.Read = 2;
    /**
     * Write-access of a symbol, like writing to a variable.
     */ DocumentHighlightKind.Write = 3;
})(DocumentHighlightKind || (DocumentHighlightKind = {}));
var DocumentHighlight;
(function(DocumentHighlight) {
    /**
     * Create a DocumentHighlight object.
     * @param range The range the highlight applies to.
     * @param kind The highlight kind
     */ function create(range, kind) {
        let result = {
            range
        };
        if (Is.number(kind)) {
            result.kind = kind;
        }
        return result;
    }
    DocumentHighlight.create = create;
})(DocumentHighlight || (DocumentHighlight = {}));
var SymbolKind;
(function(SymbolKind) {
    SymbolKind.File = 1;
    SymbolKind.Module = 2;
    SymbolKind.Namespace = 3;
    SymbolKind.Package = 4;
    SymbolKind.Class = 5;
    SymbolKind.Method = 6;
    SymbolKind.Property = 7;
    SymbolKind.Field = 8;
    SymbolKind.Constructor = 9;
    SymbolKind.Enum = 10;
    SymbolKind.Interface = 11;
    SymbolKind.Function = 12;
    SymbolKind.Variable = 13;
    SymbolKind.Constant = 14;
    SymbolKind.String = 15;
    SymbolKind.Number = 16;
    SymbolKind.Boolean = 17;
    SymbolKind.Array = 18;
    SymbolKind.Object = 19;
    SymbolKind.Key = 20;
    SymbolKind.Null = 21;
    SymbolKind.EnumMember = 22;
    SymbolKind.Struct = 23;
    SymbolKind.Event = 24;
    SymbolKind.Operator = 25;
    SymbolKind.TypeParameter = 26;
})(SymbolKind || (SymbolKind = {}));
var SymbolTag;
(function(SymbolTag) {
    /**
     * Render a symbol as obsolete, usually using a strike-out.
     */ SymbolTag.Deprecated = 1;
})(SymbolTag || (SymbolTag = {}));
var SymbolInformation;
(function(SymbolInformation) {
    /**
     * Creates a new symbol information literal.
     *
     * @param name The name of the symbol.
     * @param kind The kind of the symbol.
     * @param range The range of the location of the symbol.
     * @param uri The resource of the location of symbol.
     * @param containerName The name of the symbol containing the symbol.
     */ function create(name, kind, range, uri, containerName) {
        let result = {
            name,
            kind,
            location: {
                uri,
                range
            }
        };
        if (containerName) {
            result.containerName = containerName;
        }
        return result;
    }
    SymbolInformation.create = create;
})(SymbolInformation || (SymbolInformation = {}));
var WorkspaceSymbol;
(function(WorkspaceSymbol) {
    /**
     * Create a new workspace symbol.
     *
     * @param name The name of the symbol.
     * @param kind The kind of the symbol.
     * @param uri The resource of the location of the symbol.
     * @param range An options range of the location.
     * @returns A WorkspaceSymbol.
     */ function create(name, kind, uri, range) {
        return range !== undefined ? {
            name,
            kind,
            location: {
                uri,
                range
            }
        } : {
            name,
            kind,
            location: {
                uri
            }
        };
    }
    WorkspaceSymbol.create = create;
})(WorkspaceSymbol || (WorkspaceSymbol = {}));
var DocumentSymbol;
(function(DocumentSymbol) {
    /**
     * Creates a new symbol information literal.
     *
     * @param name The name of the symbol.
     * @param detail The detail of the symbol.
     * @param kind The kind of the symbol.
     * @param range The range of the symbol.
     * @param selectionRange The selectionRange of the symbol.
     * @param children Children of the symbol.
     */ function create(name, detail, kind, range, selectionRange, children) {
        let result = {
            name,
            detail,
            kind,
            range,
            selectionRange
        };
        if (children !== undefined) {
            result.children = children;
        }
        return result;
    }
    DocumentSymbol.create = create;
    /**
     * Checks whether the given literal conforms to the {@link DocumentSymbol} interface.
     */ function is(value) {
        let candidate = value;
        return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === undefined || Is.string(candidate.detail)) && (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) && (candidate.children === undefined || Array.isArray(candidate.children)) && (candidate.tags === undefined || Array.isArray(candidate.tags));
    }
    DocumentSymbol.is = is;
})(DocumentSymbol || (DocumentSymbol = {}));
var CodeActionKind;
(function(CodeActionKind) {
    /**
     * Empty kind.
     */ CodeActionKind.Empty = '';
    /**
     * Base kind for quickfix actions: 'quickfix'
     */ CodeActionKind.QuickFix = 'quickfix';
    /**
     * Base kind for refactoring actions: 'refactor'
     */ CodeActionKind.Refactor = 'refactor';
    /**
     * Base kind for refactoring extraction actions: 'refactor.extract'
     *
     * Example extract actions:
     *
     * - Extract method
     * - Extract function
     * - Extract variable
     * - Extract interface from class
     * - ...
     */ CodeActionKind.RefactorExtract = 'refactor.extract';
    /**
     * Base kind for refactoring inline actions: 'refactor.inline'
     *
     * Example inline actions:
     *
     * - Inline function
     * - Inline variable
     * - Inline constant
     * - ...
     */ CodeActionKind.RefactorInline = 'refactor.inline';
    /**
     * Base kind for refactoring rewrite actions: 'refactor.rewrite'
     *
     * Example rewrite actions:
     *
     * - Convert JavaScript function to class
     * - Add or remove parameter
     * - Encapsulate field
     * - Make method static
     * - Move method to base class
     * - ...
     */ CodeActionKind.RefactorRewrite = 'refactor.rewrite';
    /**
     * Base kind for source actions: `source`
     *
     * Source code actions apply to the entire file.
     */ CodeActionKind.Source = 'source';
    /**
     * Base kind for an organize imports source action: `source.organizeImports`
     */ CodeActionKind.SourceOrganizeImports = 'source.organizeImports';
    /**
     * Base kind for auto-fix source actions: `source.fixAll`.
     *
     * Fix all actions automatically fix errors that have a clear fix that do not require user input.
     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.
     *
     * @since 3.15.0
     */ CodeActionKind.SourceFixAll = 'source.fixAll';
})(CodeActionKind || (CodeActionKind = {}));
var CodeActionTriggerKind;
(function(CodeActionTriggerKind) {
    /**
     * Code actions were explicitly requested by the user or by an extension.
     */ CodeActionTriggerKind.Invoked = 1;
    /**
     * Code actions were requested automatically.
     *
     * This typically happens when current selection in a file changes, but can
     * also be triggered when file content changes.
     */ CodeActionTriggerKind.Automatic = 2;
})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));
var CodeActionContext;
(function(CodeActionContext) {
    /**
     * Creates a new CodeActionContext literal.
     */ function create(diagnostics, only, triggerKind) {
        let result = {
            diagnostics
        };
        if (only !== undefined && only !== null) {
            result.only = only;
        }
        if (triggerKind !== undefined && triggerKind !== null) {
            result.triggerKind = triggerKind;
        }
        return result;
    }
    CodeActionContext.create = create;
    /**
     * Checks whether the given literal conforms to the {@link CodeActionContext} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);
    }
    CodeActionContext.is = is;
})(CodeActionContext || (CodeActionContext = {}));
var CodeAction;
(function(CodeAction) {
    function create(title, kindOrCommandOrEdit, kind) {
        let result = {
            title
        };
        let checkKind = true;
        if (typeof kindOrCommandOrEdit === 'string') {
            checkKind = false;
            result.kind = kindOrCommandOrEdit;
        } else if (Command.is(kindOrCommandOrEdit)) {
            result.command = kindOrCommandOrEdit;
        } else {
            result.edit = kindOrCommandOrEdit;
        }
        if (checkKind && kind !== undefined) {
            result.kind = kind;
        }
        return result;
    }
    CodeAction.create = create;
    function is(value) {
        let candidate = value;
        return candidate && Is.string(candidate.title) && (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === undefined || Is.string(candidate.kind)) && (candidate.edit !== undefined || candidate.command !== undefined) && (candidate.command === undefined || Command.is(candidate.command)) && (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) && (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));
    }
    CodeAction.is = is;
})(CodeAction || (CodeAction = {}));
var CodeLens;
(function(CodeLens) {
    /**
     * Creates a new CodeLens literal.
     */ function create(range, data) {
        let result = {
            range
        };
        if (Is.defined(data)) {
            result.data = data;
        }
        return result;
    }
    CodeLens.create = create;
    /**
     * Checks whether the given literal conforms to the {@link CodeLens} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));
    }
    CodeLens.is = is;
})(CodeLens || (CodeLens = {}));
var FormattingOptions;
(function(FormattingOptions) {
    /**
     * Creates a new FormattingOptions literal.
     */ function create(tabSize, insertSpaces) {
        return {
            tabSize,
            insertSpaces
        };
    }
    FormattingOptions.create = create;
    /**
     * Checks whether the given literal conforms to the {@link FormattingOptions} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);
    }
    FormattingOptions.is = is;
})(FormattingOptions || (FormattingOptions = {}));
var DocumentLink;
(function(DocumentLink) {
    /**
     * Creates a new DocumentLink literal.
     */ function create(range, target, data) {
        return {
            range,
            target,
            data
        };
    }
    DocumentLink.create = create;
    /**
     * Checks whether the given literal conforms to the {@link DocumentLink} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));
    }
    DocumentLink.is = is;
})(DocumentLink || (DocumentLink = {}));
var SelectionRange;
(function(SelectionRange) {
    /**
     * Creates a new SelectionRange
     * @param range the range.
     * @param parent an optional parent.
     */ function create(range, parent) {
        return {
            range,
            parent
        };
    }
    SelectionRange.create = create;
    function is(value) {
        let candidate = value;
        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));
    }
    SelectionRange.is = is;
})(SelectionRange || (SelectionRange = {}));
var SemanticTokenTypes;
(function(SemanticTokenTypes) {
    SemanticTokenTypes["namespace"] = "namespace";
    /**
     * Represents a generic type. Acts as a fallback for types which can't be mapped to
     * a specific type like class or enum.
     */ SemanticTokenTypes["type"] = "type";
    SemanticTokenTypes["class"] = "class";
    SemanticTokenTypes["enum"] = "enum";
    SemanticTokenTypes["interface"] = "interface";
    SemanticTokenTypes["struct"] = "struct";
    SemanticTokenTypes["typeParameter"] = "typeParameter";
    SemanticTokenTypes["parameter"] = "parameter";
    SemanticTokenTypes["variable"] = "variable";
    SemanticTokenTypes["property"] = "property";
    SemanticTokenTypes["enumMember"] = "enumMember";
    SemanticTokenTypes["event"] = "event";
    SemanticTokenTypes["function"] = "function";
    SemanticTokenTypes["method"] = "method";
    SemanticTokenTypes["macro"] = "macro";
    SemanticTokenTypes["keyword"] = "keyword";
    SemanticTokenTypes["modifier"] = "modifier";
    SemanticTokenTypes["comment"] = "comment";
    SemanticTokenTypes["string"] = "string";
    SemanticTokenTypes["number"] = "number";
    SemanticTokenTypes["regexp"] = "regexp";
    SemanticTokenTypes["operator"] = "operator";
    /**
     * @since 3.17.0
     */ SemanticTokenTypes["decorator"] = "decorator";
})(SemanticTokenTypes || (SemanticTokenTypes = {}));
var SemanticTokenModifiers;
(function(SemanticTokenModifiers) {
    SemanticTokenModifiers["declaration"] = "declaration";
    SemanticTokenModifiers["definition"] = "definition";
    SemanticTokenModifiers["readonly"] = "readonly";
    SemanticTokenModifiers["static"] = "static";
    SemanticTokenModifiers["deprecated"] = "deprecated";
    SemanticTokenModifiers["abstract"] = "abstract";
    SemanticTokenModifiers["async"] = "async";
    SemanticTokenModifiers["modification"] = "modification";
    SemanticTokenModifiers["documentation"] = "documentation";
    SemanticTokenModifiers["defaultLibrary"] = "defaultLibrary";
})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));
var SemanticTokens;
(function(SemanticTokens) {
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');
    }
    SemanticTokens.is = is;
})(SemanticTokens || (SemanticTokens = {}));
var InlineValueText;
(function(InlineValueText) {
    /**
     * Creates a new InlineValueText literal.
     */ function create(range, text) {
        return {
            range,
            text
        };
    }
    InlineValueText.create = create;
    function is(value) {
        const candidate = value;
        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);
    }
    InlineValueText.is = is;
})(InlineValueText || (InlineValueText = {}));
var InlineValueVariableLookup;
(function(InlineValueVariableLookup) {
    /**
     * Creates a new InlineValueText literal.
     */ function create(range, variableName, caseSensitiveLookup) {
        return {
            range,
            variableName,
            caseSensitiveLookup
        };
    }
    InlineValueVariableLookup.create = create;
    function is(value) {
        const candidate = value;
        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === undefined);
    }
    InlineValueVariableLookup.is = is;
})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));
var InlineValueEvaluatableExpression;
(function(InlineValueEvaluatableExpression) {
    /**
     * Creates a new InlineValueEvaluatableExpression literal.
     */ function create(range, expression) {
        return {
            range,
            expression
        };
    }
    InlineValueEvaluatableExpression.create = create;
    function is(value) {
        const candidate = value;
        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === undefined);
    }
    InlineValueEvaluatableExpression.is = is;
})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));
var InlineValueContext;
(function(InlineValueContext) {
    /**
     * Creates a new InlineValueContext literal.
     */ function create(frameId, stoppedLocation) {
        return {
            frameId,
            stoppedLocation
        };
    }
    InlineValueContext.create = create;
    /**
     * Checks whether the given literal conforms to the {@link InlineValueContext} interface.
     */ function is(value) {
        const candidate = value;
        return Is.defined(candidate) && Range.is(value.stoppedLocation);
    }
    InlineValueContext.is = is;
})(InlineValueContext || (InlineValueContext = {}));
var InlayHintKind;
(function(InlayHintKind) {
    /**
     * An inlay hint that for a type annotation.
     */ InlayHintKind.Type = 1;
    /**
     * An inlay hint that is for a parameter.
     */ InlayHintKind.Parameter = 2;
    function is(value) {
        return value === 1 || value === 2;
    }
    InlayHintKind.is = is;
})(InlayHintKind || (InlayHintKind = {}));
var InlayHintLabelPart;
(function(InlayHintLabelPart) {
    function create(value) {
        return {
            value
        };
    }
    InlayHintLabelPart.create = create;
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === undefined || Location.is(candidate.location)) && (candidate.command === undefined || Command.is(candidate.command));
    }
    InlayHintLabelPart.is = is;
})(InlayHintLabelPart || (InlayHintLabelPart = {}));
var InlayHint;
(function(InlayHint) {
    function create(position, label, kind) {
        const result = {
            position,
            label
        };
        if (kind !== undefined) {
            result.kind = kind;
        }
        return result;
    }
    InlayHint.create = create;
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === undefined || InlayHintKind.is(candidate.kind)) && candidate.textEdits === undefined || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));
    }
    InlayHint.is = is;
})(InlayHint || (InlayHint = {}));
var StringValue;
(function(StringValue) {
    function createSnippet(value) {
        return {
            kind: 'snippet',
            value
        };
    }
    StringValue.createSnippet = createSnippet;
})(StringValue || (StringValue = {}));
var InlineCompletionItem;
(function(InlineCompletionItem) {
    function create(insertText, filterText, range, command) {
        return {
            insertText,
            filterText,
            range,
            command
        };
    }
    InlineCompletionItem.create = create;
})(InlineCompletionItem || (InlineCompletionItem = {}));
var InlineCompletionList;
(function(InlineCompletionList) {
    function create(items) {
        return {
            items
        };
    }
    InlineCompletionList.create = create;
})(InlineCompletionList || (InlineCompletionList = {}));
var InlineCompletionTriggerKind;
(function(InlineCompletionTriggerKind) {
    /**
     * Completion was triggered explicitly by a user gesture.
     */ InlineCompletionTriggerKind.Invoked = 0;
    /**
     * Completion was triggered automatically while editing.
     */ InlineCompletionTriggerKind.Automatic = 1;
})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));
var SelectedCompletionInfo;
(function(SelectedCompletionInfo) {
    function create(range, text) {
        return {
            range,
            text
        };
    }
    SelectedCompletionInfo.create = create;
})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));
var InlineCompletionContext;
(function(InlineCompletionContext) {
    function create(triggerKind, selectedCompletionInfo) {
        return {
            triggerKind,
            selectedCompletionInfo
        };
    }
    InlineCompletionContext.create = create;
})(InlineCompletionContext || (InlineCompletionContext = {}));
var WorkspaceFolder;
(function(WorkspaceFolder) {
    function is(value) {
        const candidate = value;
        return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);
    }
    WorkspaceFolder.is = is;
})(WorkspaceFolder || (WorkspaceFolder = {}));
const EOL = [
    '\n',
    '\r\n',
    '\r'
];
var TextDocument;
(function(TextDocument) {
    /**
     * Creates a new ITextDocument literal from the given uri and content.
     * @param uri The document's uri.
     * @param languageId The document's language Id.
     * @param version The document's version.
     * @param content The document's content.
     */ function create(uri, languageId, version, content) {
        return new FullTextDocument(uri, languageId, version, content);
    }
    TextDocument.create = create;
    /**
     * Checks whether the given literal conforms to the {@link ITextDocument} interface.
     */ function is(value) {
        let candidate = value;
        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;
    }
    TextDocument.is = is;
    function applyEdits(document, edits) {
        let text = document.getText();
        let sortedEdits = mergeSort(edits, (a, b)=>{
            let diff = a.range.start.line - b.range.start.line;
            if (diff === 0) {
                return a.range.start.character - b.range.start.character;
            }
            return diff;
        });
        let lastModifiedOffset = text.length;
        for(let i = sortedEdits.length - 1; i >= 0; i--){
            let e = sortedEdits[i];
            let startOffset = document.offsetAt(e.range.start);
            let endOffset = document.offsetAt(e.range.end);
            if (endOffset <= lastModifiedOffset) {
                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);
            } else {
                throw new Error('Overlapping edit');
            }
            lastModifiedOffset = startOffset;
        }
        return text;
    }
    TextDocument.applyEdits = applyEdits;
    function mergeSort(data, compare) {
        if (data.length <= 1) {
            // sorted
            return data;
        }
        const p = data.length / 2 | 0;
        const left = data.slice(0, p);
        const right = data.slice(p);
        mergeSort(left, compare);
        mergeSort(right, compare);
        let leftIdx = 0;
        let rightIdx = 0;
        let i = 0;
        while(leftIdx < left.length && rightIdx < right.length){
            let ret = compare(left[leftIdx], right[rightIdx]);
            if (ret <= 0) {
                // smaller_equal -> take left to preserve order
                data[i++] = left[leftIdx++];
            } else {
                // greater -> take right
                data[i++] = right[rightIdx++];
            }
        }
        while(leftIdx < left.length){
            data[i++] = left[leftIdx++];
        }
        while(rightIdx < right.length){
            data[i++] = right[rightIdx++];
        }
        return data;
    }
})(TextDocument || (TextDocument = {}));
/**
 * @deprecated Use the text document from the new vscode-languageserver-textdocument package.
 */ class FullTextDocument {
    get uri() {
        return this._uri;
    }
    get languageId() {
        return this._languageId;
    }
    get version() {
        return this._version;
    }
    getText(range) {
        if (range) {
            let start = this.offsetAt(range.start);
            let end = this.offsetAt(range.end);
            return this._content.substring(start, end);
        }
        return this._content;
    }
    update(event, version) {
        this._content = event.text;
        this._version = version;
        this._lineOffsets = undefined;
    }
    getLineOffsets() {
        if (this._lineOffsets === undefined) {
            let lineOffsets = [];
            let text = this._content;
            let isLineStart = true;
            for(let i = 0; i < text.length; i++){
                if (isLineStart) {
                    lineOffsets.push(i);
                    isLineStart = false;
                }
                let ch = text.charAt(i);
                isLineStart = ch === '\r' || ch === '\n';
                if (ch === '\r' && i + 1 < text.length && text.charAt(i + 1) === '\n') {
                    i++;
                }
            }
            if (isLineStart && text.length > 0) {
                lineOffsets.push(text.length);
            }
            this._lineOffsets = lineOffsets;
        }
        return this._lineOffsets;
    }
    positionAt(offset) {
        offset = Math.max(Math.min(offset, this._content.length), 0);
        let lineOffsets = this.getLineOffsets();
        let low = 0, high = lineOffsets.length;
        if (high === 0) {
            return Position.create(0, offset);
        }
        while(low < high){
            let mid = Math.floor((low + high) / 2);
            if (lineOffsets[mid] > offset) {
                high = mid;
            } else {
                low = mid + 1;
            }
        }
        // low is the least x for which the line offset is larger than the current offset
        // or array.length if no line offset is larger than the current offset
        let line = low - 1;
        return Position.create(line, offset - lineOffsets[line]);
    }
    offsetAt(position) {
        let lineOffsets = this.getLineOffsets();
        if (position.line >= lineOffsets.length) {
            return this._content.length;
        } else if (position.line < 0) {
            return 0;
        }
        let lineOffset = lineOffsets[position.line];
        let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;
        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);
    }
    get lineCount() {
        return this.getLineOffsets().length;
    }
    constructor(uri, languageId, version, content){
        this._uri = uri;
        this._languageId = languageId;
        this._version = version;
        this._content = content;
        this._lineOffsets = undefined;
    }
}
var Is;
(function(Is) {
    const toString = Object.prototype.toString;
    function defined(value) {
        return typeof value !== 'undefined';
    }
    Is.defined = defined;
    function undefined1(value) {
        return typeof value === 'undefined';
    }
    Is.undefined = undefined1;
    function boolean(value) {
        return value === true || value === false;
    }
    Is.boolean = boolean;
    function string(value) {
        return toString.call(value) === '[object String]';
    }
    Is.string = string;
    function number(value) {
        return toString.call(value) === '[object Number]';
    }
    Is.number = number;
    function numberRange(value, min, max) {
        return toString.call(value) === '[object Number]' && min <= value && value <= max;
    }
    Is.numberRange = numberRange;
    function integer(value) {
        return toString.call(value) === '[object Number]' && -2147483648 <= value && value <= 2147483647;
    }
    Is.integer = integer;
    function uinteger(value) {
        return toString.call(value) === '[object Number]' && 0 <= value && value <= 2147483647;
    }
    Is.uinteger = uinteger;
    function func(value) {
        return toString.call(value) === '[object Function]';
    }
    Is.func = func;
    function objectLiteral(value) {
        // Strictly speaking class instances pass this check as well. Since the LSP
        // doesn't use classes we ignore this for now. If we do we need to add something
        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`
        return value !== null && typeof value === 'object';
    }
    Is.objectLiteral = objectLiteral;
    function typedArray(value, check) {
        return Array.isArray(value) && value.every(check);
    }
    Is.typedArray = typedArray;
})(Is || (Is = {}));
}),
"[project]/node_modules/vscode-jsonrpc/lib/common/ral.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */ Object.defineProperty(exports, "__esModule", {
    value: true
});
let _ral;
function RAL() {
    if (_ral === undefined) {
        throw new Error("No runtime abstraction layer installed");
    }
    return _ral;
}
(function(RAL) {
    function install(ral) {
        if (ral === undefined) {
            throw new Error("No runtime abstraction layer provided");
        }
        _ral = ral;
    }
    RAL.install = install;
})(RAL || (RAL = {}));
exports.default = RAL;
}}),
"[project]/node_modules/vscode-jsonrpc/lib/common/is.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.stringArray = exports.array = exports.func = exports.error = exports.number = exports.string = exports.boolean = void 0;
function boolean(value) {
    return value === true || value === false;
}
exports.boolean = boolean;
function string(value) {
    return typeof value === 'string' || value instanceof String;
}
exports.string = string;
function number(value) {
    return typeof value === 'number' || value instanceof Number;
}
exports.number = number;
function error(value) {
    return value instanceof Error;
}
exports.error = error;
function func(value) {
    return typeof value === 'function';
}
exports.func = func;
function array(value) {
    return Array.isArray(value);
}
exports.array = array;
function stringArray(value) {
    return array(value) && value.every((elem)=>string(elem));
}
exports.stringArray = stringArray;
}}),
"[project]/node_modules/vscode-jsonrpc/lib/common/events.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.Emitter = exports.Event = void 0;
const ral_1 = __turbopack_context__.r("[project]/node_modules/vscode-jsonrpc/lib/common/ral.js [app-client] (ecmascript)");
var Event;
(function(Event) {
    const _disposable = {
        dispose () {}
    };
    Event.None = function() {
        return _disposable;
    };
})(Event || (exports.Event = Event = {}));
class CallbackList {
    add(callback) {
        let context = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null, bucket = arguments.length > 2 ? arguments[2] : void 0;
        if (!this._callbacks) {
            this._callbacks = [];
            this._contexts = [];
        }
        this._callbacks.push(callback);
        this._contexts.push(context);
        if (Array.isArray(bucket)) {
            bucket.push({
                dispose: ()=>this.remove(callback, context)
            });
        }
    }
    remove(callback) {
        let context = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;
        if (!this._callbacks) {
            return;
        }
        let foundCallbackWithDifferentContext = false;
        for(let i = 0, len = this._callbacks.length; i < len; i++){
            if (this._callbacks[i] === callback) {
                if (this._contexts[i] === context) {
                    // callback & context match => remove it
                    this._callbacks.splice(i, 1);
                    this._contexts.splice(i, 1);
                    return;
                } else {
                    foundCallbackWithDifferentContext = true;
                }
            }
        }
        if (foundCallbackWithDifferentContext) {
            throw new Error('When adding a listener with a context, you should remove it with the same context');
        }
    }
    invoke() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        if (!this._callbacks) {
            return [];
        }
        const ret = [], callbacks = this._callbacks.slice(0), contexts = this._contexts.slice(0);
        for(let i = 0, len = callbacks.length; i < len; i++){
            try {
                ret.push(callbacks[i].apply(contexts[i], args));
            } catch (e) {
                // eslint-disable-next-line no-console
                (0, ral_1.default)().console.error(e);
            }
        }
        return ret;
    }
    isEmpty() {
        return !this._callbacks || this._callbacks.length === 0;
    }
    dispose() {
        this._callbacks = undefined;
        this._contexts = undefined;
    }
}
class Emitter {
    /**
     * For the public to allow to subscribe
     * to events from this Emitter
     */ get event() {
        if (!this._event) {
            this._event = (listener, thisArgs, disposables)=>{
                if (!this._callbacks) {
                    this._callbacks = new CallbackList();
                }
                if (this._options && this._options.onFirstListenerAdd && this._callbacks.isEmpty()) {
                    this._options.onFirstListenerAdd(this);
                }
                this._callbacks.add(listener, thisArgs);
                const result = {
                    dispose: ()=>{
                        if (!this._callbacks) {
                            // disposable is disposed after emitter is disposed.
                            return;
                        }
                        this._callbacks.remove(listener, thisArgs);
                        result.dispose = Emitter._noop;
                        if (this._options && this._options.onLastListenerRemove && this._callbacks.isEmpty()) {
                            this._options.onLastListenerRemove(this);
                        }
                    }
                };
                if (Array.isArray(disposables)) {
                    disposables.push(result);
                }
                return result;
            };
        }
        return this._event;
    }
    /**
     * To be kept private to fire an event to
     * subscribers
     */ fire(event) {
        if (this._callbacks) {
            this._callbacks.invoke.call(this._callbacks, event);
        }
    }
    dispose() {
        if (this._callbacks) {
            this._callbacks.dispose();
            this._callbacks = undefined;
        }
    }
    constructor(_options){
        this._options = _options;
    }
}
exports.Emitter = Emitter;
Emitter._noop = function() {};
}}),
"[project]/node_modules/vscode-jsonrpc/lib/common/cancellation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.CancellationTokenSource = exports.CancellationToken = void 0;
const ral_1 = __turbopack_context__.r("[project]/node_modules/vscode-jsonrpc/lib/common/ral.js [app-client] (ecmascript)");
const Is = __turbopack_context__.r("[project]/node_modules/vscode-jsonrpc/lib/common/is.js [app-client] (ecmascript)");
const events_1 = __turbopack_context__.r("[project]/node_modules/vscode-jsonrpc/lib/common/events.js [app-client] (ecmascript)");
var CancellationToken;
(function(CancellationToken) {
    CancellationToken.None = Object.freeze({
        isCancellationRequested: false,
        onCancellationRequested: events_1.Event.None
    });
    CancellationToken.Cancelled = Object.freeze({
        isCancellationRequested: true,
        onCancellationRequested: events_1.Event.None
    });
    function is(value) {
        const candidate = value;
        return candidate && (candidate === CancellationToken.None || candidate === CancellationToken.Cancelled || Is.boolean(candidate.isCancellationRequested) && !!candidate.onCancellationRequested);
    }
    CancellationToken.is = is;
})(CancellationToken || (exports.CancellationToken = CancellationToken = {}));
const shortcutEvent = Object.freeze(function(callback, context) {
    const handle = (0, ral_1.default)().timer.setTimeout(callback.bind(context), 0);
    return {
        dispose () {
            handle.dispose();
        }
    };
});
class MutableToken {
    cancel() {
        if (!this._isCancelled) {
            this._isCancelled = true;
            if (this._emitter) {
                this._emitter.fire(undefined);
                this.dispose();
            }
        }
    }
    get isCancellationRequested() {
        return this._isCancelled;
    }
    get onCancellationRequested() {
        if (this._isCancelled) {
            return shortcutEvent;
        }
        if (!this._emitter) {
            this._emitter = new events_1.Emitter();
        }
        return this._emitter.event;
    }
    dispose() {
        if (this._emitter) {
            this._emitter.dispose();
            this._emitter = undefined;
        }
    }
    constructor(){
        this._isCancelled = false;
    }
}
class CancellationTokenSource {
    get token() {
        if (!this._token) {
            // be lazy and create the token only when
            // actually needed
            this._token = new MutableToken();
        }
        return this._token;
    }
    cancel() {
        if (!this._token) {
            // save an object by returning the default
            // cancelled token when cancellation happens
            // before someone asks for the token
            this._token = CancellationToken.Cancelled;
        } else {
            this._token.cancel();
        }
    }
    dispose() {
        if (!this._token) {
            // ensure to initialize with an empty token if we had none
            this._token = CancellationToken.None;
        } else if (this._token instanceof MutableToken) {
            // actually dispose
            this._token.dispose();
        }
    }
}
exports.CancellationTokenSource = CancellationTokenSource;
}}),
"[project]/node_modules/vscode-languageserver-textdocument/lib/esm/main.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */ __turbopack_context__.s({
    "TextDocument": ()=>TextDocument
});
'use strict';
class FullTextDocument {
    get uri() {
        return this._uri;
    }
    get languageId() {
        return this._languageId;
    }
    get version() {
        return this._version;
    }
    getText(range) {
        if (range) {
            const start = this.offsetAt(range.start);
            const end = this.offsetAt(range.end);
            return this._content.substring(start, end);
        }
        return this._content;
    }
    update(changes, version) {
        for (const change of changes){
            if (FullTextDocument.isIncremental(change)) {
                // makes sure start is before end
                const range = getWellformedRange(change.range);
                // update content
                const startOffset = this.offsetAt(range.start);
                const endOffset = this.offsetAt(range.end);
                this._content = this._content.substring(0, startOffset) + change.text + this._content.substring(endOffset, this._content.length);
                // update the offsets
                const startLine = Math.max(range.start.line, 0);
                const endLine = Math.max(range.end.line, 0);
                let lineOffsets = this._lineOffsets;
                const addedLineOffsets = computeLineOffsets(change.text, false, startOffset);
                if (endLine - startLine === addedLineOffsets.length) {
                    for(let i = 0, len = addedLineOffsets.length; i < len; i++){
                        lineOffsets[i + startLine + 1] = addedLineOffsets[i];
                    }
                } else {
                    if (addedLineOffsets.length < 10000) {
                        lineOffsets.splice(startLine + 1, endLine - startLine, ...addedLineOffsets);
                    } else {
                        this._lineOffsets = lineOffsets = lineOffsets.slice(0, startLine + 1).concat(addedLineOffsets, lineOffsets.slice(endLine + 1));
                    }
                }
                const diff = change.text.length - (endOffset - startOffset);
                if (diff !== 0) {
                    for(let i = startLine + 1 + addedLineOffsets.length, len = lineOffsets.length; i < len; i++){
                        lineOffsets[i] = lineOffsets[i] + diff;
                    }
                }
            } else if (FullTextDocument.isFull(change)) {
                this._content = change.text;
                this._lineOffsets = undefined;
            } else {
                throw new Error('Unknown change event received');
            }
        }
        this._version = version;
    }
    getLineOffsets() {
        if (this._lineOffsets === undefined) {
            this._lineOffsets = computeLineOffsets(this._content, true);
        }
        return this._lineOffsets;
    }
    positionAt(offset) {
        offset = Math.max(Math.min(offset, this._content.length), 0);
        const lineOffsets = this.getLineOffsets();
        let low = 0, high = lineOffsets.length;
        if (high === 0) {
            return {
                line: 0,
                character: offset
            };
        }
        while(low < high){
            const mid = Math.floor((low + high) / 2);
            if (lineOffsets[mid] > offset) {
                high = mid;
            } else {
                low = mid + 1;
            }
        }
        // low is the least x for which the line offset is larger than the current offset
        // or array.length if no line offset is larger than the current offset
        const line = low - 1;
        offset = this.ensureBeforeEOL(offset, lineOffsets[line]);
        return {
            line,
            character: offset - lineOffsets[line]
        };
    }
    offsetAt(position) {
        const lineOffsets = this.getLineOffsets();
        if (position.line >= lineOffsets.length) {
            return this._content.length;
        } else if (position.line < 0) {
            return 0;
        }
        const lineOffset = lineOffsets[position.line];
        if (position.character <= 0) {
            return lineOffset;
        }
        const nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;
        const offset = Math.min(lineOffset + position.character, nextLineOffset);
        return this.ensureBeforeEOL(offset, lineOffset);
    }
    ensureBeforeEOL(offset, lineOffset) {
        while(offset > lineOffset && isEOL(this._content.charCodeAt(offset - 1))){
            offset--;
        }
        return offset;
    }
    get lineCount() {
        return this.getLineOffsets().length;
    }
    static isIncremental(event) {
        const candidate = event;
        return candidate !== undefined && candidate !== null && typeof candidate.text === 'string' && candidate.range !== undefined && (candidate.rangeLength === undefined || typeof candidate.rangeLength === 'number');
    }
    static isFull(event) {
        const candidate = event;
        return candidate !== undefined && candidate !== null && typeof candidate.text === 'string' && candidate.range === undefined && candidate.rangeLength === undefined;
    }
    constructor(uri, languageId, version, content){
        this._uri = uri;
        this._languageId = languageId;
        this._version = version;
        this._content = content;
        this._lineOffsets = undefined;
    }
}
var TextDocument;
(function(TextDocument) {
    /**
     * Creates a new text document.
     *
     * @param uri The document's uri.
     * @param languageId  The document's language Id.
     * @param version The document's initial version number.
     * @param content The document's content.
     */ function create(uri, languageId, version, content) {
        return new FullTextDocument(uri, languageId, version, content);
    }
    TextDocument.create = create;
    /**
     * Updates a TextDocument by modifying its content.
     *
     * @param document the document to update. Only documents created by TextDocument.create are valid inputs.
     * @param changes the changes to apply to the document.
     * @param version the changes version for the document.
     * @returns The updated TextDocument. Note: That's the same document instance passed in as first parameter.
     *
     */ function update(document, changes, version) {
        if (document instanceof FullTextDocument) {
            document.update(changes, version);
            return document;
        } else {
            throw new Error('TextDocument.update: document must be created by TextDocument.create');
        }
    }
    TextDocument.update = update;
    function applyEdits(document, edits) {
        const text = document.getText();
        const sortedEdits = mergeSort(edits.map(getWellformedEdit), (a, b)=>{
            const diff = a.range.start.line - b.range.start.line;
            if (diff === 0) {
                return a.range.start.character - b.range.start.character;
            }
            return diff;
        });
        let lastModifiedOffset = 0;
        const spans = [];
        for (const e of sortedEdits){
            const startOffset = document.offsetAt(e.range.start);
            if (startOffset < lastModifiedOffset) {
                throw new Error('Overlapping edit');
            } else if (startOffset > lastModifiedOffset) {
                spans.push(text.substring(lastModifiedOffset, startOffset));
            }
            if (e.newText.length) {
                spans.push(e.newText);
            }
            lastModifiedOffset = document.offsetAt(e.range.end);
        }
        spans.push(text.substr(lastModifiedOffset));
        return spans.join('');
    }
    TextDocument.applyEdits = applyEdits;
})(TextDocument || (TextDocument = {}));
function mergeSort(data, compare) {
    if (data.length <= 1) {
        // sorted
        return data;
    }
    const p = data.length / 2 | 0;
    const left = data.slice(0, p);
    const right = data.slice(p);
    mergeSort(left, compare);
    mergeSort(right, compare);
    let leftIdx = 0;
    let rightIdx = 0;
    let i = 0;
    while(leftIdx < left.length && rightIdx < right.length){
        const ret = compare(left[leftIdx], right[rightIdx]);
        if (ret <= 0) {
            // smaller_equal -> take left to preserve order
            data[i++] = left[leftIdx++];
        } else {
            // greater -> take right
            data[i++] = right[rightIdx++];
        }
    }
    while(leftIdx < left.length){
        data[i++] = left[leftIdx++];
    }
    while(rightIdx < right.length){
        data[i++] = right[rightIdx++];
    }
    return data;
}
function computeLineOffsets(text, isAtLineStart) {
    let textOffset = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
    const result = isAtLineStart ? [
        textOffset
    ] : [];
    for(let i = 0; i < text.length; i++){
        const ch = text.charCodeAt(i);
        if (isEOL(ch)) {
            if (ch === 13 /* CharCode.CarriageReturn */  && i + 1 < text.length && text.charCodeAt(i + 1) === 10 /* CharCode.LineFeed */ ) {
                i++;
            }
            result.push(textOffset + i + 1);
        }
    }
    return result;
}
function isEOL(char) {
    return char === 13 /* CharCode.CarriageReturn */  || char === 10 /* CharCode.LineFeed */ ;
}
function getWellformedRange(range) {
    const start = range.start;
    const end = range.end;
    if (start.line > end.line || start.line === end.line && start.character > end.character) {
        return {
            start: end,
            end: start
        };
    }
    return range;
}
function getWellformedEdit(textEdit) {
    const range = getWellformedRange(textEdit.range);
    if (range !== textEdit.range) {
        return {
            newText: textEdit.newText,
            range
        };
    }
    return textEdit;
}
}),
"[project]/node_modules/vscode-uri/lib/esm/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "URI": ()=>URI,
    "Utils": ()=>Utils
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
var LIB;
(()=>{
    "use strict";
    var t = {
        470: (t)=>{
            function e(t) {
                if ("string" != typeof t) throw new TypeError("Path must be a string. Received " + JSON.stringify(t));
            }
            function r(t, e) {
                for(var r, n = "", i = 0, o = -1, s = 0, h = 0; h <= t.length; ++h){
                    if (h < t.length) r = t.charCodeAt(h);
                    else {
                        if (47 === r) break;
                        r = 47;
                    }
                    if (47 === r) {
                        if (o === h - 1 || 1 === s) ;
                        else if (o !== h - 1 && 2 === s) {
                            if (n.length < 2 || 2 !== i || 46 !== n.charCodeAt(n.length - 1) || 46 !== n.charCodeAt(n.length - 2)) {
                                if (n.length > 2) {
                                    var a = n.lastIndexOf("/");
                                    if (a !== n.length - 1) {
                                        -1 === a ? (n = "", i = 0) : i = (n = n.slice(0, a)).length - 1 - n.lastIndexOf("/"), o = h, s = 0;
                                        continue;
                                    }
                                } else if (2 === n.length || 1 === n.length) {
                                    n = "", i = 0, o = h, s = 0;
                                    continue;
                                }
                            }
                            e && (n.length > 0 ? n += "/.." : n = "..", i = 2);
                        } else n.length > 0 ? n += "/" + t.slice(o + 1, h) : n = t.slice(o + 1, h), i = h - o - 1;
                        o = h, s = 0;
                    } else 46 === r && -1 !== s ? ++s : s = -1;
                }
                return n;
            }
            var n = {
                resolve: function() {
                    for(var t, n = "", i = !1, o = arguments.length - 1; o >= -1 && !i; o--){
                        var s;
                        o >= 0 ? s = arguments[o] : (void 0 === t && (t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cwd()), s = t), e(s), 0 !== s.length && (n = s + "/" + n, i = 47 === s.charCodeAt(0));
                    }
                    return n = r(n, !i), i ? n.length > 0 ? "/" + n : "/" : n.length > 0 ? n : ".";
                },
                normalize: function(t) {
                    if (e(t), 0 === t.length) return ".";
                    var n = 47 === t.charCodeAt(0), i = 47 === t.charCodeAt(t.length - 1);
                    return 0 !== (t = r(t, !n)).length || n || (t = "."), t.length > 0 && i && (t += "/"), n ? "/" + t : t;
                },
                isAbsolute: function(t) {
                    return e(t), t.length > 0 && 47 === t.charCodeAt(0);
                },
                join: function() {
                    if (0 === arguments.length) return ".";
                    for(var t, r = 0; r < arguments.length; ++r){
                        var i = arguments[r];
                        e(i), i.length > 0 && (void 0 === t ? t = i : t += "/" + i);
                    }
                    return void 0 === t ? "." : n.normalize(t);
                },
                relative: function(t, r) {
                    if (e(t), e(r), t === r) return "";
                    if ((t = n.resolve(t)) === (r = n.resolve(r))) return "";
                    for(var i = 1; i < t.length && 47 === t.charCodeAt(i); ++i);
                    for(var o = t.length, s = o - i, h = 1; h < r.length && 47 === r.charCodeAt(h); ++h);
                    for(var a = r.length - h, c = s < a ? s : a, f = -1, u = 0; u <= c; ++u){
                        if (u === c) {
                            if (a > c) {
                                if (47 === r.charCodeAt(h + u)) return r.slice(h + u + 1);
                                if (0 === u) return r.slice(h + u);
                            } else s > c && (47 === t.charCodeAt(i + u) ? f = u : 0 === u && (f = 0));
                            break;
                        }
                        var l = t.charCodeAt(i + u);
                        if (l !== r.charCodeAt(h + u)) break;
                        47 === l && (f = u);
                    }
                    var g = "";
                    for(u = i + f + 1; u <= o; ++u)u !== o && 47 !== t.charCodeAt(u) || (0 === g.length ? g += ".." : g += "/..");
                    return g.length > 0 ? g + r.slice(h + f) : (h += f, 47 === r.charCodeAt(h) && ++h, r.slice(h));
                },
                _makeLong: function(t) {
                    return t;
                },
                dirname: function(t) {
                    if (e(t), 0 === t.length) return ".";
                    for(var r = t.charCodeAt(0), n = 47 === r, i = -1, o = !0, s = t.length - 1; s >= 1; --s)if (47 === (r = t.charCodeAt(s))) {
                        if (!o) {
                            i = s;
                            break;
                        }
                    } else o = !1;
                    return -1 === i ? n ? "/" : "." : n && 1 === i ? "//" : t.slice(0, i);
                },
                basename: function(t, r) {
                    if (void 0 !== r && "string" != typeof r) throw new TypeError('"ext" argument must be a string');
                    e(t);
                    var n, i = 0, o = -1, s = !0;
                    if (void 0 !== r && r.length > 0 && r.length <= t.length) {
                        if (r.length === t.length && r === t) return "";
                        var h = r.length - 1, a = -1;
                        for(n = t.length - 1; n >= 0; --n){
                            var c = t.charCodeAt(n);
                            if (47 === c) {
                                if (!s) {
                                    i = n + 1;
                                    break;
                                }
                            } else -1 === a && (s = !1, a = n + 1), h >= 0 && (c === r.charCodeAt(h) ? -1 == --h && (o = n) : (h = -1, o = a));
                        }
                        return i === o ? o = a : -1 === o && (o = t.length), t.slice(i, o);
                    }
                    for(n = t.length - 1; n >= 0; --n)if (47 === t.charCodeAt(n)) {
                        if (!s) {
                            i = n + 1;
                            break;
                        }
                    } else -1 === o && (s = !1, o = n + 1);
                    return -1 === o ? "" : t.slice(i, o);
                },
                extname: function(t) {
                    e(t);
                    for(var r = -1, n = 0, i = -1, o = !0, s = 0, h = t.length - 1; h >= 0; --h){
                        var a = t.charCodeAt(h);
                        if (47 !== a) -1 === i && (o = !1, i = h + 1), 46 === a ? -1 === r ? r = h : 1 !== s && (s = 1) : -1 !== r && (s = -1);
                        else if (!o) {
                            n = h + 1;
                            break;
                        }
                    }
                    return -1 === r || -1 === i || 0 === s || 1 === s && r === i - 1 && r === n + 1 ? "" : t.slice(r, i);
                },
                format: function(t) {
                    if (null === t || "object" != typeof t) throw new TypeError('The "pathObject" argument must be of type Object. Received type ' + typeof t);
                    return function(t, e) {
                        var r = e.dir || e.root, n = e.base || (e.name || "") + (e.ext || "");
                        return r ? r === e.root ? r + n : r + "/" + n : n;
                    }(0, t);
                },
                parse: function(t) {
                    e(t);
                    var r = {
                        root: "",
                        dir: "",
                        base: "",
                        ext: "",
                        name: ""
                    };
                    if (0 === t.length) return r;
                    var n, i = t.charCodeAt(0), o = 47 === i;
                    o ? (r.root = "/", n = 1) : n = 0;
                    for(var s = -1, h = 0, a = -1, c = !0, f = t.length - 1, u = 0; f >= n; --f)if (47 !== (i = t.charCodeAt(f))) -1 === a && (c = !1, a = f + 1), 46 === i ? -1 === s ? s = f : 1 !== u && (u = 1) : -1 !== s && (u = -1);
                    else if (!c) {
                        h = f + 1;
                        break;
                    }
                    return -1 === s || -1 === a || 0 === u || 1 === u && s === a - 1 && s === h + 1 ? -1 !== a && (r.base = r.name = 0 === h && o ? t.slice(1, a) : t.slice(h, a)) : (0 === h && o ? (r.name = t.slice(1, s), r.base = t.slice(1, a)) : (r.name = t.slice(h, s), r.base = t.slice(h, a)), r.ext = t.slice(s, a)), h > 0 ? r.dir = t.slice(0, h - 1) : o && (r.dir = "/"), r;
                },
                sep: "/",
                delimiter: ":",
                win32: null,
                posix: null
            };
            n.posix = n, t.exports = n;
        }
    }, e = {};
    function r(n) {
        var i = e[n];
        if (void 0 !== i) return i.exports;
        var o = e[n] = {
            exports: {}
        };
        return t[n](o, o.exports, r), o.exports;
    }
    r.d = (t, e)=>{
        for(var n in e)r.o(e, n) && !r.o(t, n) && Object.defineProperty(t, n, {
            enumerable: !0,
            get: e[n]
        });
    }, r.o = (t, e)=>Object.prototype.hasOwnProperty.call(t, e), r.r = (t)=>{
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {
            value: "Module"
        }), Object.defineProperty(t, "__esModule", {
            value: !0
        });
    };
    var n = {};
    (()=>{
        let t;
        if (r.r(n), r.d(n, {
            URI: ()=>f,
            Utils: ()=>P
        }), "object" == typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]) t = "win32" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].platform;
        else if ("object" == typeof navigator) {
            let e = navigator.userAgent;
            t = e.indexOf("Windows") >= 0;
        }
        const e = /^\w[\w\d+.-]*$/, i = /^\//, o = /^\/\//;
        function s(t, r) {
            if (!t.scheme && r) throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'.concat(t.authority, '", path: "').concat(t.path, '", query: "').concat(t.query, '", fragment: "').concat(t.fragment, '"}'));
            if (t.scheme && !e.test(t.scheme)) throw new Error("[UriError]: Scheme contains illegal characters.");
            if (t.path) {
                if (t.authority) {
                    if (!i.test(t.path)) throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character');
                } else if (o.test(t.path)) throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")');
            }
        }
        const h = "", a = "/", c = /^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;
        class f {
            static isUri(t) {
                return t instanceof f || !!t && "string" == typeof t.authority && "string" == typeof t.fragment && "string" == typeof t.path && "string" == typeof t.query && "string" == typeof t.scheme && "string" == typeof t.fsPath && "function" == typeof t.with && "function" == typeof t.toString;
            }
            get fsPath() {
                return m(this, !1);
            }
            with(t) {
                if (!t) return this;
                let { scheme: e, authority: r, path: n, query: i, fragment: o } = t;
                return void 0 === e ? e = this.scheme : null === e && (e = h), void 0 === r ? r = this.authority : null === r && (r = h), void 0 === n ? n = this.path : null === n && (n = h), void 0 === i ? i = this.query : null === i && (i = h), void 0 === o ? o = this.fragment : null === o && (o = h), e === this.scheme && r === this.authority && n === this.path && i === this.query && o === this.fragment ? this : new l(e, r, n, i, o);
            }
            static parse(t) {
                let e = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : !1;
                const r = c.exec(t);
                return r ? new l(r[2] || h, C(r[4] || h), C(r[5] || h), C(r[7] || h), C(r[9] || h), e) : new l(h, h, h, h, h);
            }
            static file(e) {
                let r = h;
                if (t && (e = e.replace(/\\/g, a)), e[0] === a && e[1] === a) {
                    const t = e.indexOf(a, 2);
                    -1 === t ? (r = e.substring(2), e = a) : (r = e.substring(2, t), e = e.substring(t) || a);
                }
                return new l("file", r, e, h, h);
            }
            static from(t) {
                const e = new l(t.scheme, t.authority, t.path, t.query, t.fragment);
                return s(e, !0), e;
            }
            toString() {
                let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : !1;
                return y(this, t);
            }
            toJSON() {
                return this;
            }
            static revive(t) {
                if (t) {
                    if (t instanceof f) return t;
                    {
                        const e = new l(t);
                        return e._formatted = t.external, e._fsPath = t._sep === u ? t.fsPath : null, e;
                    }
                }
                return t;
            }
            constructor(t, e, r, n, i, o = !1){
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "scheme", void 0);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "authority", void 0);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "path", void 0);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "query", void 0);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "fragment", void 0);
                "object" == typeof t ? (this.scheme = t.scheme || h, this.authority = t.authority || h, this.path = t.path || h, this.query = t.query || h, this.fragment = t.fragment || h) : (this.scheme = function(t, e) {
                    return t || e ? t : "file";
                }(t, o), this.authority = e || h, this.path = function(t, e) {
                    switch(t){
                        case "https":
                        case "http":
                        case "file":
                            e ? e[0] !== a && (e = a + e) : e = a;
                    }
                    return e;
                }(this.scheme, r || h), this.query = n || h, this.fragment = i || h, s(this, o));
            }
        }
        const u = t ? 1 : void 0;
        class l extends f {
            get fsPath() {
                return this._fsPath || (this._fsPath = m(this, !1)), this._fsPath;
            }
            toString() {
                let t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : !1;
                return t ? y(this, !0) : (this._formatted || (this._formatted = y(this, !1)), this._formatted);
            }
            toJSON() {
                const t = {
                    $mid: 1
                };
                return this._fsPath && (t.fsPath = this._fsPath, t._sep = u), this._formatted && (t.external = this._formatted), this.path && (t.path = this.path), this.scheme && (t.scheme = this.scheme), this.authority && (t.authority = this.authority), this.query && (t.query = this.query), this.fragment && (t.fragment = this.fragment), t;
            }
            constructor(...args){
                super(...args), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "_formatted", null), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "_fsPath", null);
            }
        }
        const g = {
            58: "%3A",
            47: "%2F",
            63: "%3F",
            35: "%23",
            91: "%5B",
            93: "%5D",
            64: "%40",
            33: "%21",
            36: "%24",
            38: "%26",
            39: "%27",
            40: "%28",
            41: "%29",
            42: "%2A",
            43: "%2B",
            44: "%2C",
            59: "%3B",
            61: "%3D",
            32: "%20"
        };
        function d(t, e, r) {
            let n, i = -1;
            for(let o = 0; o < t.length; o++){
                const s = t.charCodeAt(o);
                if (s >= 97 && s <= 122 || s >= 65 && s <= 90 || s >= 48 && s <= 57 || 45 === s || 46 === s || 95 === s || 126 === s || e && 47 === s || r && 91 === s || r && 93 === s || r && 58 === s) -1 !== i && (n += encodeURIComponent(t.substring(i, o)), i = -1), void 0 !== n && (n += t.charAt(o));
                else {
                    void 0 === n && (n = t.substr(0, o));
                    const e = g[s];
                    void 0 !== e ? (-1 !== i && (n += encodeURIComponent(t.substring(i, o)), i = -1), n += e) : -1 === i && (i = o);
                }
            }
            return -1 !== i && (n += encodeURIComponent(t.substring(i))), void 0 !== n ? n : t;
        }
        function p(t) {
            let e;
            for(let r = 0; r < t.length; r++){
                const n = t.charCodeAt(r);
                35 === n || 63 === n ? (void 0 === e && (e = t.substr(0, r)), e += g[n]) : void 0 !== e && (e += t[r]);
            }
            return void 0 !== e ? e : t;
        }
        function m(e, r) {
            let n;
            return n = e.authority && e.path.length > 1 && "file" === e.scheme ? "//".concat(e.authority).concat(e.path) : 47 === e.path.charCodeAt(0) && (e.path.charCodeAt(1) >= 65 && e.path.charCodeAt(1) <= 90 || e.path.charCodeAt(1) >= 97 && e.path.charCodeAt(1) <= 122) && 58 === e.path.charCodeAt(2) ? r ? e.path.substr(1) : e.path[1].toLowerCase() + e.path.substr(2) : e.path, t && (n = n.replace(/\//g, "\\")), n;
        }
        function y(t, e) {
            const r = e ? p : d;
            let n = "", { scheme: i, authority: o, path: s, query: h, fragment: c } = t;
            if (i && (n += i, n += ":"), (o || "file" === i) && (n += a, n += a), o) {
                let t = o.indexOf("@");
                if (-1 !== t) {
                    const e = o.substr(0, t);
                    o = o.substr(t + 1), t = e.lastIndexOf(":"), -1 === t ? n += r(e, !1, !1) : (n += r(e.substr(0, t), !1, !1), n += ":", n += r(e.substr(t + 1), !1, !0)), n += "@";
                }
                o = o.toLowerCase(), t = o.lastIndexOf(":"), -1 === t ? n += r(o, !1, !0) : (n += r(o.substr(0, t), !1, !0), n += o.substr(t));
            }
            if (s) {
                if (s.length >= 3 && 47 === s.charCodeAt(0) && 58 === s.charCodeAt(2)) {
                    const t = s.charCodeAt(1);
                    t >= 65 && t <= 90 && (s = "/".concat(String.fromCharCode(t + 32), ":").concat(s.substr(3)));
                } else if (s.length >= 2 && 58 === s.charCodeAt(1)) {
                    const t = s.charCodeAt(0);
                    t >= 65 && t <= 90 && (s = "".concat(String.fromCharCode(t + 32), ":").concat(s.substr(2)));
                }
                n += r(s, !0, !1);
            }
            return h && (n += "?", n += r(h, !1, !1)), c && (n += "#", n += e ? c : d(c, !1, !1)), n;
        }
        function v(t) {
            try {
                return decodeURIComponent(t);
            } catch (e) {
                return t.length > 3 ? t.substr(0, 3) + v(t.substr(3)) : t;
            }
        }
        const b = /(%[0-9A-Za-z][0-9A-Za-z])+/g;
        function C(t) {
            return t.match(b) ? t.replace(b, (t)=>v(t)) : t;
        }
        var A = r(470);
        const w = A.posix || A, x = "/";
        var P;
        !function(t) {
            t.joinPath = function(t) {
                for(var _len = arguments.length, e = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    e[_key - 1] = arguments[_key];
                }
                return t.with({
                    path: w.join(t.path, ...e)
                });
            }, t.resolvePath = function(t) {
                for(var _len = arguments.length, e = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    e[_key - 1] = arguments[_key];
                }
                let r = t.path, n = !1;
                r[0] !== x && (r = x + r, n = !0);
                let i = w.resolve(r, ...e);
                return n && i[0] === x && !t.authority && (i = i.substring(1)), t.with({
                    path: i
                });
            }, t.dirname = function(t) {
                if (0 === t.path.length || t.path === x) return t;
                let e = w.dirname(t.path);
                return 1 === e.length && 46 === e.charCodeAt(0) && (e = ""), t.with({
                    path: e
                });
            }, t.basename = function(t) {
                return w.basename(t.path);
            }, t.extname = function(t) {
                return w.extname(t.path);
            };
        }(P || (P = {}));
    })(), LIB = n;
})();
const { URI, Utils } = LIB; //# sourceMappingURL=index.mjs.map
}),
}]);

//# sourceMappingURL=node_modules_e155aa38._.js.map