{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/AgentFlow/agent-flow-app/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, X, AlertCircle } from 'lucide-react';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  onFileRemove: () => void;\n  selectedFile: File | null;\n  isProcessing: boolean;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  onFileRemove,\n  selectedFile,\n  isProcessing\n}) => {\n  const [error, setError] = useState<string | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError(null);\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setError('File is too large. Maximum size is 10MB.');\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setError('Invalid file type. Please upload an image file (PNG, JPG, JPEG, GIF, SVG, WebP).');\n      } else {\n        setError('Invalid file. Please try again.');\n      }\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      onFileSelect(acceptedFiles[0]);\n    }\n  }, [onFileSelect]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp']\n    },\n    maxSize: 10 * 1024 * 1024, // 10MB\n    multiple: false,\n    disabled: isProcessing\n  });\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto\">\n      {!selectedFile ? (\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\n            ${isDragActive \n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' \n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n            }\n            ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}\n          `}\n        >\n          <input {...getInputProps()} />\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <div className=\"space-y-2\">\n            <p className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              {isDragActive ? 'Drop your file here' : 'Upload a process diagram'}\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Drag and drop an image file here, or click to browse\n            </p>\n            <p className=\"text-xs text-gray-400 dark:text-gray-500\">\n              Supports PNG, JPG, JPEG, GIF, SVG, WebP (max 10MB)\n            </p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <File className=\"h-8 w-8 text-blue-500\" />\n              <div>\n                <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                  {selectedFile.name}\n                </p>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            {!isProcessing && (\n              <button\n                onClick={onFileRemove}\n                className=\"p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors\"\n                title=\"Remove file\"\n              >\n                <X className=\"h-5 w-5 text-gray-500\" />\n              </button>\n            )}\n          </div>\n          \n          {selectedFile.type.startsWith('image/') && (\n            <div className=\"mt-4\">\n              <img\n                src={URL.createObjectURL(selectedFile)}\n                alt=\"Preview\"\n                className=\"max-w-full h-auto max-h-64 rounded-lg border border-gray-200 dark:border-gray-700\"\n              />\n            </div>\n          )}\n        </div>\n      )}\n\n      {error && (\n        <div className=\"mt-4 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <div className=\"flex items-center space-x-2\">\n            <AlertCircle className=\"h-5 w-5 text-red-500\" />\n            <p className=\"text-sm text-red-700 dark:text-red-400\">{error}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAaA,MAAM,aAAwC;QAAC,EAC7C,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACb;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;oBAExB,oBAEO;gBAHX,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,EAAA,qBAAA,UAAU,MAAM,CAAC,EAAE,cAAnB,yCAAA,mBAAqB,IAAI,MAAK,kBAAkB;oBAClD,SAAS;gBACX,OAAO,IAAI,EAAA,sBAAA,UAAU,MAAM,CAAC,EAAE,cAAnB,0CAAA,oBAAqB,IAAI,MAAK,qBAAqB;oBAC5D,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,aAAa,aAAa,CAAC,EAAE;YAC/B;QACF;yCAAG;QAAC;KAAa;IAEjB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/D;QACA,SAAS,KAAK,OAAO;QACrB,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,6BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,2HAMR,OAJA,eACE,mDACA,yFACH,kBACqD,OAApD,eAAe,kCAAkC,IAAG;;kCAGxD,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,eAAe,wBAAwB;;;;;;0CAE1C,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CAGxD,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;yEAM5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DACV,aAAa,IAAI;;;;;;0DAEpB,6LAAC;gDAAE,WAAU;0DACV,eAAe,aAAa,IAAI;;;;;;;;;;;;;;;;;;4BAItC,CAAC,8BACA,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKlB,aAAa,IAAI,CAAC,UAAU,CAAC,2BAC5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK,IAAI,eAAe,CAAC;4BACzB,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;YAOnB,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;AAMnE;GAzHM;;QA4BkD,2KAAA,CAAA,cAAW;;;KA5B7D;uCA2HS", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/AgentFlow/agent-flow-app/src/components/ProcessResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef } from 'react';\nimport mermaid from 'mermaid';\nimport { Copy, Download, Bot, Lightbulb } from 'lucide-react';\n\ninterface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  type: 'manual' | 'automated' | 'decision';\n}\n\ninterface AutomationAgent {\n  name: string;\n  description: string;\n  capabilities: string[];\n  estimatedEffort: string;\n}\n\ninterface ProcessResultsProps {\n  mermaidDiagram: string;\n  processSteps: ProcessStep[];\n  automationAgents: AutomationAgent[];\n  isLoading: boolean;\n}\n\nconst ProcessResults: React.FC<ProcessResultsProps> = ({\n  mermaidDiagram,\n  processSteps,\n  automationAgents,\n  isLoading\n}) => {\n  const mermaidRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (mermaidDiagram && mermaidRef.current) {\n      mermaid.initialize({\n        startOnLoad: true,\n        theme: 'default',\n        securityLevel: 'loose',\n        fontFamily: 'ui-sans-serif, system-ui, sans-serif'\n      });\n\n      mermaidRef.current.innerHTML = mermaidDiagram;\n      mermaid.init(undefined, mermaidRef.current);\n    }\n  }, [mermaidDiagram]);\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n  };\n\n  const downloadDiagram = () => {\n    const svg = mermaidRef.current?.querySelector('svg');\n    if (svg) {\n      const svgData = new XMLSerializer().serializeToString(svg);\n      const blob = new Blob([svgData], { type: 'image/svg+xml' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'process-diagram.svg';\n      a.click();\n      URL.revokeObjectURL(url);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"w-full max-w-6xl mx-auto p-6\">\n        <div className=\"animate-pulse space-y-6\">\n          <div className=\"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3\"></div>\n          <div className=\"h-64 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n          <div className=\"space-y-4\">\n            <div className=\"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n              <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto p-6 space-y-8\">\n      {/* Mermaid Diagram Section */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n            Process Flow Diagram\n          </h2>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => copyToClipboard(mermaidDiagram)}\n              className=\"flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n            >\n              <Copy className=\"h-4 w-4\" />\n              <span>Copy Code</span>\n            </button>\n            <button\n              onClick={downloadDiagram}\n              className=\"flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>Download SVG</span>\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900\">\n          <div ref={mermaidRef} className=\"mermaid-diagram\"></div>\n        </div>\n      </div>\n\n      {/* Process Steps Section */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6\">\n          Process Steps Analysis\n        </h2>\n        \n        <div className=\"space-y-4\">\n          {processSteps.map((step, index) => (\n            <div\n              key={step.id}\n              className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <div className={`\n                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold\n                    ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}\n                    ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}\n                    ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}\n                  `}>\n                    {index + 1}\n                  </div>\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <h3 className=\"font-semibold text-gray-900 dark:text-gray-100\">\n                      {step.title}\n                    </h3>\n                    <span className={`\n                      px-2 py-1 text-xs rounded-full\n                      ${step.type === 'manual' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : ''}\n                      ${step.type === 'automated' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}\n                      ${step.type === 'decision' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}\n                    `}>\n                      {step.type}\n                    </span>\n                  </div>\n                  <p className=\"text-gray-600 dark:text-gray-300\">\n                    {step.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Automation Agents Section */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center space-x-2 mb-6\">\n          <Bot className=\"h-6 w-6 text-blue-500\" />\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n            Suggested Automation Agents\n          </h2>\n        </div>\n        \n        <div className=\"grid gap-6 md:grid-cols-2\">\n          {automationAgents.map((agent, index) => (\n            <div\n              key={index}\n              className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <Lightbulb className=\"h-6 w-6 text-yellow-500 flex-shrink-0 mt-1\" />\n                <div className=\"flex-1\">\n                  <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n                    {agent.name}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 mb-3\">\n                    {agent.description}\n                  </p>\n                  \n                  <div className=\"mb-3\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                      Capabilities:\n                    </h4>\n                    <ul className=\"list-disc list-inside text-sm text-gray-600 dark:text-gray-300 space-y-1\">\n                      {agent.capabilities.map((capability, capIndex) => (\n                        <li key={capIndex}>{capability}</li>\n                      ))}\n                    </ul>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Estimated effort: {agent.estimatedEffort}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProcessResults;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AA2BA,MAAM,iBAAgD;QAAC,EACrD,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACV;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB,WAAW,OAAO,EAAE;gBACxC,sJAAA,CAAA,UAAO,CAAC,UAAU,CAAC;oBACjB,aAAa;oBACb,OAAO;oBACP,eAAe;oBACf,YAAY;gBACd;gBAEA,WAAW,OAAO,CAAC,SAAS,GAAG;gBAC/B,sJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,WAAW,WAAW,OAAO;YAC5C;QACF;mCAAG;QAAC;KAAe;IAEnB,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,kBAAkB;YACV;QAAZ,MAAM,OAAM,sBAAA,WAAW,OAAO,cAAlB,0CAAA,oBAAoB,aAAa,CAAC;QAC9C,IAAI,KAAK;YACP,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;YACtD,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAAgB;YACzD,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,EAAE,KAAK;YACP,IAAI,eAAe,CAAC;QACtB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,KAAK;4BAAY,WAAU;;;;;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gCAEC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,AAAC,sHAGb,OADA,KAAK,IAAI,KAAK,WAAW,0EAA0E,IAAG,0BAEtG,OADA,KAAK,IAAI,KAAK,cAAc,sEAAsE,IAAG,0BACL,OAAhG,KAAK,IAAI,KAAK,aAAa,kEAAkE,IAAG;0DAEjG,QAAQ;;;;;;;;;;;sDAGb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAK,WAAW,AAAC,iFAGd,OADA,KAAK,IAAI,KAAK,WAAW,0EAA0E,IAAG,4BAEtG,OADA,KAAK,IAAI,KAAK,cAAc,sEAAsE,IAAG,4BACL,OAAhG,KAAK,IAAI,KAAK,aAAa,kEAAkE,IAAG;sEAEjG,KAAK,IAAI;;;;;;;;;;;;8DAGd,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BA7BlB,KAAK,EAAE;;;;;;;;;;;;;;;;0BAuCpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC;gCAEC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,MAAM,IAAI;;;;;;8DAEb,6LAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAG1E,6LAAC;4DAAG,WAAU;sEACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,yBACnC,6LAAC;8EAAmB;mEAAX;;;;;;;;;;;;;;;;8DAKf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;;4DAA2C;4DACtC,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;+BA1B3C;;;;;;;;;;;;;;;;;;;;;;AAqCnB;GAzLM;KAAA;uCA2LS", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/AgentFlow/agent-flow-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport FileUpload from '@/components/FileUpload';\nimport ProcessResults from '@/components/ProcessResults';\nimport { Bot, Zap } from 'lucide-react';\n\ninterface ProcessStep {\n  id: string;\n  title: string;\n  description: string;\n  type: 'manual' | 'automated' | 'decision';\n}\n\ninterface AutomationAgent {\n  name: string;\n  description: string;\n  capabilities: string[];\n  estimatedEffort: string;\n}\n\ninterface AnalysisResult {\n  mermaidDiagram: string;\n  processSteps: ProcessStep[];\n  automationAgents: AutomationAgent[];\n}\n\nexport default function Home() {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleFileSelect = (file: File) => {\n    setSelectedFile(file);\n    setAnalysisResult(null);\n    setError(null);\n  };\n\n  const handleFileRemove = () => {\n    setSelectedFile(null);\n    setAnalysisResult(null);\n    setError(null);\n  };\n\n  const handleAnalyzeProcess = async () => {\n    if (!selectedFile) return;\n\n    setIsProcessing(true);\n    setError(null);\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n\n      const response = await fetch('/api/analyze-process', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.details || errorData.error || 'Failed to analyze process');\n      }\n\n      const result: AnalysisResult = await response.json();\n      setAnalysisResult(result);\n    } catch (err) {\n      console.error('Error analyzing process:', err);\n      setError(err instanceof Error ? err.message : 'An unexpected error occurred');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <Bot className=\"h-8 w-8 text-blue-500\" />\n              <Zap className=\"h-6 w-6 text-yellow-500\" />\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n                AgentFlow\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                Transform process diagrams into automated workflows\n              </p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!analysisResult ? (\n          <div className=\"space-y-8\">\n            {/* Upload Section */}\n            <div className=\"text-center space-y-4\">\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                Upload Your Process Diagram\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto\">\n                Upload an image of your process flow, workflow diagram, or flowchart.\n                Claude AI will analyze it and create a Mermaid diagram with automation suggestions.\n              </p>\n            </div>\n\n            <FileUpload\n              onFileSelect={handleFileSelect}\n              onFileRemove={handleFileRemove}\n              selectedFile={selectedFile}\n              isProcessing={isProcessing}\n            />\n\n            {selectedFile && !isProcessing && (\n              <div className=\"text-center\">\n                <button\n                  onClick={handleAnalyzeProcess}\n                  className=\"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <Bot className=\"h-5 w-5\" />\n                  <span>Analyze Process</span>\n                </button>\n              </div>\n            )}\n\n            {error && (\n              <div className=\"max-w-2xl mx-auto p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg\">\n                <p className=\"text-red-700 dark:text-red-400\">{error}</p>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            {/* Back to Upload Button */}\n            <div className=\"flex justify-between items-center\">\n              <button\n                onClick={() => {\n                  setAnalysisResult(null);\n                  setSelectedFile(null);\n                }}\n                className=\"inline-flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/20 rounded-lg transition-colors\"\n              >\n                <span>← Upload New Diagram</span>\n              </button>\n            </div>\n\n            {/* Results */}\n            <ProcessResults\n              mermaidDiagram={analysisResult.mermaidDiagram}\n              processSteps={analysisResult.processSteps}\n              automationAgents={analysisResult.automationAgents}\n              isLoading={false}\n            />\n          </div>\n        )}\n\n        {isProcessing && (\n          <ProcessResults\n            mermaidDiagram=\"\"\n            processSteps={[]}\n            automationAgents={[]}\n            isLoading={true}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,SAAS;IACX;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,kBAAkB;QAClB,SAAS;IACX;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc;QAEnB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;YAC1D;YAEA,MAAM,SAAyB,MAAM,SAAS,IAAI;YAClD,kBAAkB;QACpB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;0CAEjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAK,WAAU;;oBACb,CAAC,+BACA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCAAE,WAAU;kDAAqD;;;;;;;;;;;;0CAMpE,6LAAC,mIAAA,CAAA,UAAU;gCACT,cAAc;gCACd,cAAc;gCACd,cAAc;gCACd,cAAc;;;;;;4BAGf,gBAAgB,CAAC,8BAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;;;;;;4BAKX,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;6CAKrD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,kBAAkB;wCAClB,gBAAgB;oCAClB;oCACA,WAAU;8CAEV,cAAA,6LAAC;kDAAK;;;;;;;;;;;;;;;;0CAKV,6LAAC,uIAAA,CAAA,UAAc;gCACb,gBAAgB,eAAe,cAAc;gCAC7C,cAAc,eAAe,YAAY;gCACzC,kBAAkB,eAAe,gBAAgB;gCACjD,WAAW;;;;;;;;;;;;oBAKhB,8BACC,6LAAC,uIAAA,CAAA,UAAc;wBACb,gBAAe;wBACf,cAAc,EAAE;wBAChB,kBAAkB,EAAE;wBACpB,WAAW;;;;;;;;;;;;;;;;;;AAMvB;GAlJwB;KAAA", "debugId": null}}]}