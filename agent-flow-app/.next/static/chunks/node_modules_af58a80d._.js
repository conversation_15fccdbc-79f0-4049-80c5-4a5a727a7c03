(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/d3-array/src/range.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>range
});
function range(start, stop, step) {
    start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;
    var i = -1, n = Math.max(0, Math.ceil((stop - start) / step)) | 0, range = new Array(n);
    while(++i < n){
        range[i] = start + i * step;
    }
    return range;
}
}),
"[project]/node_modules/d3-array/src/range.js [app-client] (ecmascript) <export default as range>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "range": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/range.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "initInterpolator": ()=>initInterpolator,
    "initRange": ()=>initRange
});
function initRange(domain, range) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            this.range(domain);
            break;
        default:
            this.range(range).domain(domain);
            break;
    }
    return this;
}
function initInterpolator(domain, interpolator) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            {
                if (typeof domain === "function") this.interpolator(domain);
                else this.range(domain);
                break;
            }
        default:
            {
                this.domain(domain);
                if (typeof interpolator === "function") this.interpolator(interpolator);
                else this.range(interpolator);
                break;
            }
    }
    return this;
}
}),
"[project]/node_modules/internmap/src/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "InternMap": ()=>InternMap,
    "InternSet": ()=>InternSet
});
class InternMap extends Map {
    get(key) {
        return super.get(intern_get(this, key));
    }
    has(key) {
        return super.has(intern_get(this, key));
    }
    set(key, value) {
        return super.set(intern_set(this, key), value);
    }
    delete(key) {
        return super.delete(intern_delete(this, key));
    }
    constructor(entries, key = keyof){
        super();
        Object.defineProperties(this, {
            _intern: {
                value: new Map()
            },
            _key: {
                value: key
            }
        });
        if (entries != null) for (const [key, value] of entries)this.set(key, value);
    }
}
class InternSet extends Set {
    has(value) {
        return super.has(intern_get(this, value));
    }
    add(value) {
        return super.add(intern_set(this, value));
    }
    delete(value) {
        return super.delete(intern_delete(this, value));
    }
    constructor(values, key = keyof){
        super();
        Object.defineProperties(this, {
            _intern: {
                value: new Map()
            },
            _key: {
                value: key
            }
        });
        if (values != null) for (const value of values)this.add(value);
    }
}
function intern_get(param, value) {
    let { _intern, _key } = param;
    const key = _key(value);
    return _intern.has(key) ? _intern.get(key) : value;
}
function intern_set(param, value) {
    let { _intern, _key } = param;
    const key = _key(value);
    if (_intern.has(key)) return _intern.get(key);
    _intern.set(key, value);
    return value;
}
function intern_delete(param, value) {
    let { _intern, _key } = param;
    const key = _key(value);
    if (_intern.has(key)) {
        value = _intern.get(key);
        _intern.delete(key);
    }
    return value;
}
function keyof(value) {
    return value !== null && typeof value === "object" ? value.valueOf() : value;
}
}),
"[project]/node_modules/d3-scale/src/ordinal.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ordinal,
    "implicit": ()=>implicit
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$internmap$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/internmap/src/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)");
;
;
const implicit = Symbol("implicit");
function ordinal() {
    var index = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$internmap$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InternMap"](), domain = [], range = [], unknown = implicit;
    function scale(d) {
        let i = index.get(d);
        if (i === undefined) {
            if (unknown !== implicit) return unknown;
            index.set(d, i = domain.push(d) - 1);
        }
        return range[i % range.length];
    }
    scale.domain = function(_) {
        if (!arguments.length) return domain.slice();
        domain = [], index = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$internmap$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InternMap"]();
        for (const value of _){
            if (index.has(value)) continue;
            index.set(value, domain.push(value) - 1);
        }
        return scale;
    };
    scale.range = function(_) {
        return arguments.length ? (range = Array.from(_), scale) : range.slice();
    };
    scale.unknown = function(_) {
        return arguments.length ? (unknown = _, scale) : unknown;
    };
    scale.copy = function() {
        return ordinal(domain, range).unknown(unknown);
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initRange"].apply(scale, arguments);
    return scale;
}
}),
"[project]/node_modules/d3-scale/src/band.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>band,
    "point": ()=>point
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__range$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/range.js [app-client] (ecmascript) <export default as range>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$ordinal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/ordinal.js [app-client] (ecmascript)");
;
;
;
function band() {
    var scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$ordinal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().unknown(undefined), domain = scale.domain, ordinalRange = scale.range, r0 = 0, r1 = 1, step, bandwidth, round = false, paddingInner = 0, paddingOuter = 0, align = 0.5;
    delete scale.unknown;
    function rescale() {
        var n = domain().length, reverse = r1 < r0, start = reverse ? r1 : r0, stop = reverse ? r0 : r1;
        step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);
        if (round) step = Math.floor(step);
        start += (stop - start - step * (n - paddingInner)) * align;
        bandwidth = step * (1 - paddingInner);
        if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);
        var values = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__range$3e$__["range"])(n).map(function(i) {
            return start + step * i;
        });
        return ordinalRange(reverse ? values.reverse() : values);
    }
    scale.domain = function(_) {
        return arguments.length ? (domain(_), rescale()) : domain();
    };
    scale.range = function(_) {
        return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [
            r0,
            r1
        ];
    };
    scale.rangeRound = function(_) {
        return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();
    };
    scale.bandwidth = function() {
        return bandwidth;
    };
    scale.step = function() {
        return step;
    };
    scale.round = function(_) {
        return arguments.length ? (round = !!_, rescale()) : round;
    };
    scale.padding = function(_) {
        return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;
    };
    scale.paddingInner = function(_) {
        return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;
    };
    scale.paddingOuter = function(_) {
        return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;
    };
    scale.align = function(_) {
        return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;
    };
    scale.copy = function() {
        return band(domain(), [
            r0,
            r1
        ]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initRange"].apply(rescale(), arguments);
}
function pointish(scale) {
    var copy = scale.copy;
    scale.padding = scale.paddingOuter;
    delete scale.paddingInner;
    delete scale.paddingOuter;
    scale.copy = function() {
        return pointish(copy());
    };
    return scale;
}
function point() {
    return pointish(band.apply(null, arguments).paddingInner(1));
}
}),
"[project]/node_modules/d3-scale/src/band.js [app-client] (ecmascript) <export default as scaleBand>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "scaleBand": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$band$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$band$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/band.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ticks,
    "tickIncrement": ()=>tickIncrement,
    "tickStep": ()=>tickStep
});
const e10 = Math.sqrt(50), e5 = Math.sqrt(10), e2 = Math.sqrt(2);
function tickSpec(start, stop, count) {
    const step = (stop - start) / Math.max(0, count), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
    let i1, i2, inc;
    if (power < 0) {
        inc = Math.pow(10, -power) / factor;
        i1 = Math.round(start * inc);
        i2 = Math.round(stop * inc);
        if (i1 / inc < start) ++i1;
        if (i2 / inc > stop) --i2;
        inc = -inc;
    } else {
        inc = Math.pow(10, power) * factor;
        i1 = Math.round(start / inc);
        i2 = Math.round(stop / inc);
        if (i1 * inc < start) ++i1;
        if (i2 * inc > stop) --i2;
    }
    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);
    return [
        i1,
        i2,
        inc
    ];
}
function ticks(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    if (!(count > 0)) return [];
    if (start === stop) return [
        start
    ];
    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);
    if (!(i2 >= i1)) return [];
    const n = i2 - i1 + 1, ticks = new Array(n);
    if (reverse) {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) * inc;
    } else {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) * inc;
    }
    return ticks;
}
function tickIncrement(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    return tickSpec(start, stop, count)[2];
}
function tickStep(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);
    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
}
}),
"[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript) <export default as ticks>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ticks": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ascending
});
function ascending(a, b) {
    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
}
}),
"[project]/node_modules/d3-array/src/descending.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>descending
});
function descending(a, b) {
    return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;
}
}),
"[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>bisector
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/descending.js [app-client] (ecmascript)");
;
;
function bisector(f) {
    let compare1, compare2, delta;
    // If an accessor is specified, promote it to a comparator. In this case we
    // can test whether the search value is (self-) comparable. We can’t do this
    // for a comparator (except for specific, known comparators) because we can’t
    // tell if the comparator is symmetric, and an asymmetric comparator can’t be
    // used to test whether a single value is comparable.
    if (f.length !== 2) {
        compare1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        compare2 = (d, x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(f(d), x);
        delta = (d, x)=>f(d) - x;
    } else {
        compare1 = f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] ? f : zero;
        compare2 = f;
        delta = f;
    }
    function left(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) < 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function right(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) <= 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function center(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        const i = left(a, x, lo, hi - 1);
        return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;
    }
    return {
        left,
        center,
        right
    };
}
function zero() {
    return 0;
}
}),
"[project]/node_modules/d3-array/src/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>number,
    "numbers": ()=>numbers
});
function number(x) {
    return x === null ? NaN : +x;
}
function* numbers(values, valueof) {
    if (valueof === undefined) {
        for (let value of values){
            if (value != null && (value = +value) >= value) {
                yield value;
            }
        }
    } else {
        let index = -1;
        for (let value of values){
            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {
                yield value;
            }
        }
    }
}
}),
"[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bisectCenter": ()=>bisectCenter,
    "bisectLeft": ()=>bisectLeft,
    "bisectRight": ()=>bisectRight,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/number.js [app-client] (ecmascript)");
;
;
;
const ascendingBisect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const bisectRight = ascendingBisect.right;
const bisectLeft = ascendingBisect.left;
const bisectCenter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]).center;
const __TURBOPACK__default__export__ = bisectRight;
}),
"[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript) <export default as bisect>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bisect": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(a, b) {
    return a = +a, b = +b, function(t) {
        return Math.round(a * (1 - t) + b * t);
    };
}
}),
"[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript) <export default as interpolateRound>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "interpolateRound": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-scale/src/constant.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>constants
});
function constants(x) {
    return function() {
        return x;
    };
}
}),
"[project]/node_modules/d3-scale/src/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>number
});
function number(x) {
    return +x;
}
}),
"[project]/node_modules/d3-scale/src/continuous.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "copy": ()=>copy,
    "default": ()=>continuous,
    "identity": ()=>identity,
    "transformer": ()=>transformer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript) <export default as bisect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/value.js [app-client] (ecmascript) <export default as interpolate>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/number.js [app-client] (ecmascript) <export default as interpolateNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript) <export default as interpolateRound>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/number.js [app-client] (ecmascript)");
;
;
;
;
var unit = [
    0,
    1
];
function identity(x) {
    return x;
}
function normalize(a, b) {
    return (b -= a = +a) ? function(x) {
        return (x - a) / b;
    } : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(isNaN(b) ? NaN : 0.5);
}
function clamper(a, b) {
    var t;
    if (a > b) t = a, a = b, b = t;
    return function(x) {
        return Math.max(a, Math.min(b, x));
    };
}
// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].
// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].
function bimap(domain, range, interpolate) {
    var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];
    if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);
    else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);
    return function(x) {
        return r0(d0(x));
    };
}
function polymap(domain, range, interpolate) {
    var j = Math.min(domain.length, range.length) - 1, d = new Array(j), r = new Array(j), i = -1;
    // Reverse descending domains.
    if (domain[j] < domain[0]) {
        domain = domain.slice().reverse();
        range = range.slice().reverse();
    }
    while(++i < j){
        d[i] = normalize(domain[i], domain[i + 1]);
        r[i] = interpolate(range[i], range[i + 1]);
    }
    return function(x) {
        var i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__["bisect"])(domain, x, 1, j) - 1;
        return r[i](d[i](x));
    };
}
function copy(source, target) {
    return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());
}
function transformer() {
    var domain = unit, range = unit, interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__["interpolate"], transform, untransform, unknown, clamp = identity, piecewise, output, input;
    function rescale() {
        var n = Math.min(domain.length, range.length);
        if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);
        piecewise = n > 2 ? polymap : bimap;
        output = input = null;
        return scale;
    }
    function scale(x) {
        return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));
    }
    scale.invert = function(y) {
        return clamp(untransform((input || (input = piecewise(range, domain.map(transform), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__["interpolateNumber"])))(y)));
    };
    scale.domain = function(_) {
        return arguments.length ? (domain = Array.from(_, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), rescale()) : domain.slice();
    };
    scale.range = function(_) {
        return arguments.length ? (range = Array.from(_), rescale()) : range.slice();
    };
    scale.rangeRound = function(_) {
        return range = Array.from(_), interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__["interpolateRound"], rescale();
    };
    scale.clamp = function(_) {
        return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;
    };
    scale.interpolate = function(_) {
        return arguments.length ? (interpolate = _, rescale()) : interpolate;
    };
    scale.unknown = function(_) {
        return arguments.length ? (unknown = _, scale) : unknown;
    };
    return function(t, u) {
        transform = t, untransform = u;
        return rescale();
    };
}
function continuous() {
    return transformer()(identity, identity);
}
}),
"[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "formatDecimalParts": ()=>formatDecimalParts
});
function __TURBOPACK__default__export__(x) {
    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString("en").replace(/,/g, "") : x.toString(10);
}
function formatDecimalParts(x, p) {
    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf("e")) < 0) return null; // NaN, ±Infinity
    var i, coefficient = x.slice(0, i);
    // The string returned by toExponential either has the form \d\.\d+e[-+]\d+
    // (e.g., 1.2e+3) or the form \de[-+]\d+ (e.g., 1e+3).
    return [
        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
        +x.slice(i + 1)
    ];
}
}),
"[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(x) {
    return x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(Math.abs(x)), x ? x[1] : NaN;
}
}),
"[project]/node_modules/d3-format/src/formatGroup.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(grouping, thousands) {
    return function(value, width) {
        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;
        while(i > 0 && g > 0){
            if (length + g + 1 > width) g = Math.max(1, width - length);
            t.push(value.substring(i -= g, i + g));
            if ((length += g + 1) > width) break;
            g = grouping[j = (j + 1) % grouping.length];
        }
        return t.reverse().join(thousands);
    };
}
}),
"[project]/node_modules/d3-format/src/formatNumerals.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(numerals) {
    return function(value) {
        return value.replace(/[0-9]/g, function(i) {
            return numerals[+i];
        });
    };
}
}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// [[fill]align][sign][symbol][0][width][,][.precision][~][type]
__turbopack_context__.s({
    "FormatSpecifier": ()=>FormatSpecifier,
    "default": ()=>formatSpecifier
});
var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
    if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
    var match;
    return new FormatSpecifier({
        fill: match[1],
        align: match[2],
        sign: match[3],
        symbol: match[4],
        zero: match[5],
        width: match[6],
        comma: match[7],
        precision: match[8] && match[8].slice(1),
        trim: match[9],
        type: match[10]
    });
}
formatSpecifier.prototype = FormatSpecifier.prototype; // instanceof
function FormatSpecifier(specifier) {
    this.fill = specifier.fill === undefined ? " " : specifier.fill + "";
    this.align = specifier.align === undefined ? ">" : specifier.align + "";
    this.sign = specifier.sign === undefined ? "-" : specifier.sign + "";
    this.symbol = specifier.symbol === undefined ? "" : specifier.symbol + "";
    this.zero = !!specifier.zero;
    this.width = specifier.width === undefined ? undefined : +specifier.width;
    this.comma = !!specifier.comma;
    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;
    this.trim = !!specifier.trim;
    this.type = specifier.type === undefined ? "" : specifier.type + "";
}
FormatSpecifier.prototype.toString = function() {
    return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === undefined ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === undefined ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};
}),
"[project]/node_modules/d3-format/src/formatTrim.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(s) {
    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){
        switch(s[i]){
            case ".":
                i0 = i1 = i;
                break;
            case "0":
                if (i0 === 0) i0 = i;
                i1 = i;
                break;
            default:
                if (!+s[i]) break out;
                if (i0 > 0) i0 = 0;
                break;
        }
    }
    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
}
}),
"[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "prefixExponent": ()=>prefixExponent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
var prefixExponent;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;
    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join("0") : i > 0 ? coefficient.slice(0, i) + "." + coefficient.slice(i) : "0." + new Array(1 - i).join("0") + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, Math.max(0, p + i - 1))[0]; // less than 1y!
}
}),
"[project]/node_modules/d3-format/src/formatRounded.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1];
    return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}
}),
"[project]/node_modules/d3-format/src/formatTypes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatRounded.js [app-client] (ecmascript)");
;
;
;
const __TURBOPACK__default__export__ = {
    "%": (x, p)=>(x * 100).toFixed(p),
    "b": (x)=>Math.round(x).toString(2),
    "c": (x)=>x + "",
    "d": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "e": (x, p)=>x.toExponential(p),
    "f": (x, p)=>x.toFixed(p),
    "g": (x, p)=>x.toPrecision(p),
    "o": (x)=>Math.round(x).toString(8),
    "p": (x, p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(x * 100, p),
    "r": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "s": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "X": (x)=>Math.round(x).toString(16).toUpperCase(),
    "x": (x)=>Math.round(x).toString(16)
};
}),
"[project]/node_modules/d3-format/src/identity.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(x) {
    return x;
}
}),
"[project]/node_modules/d3-format/src/locale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatNumerals.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTrim.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/identity.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
var map = Array.prototype.map, prefixes = [
    "y",
    "z",
    "a",
    "f",
    "p",
    "n",
    "µ",
    "m",
    "",
    "k",
    "M",
    "G",
    "T",
    "P",
    "E",
    "Z",
    "Y"
];
function __TURBOPACK__default__export__(locale) {
    var group = locale.grouping === undefined || locale.thousands === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(map.call(locale.grouping, Number), locale.thousands + ""), currencyPrefix = locale.currency === undefined ? "" : locale.currency[0] + "", currencySuffix = locale.currency === undefined ? "" : locale.currency[1] + "", decimal = locale.decimal === undefined ? "." : locale.decimal + "", numerals = locale.numerals === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? "%" : locale.percent + "", minus = locale.minus === undefined ? "−" : locale.minus + "", nan = locale.nan === undefined ? "NaN" : locale.nan + "";
    function newFormat(specifier) {
        specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(specifier);
        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
        // The "n" type is an alias for ",g".
        if (type === "n") comma = true, type = "g";
        else if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][type]) precision === undefined && (precision = 12), trim = true, type = "g";
        // If zero fill is specified, padding goes after sign and before digits.
        if (zero || fill === "0" && align === "=") zero = true, fill = "0", align = "=";
        // Compute the prefix and suffix.
        // For SI-prefix, the suffix is lazily computed.
        var prefix = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
        // What format function should we use?
        // Is this an integer type?
        // Can this type generate exponential notation?
        var formatType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][type], maybeSuffix = /[defgprs%]/.test(type);
        // Set the default precision if not specified,
        // or clamp the specified precision to the supported range.
        // For significant precision, it must be in [1, 21].
        // For fixed precision, it must be in [0, 20].
        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
        function format(value) {
            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;
            if (type === "c") {
                valueSuffix = formatType(value) + valueSuffix;
                value = "";
            } else {
                value = +value;
                // Determine the sign. -0 is not less than 0, but 1 / -0 is!
                var valueNegative = value < 0 || 1 / value < 0;
                // Perform the initial formatting.
                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
                // Trim insignificant zeros.
                if (trim) value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value);
                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.
                if (valueNegative && +value === 0 && sign !== "+") valueNegative = false;
                // Compute the prefix and suffix.
                valuePrefix = (valueNegative ? sign === "(" ? sign : minus : sign === "-" || sign === "(" ? "" : sign) + valuePrefix;
                valueSuffix = (type === "s" ? prefixes[8 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prefixExponent"] / 3] : "") + valueSuffix + (valueNegative && sign === "(" ? ")" : "");
                // Break the formatted value into the integer “value” part that can be
                // grouped, and fractional or exponential “suffix” part that is not.
                if (maybeSuffix) {
                    i = -1, n = value.length;
                    while(++i < n){
                        if (c = value.charCodeAt(i), 48 > c || c > 57) {
                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;
                            value = value.slice(0, i);
                            break;
                        }
                    }
                }
            }
            // If the fill character is not "0", grouping is applied before padding.
            if (comma && !zero) value = group(value, Infinity);
            // Compute the padding.
            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
            // If the fill character is "0", grouping is applied after padding.
            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
            // Reconstruct the final output based on the desired alignment.
            switch(align){
                case "<":
                    value = valuePrefix + value + valueSuffix + padding;
                    break;
                case "=":
                    value = valuePrefix + padding + value + valueSuffix;
                    break;
                case "^":
                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
                    break;
                default:
                    value = padding + valuePrefix + value + valueSuffix;
                    break;
            }
            return numerals(value);
        }
        format.toString = function() {
            return specifier + "";
        };
        return format;
    }
    function formatPrefix(specifier, value) {
        var f = newFormat((specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(specifier), specifier.type = "f", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];
        return function(value) {
            return f(k * value) + prefix;
        };
    }
    return {
        format: newFormat,
        formatPrefix: formatPrefix
    };
}
}),
"[project]/node_modules/d3-format/src/defaultLocale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>defaultLocale,
    "format": ()=>format,
    "formatPrefix": ()=>formatPrefix
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/locale.js [app-client] (ecmascript)");
;
var locale;
var format;
var formatPrefix;
defaultLocale({
    thousands: ",",
    grouping: [
        3
    ],
    currency: [
        "$",
        ""
    ]
});
function defaultLocale(definition) {
    locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(definition);
    format = locale.format;
    formatPrefix = locale.formatPrefix;
    return locale;
}
}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript) <export default as formatSpecifier>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "formatSpecifier": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step) {
    return Math.max(0, -(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript) <export default as precisionFixed>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionFixed": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step, value) {
    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3 - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript) <export default as precisionPrefix>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionPrefix": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step, max) {
    step = Math.abs(step), max = Math.abs(max) - step;
    return Math.max(0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(max) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(step)) + 1;
}
}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript) <export default as precisionRound>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionRound": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-scale/src/tickFormat.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>tickFormat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/defaultLocale.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript) <export default as formatSpecifier>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript) <export default as precisionFixed>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript) <export default as precisionPrefix>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript) <export default as precisionRound>");
;
;
function tickFormat(start, stop, count, specifier) {
    var step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickStep"])(start, stop, count), precision;
    specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__["formatSpecifier"])(specifier == null ? ",f" : specifier);
    switch(specifier.type){
        case "s":
            {
                var value = Math.max(Math.abs(start), Math.abs(stop));
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__["precisionPrefix"])(step, value))) specifier.precision = precision;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatPrefix"])(specifier, value);
            }
        case "":
        case "e":
        case "g":
        case "p":
        case "r":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__["precisionRound"])(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === "e");
                break;
            }
        case "f":
        case "%":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__["precisionFixed"])(step))) specifier.precision = precision - (specifier.type === "%") * 2;
                break;
            }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["format"])(specifier);
}
}),
"[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>linear,
    "linearish": ()=>linearish
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript) <export default as ticks>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/continuous.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/tickFormat.js [app-client] (ecmascript)");
;
;
;
;
function linearish(scale) {
    var domain = scale.domain;
    scale.ticks = function(count) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__["ticks"])(d[0], d[d.length - 1], count == null ? 10 : count);
    };
    scale.tickFormat = function(count, specifier) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(d[0], d[d.length - 1], count == null ? 10 : count, specifier);
    };
    scale.nice = function(count) {
        if (count == null) count = 10;
        var d = domain();
        var i0 = 0;
        var i1 = d.length - 1;
        var start = d[i0];
        var stop = d[i1];
        var prestep;
        var step;
        var maxIter = 10;
        if (stop < start) {
            step = start, start = stop, stop = step;
            step = i0, i0 = i1, i1 = step;
        }
        while(maxIter-- > 0){
            step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickIncrement"])(start, stop, count);
            if (step === prestep) {
                d[i0] = start;
                d[i1] = stop;
                return domain(d);
            } else if (step > 0) {
                start = Math.floor(start / step) * step;
                stop = Math.ceil(stop / step) * step;
            } else if (step < 0) {
                start = Math.ceil(start * step) / step;
                stop = Math.floor(stop * step) / step;
            } else {
                break;
            }
            prestep = step;
        }
        return scale;
    };
    return scale;
}
function linear() {
    var scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    scale.copy = function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["copy"])(scale, linear());
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initRange"].apply(scale, arguments);
    return linearish(scale);
}
}),
"[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript) <export default as scaleLinear>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "scaleLinear": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript)");
}),
"[project]/node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-H2YORKM3.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "diagram": ()=>diagram
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$QESNASVV$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-QESNASVV.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-P3VETL53.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs [app-client] (ecmascript)");
// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$band$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleBand$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/band.js [app-client] (ecmascript) <export default as scaleBand>");
// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript) <export default as scaleLinear>");
// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__line$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-shape/src/line.js [app-client] (ecmascript) <export default as line>");
var _class, _class1, _class2, _class3, _class4, _class5, _class6, _class7, _class8, _class9;
;
;
;
;
// src/diagrams/xychart/parser/xychart.jison
var parser = function() {
    var o = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, "o"), $V0 = [
        1,
        10,
        12,
        14,
        16,
        18,
        19,
        21,
        23
    ], $V1 = [
        2,
        6
    ], $V2 = [
        1,
        3
    ], $V3 = [
        1,
        5
    ], $V4 = [
        1,
        6
    ], $V5 = [
        1,
        7
    ], $V6 = [
        1,
        5,
        10,
        12,
        14,
        16,
        18,
        19,
        21,
        23,
        34,
        35,
        36
    ], $V7 = [
        1,
        25
    ], $V8 = [
        1,
        26
    ], $V9 = [
        1,
        28
    ], $Va = [
        1,
        29
    ], $Vb = [
        1,
        30
    ], $Vc = [
        1,
        31
    ], $Vd = [
        1,
        32
    ], $Ve = [
        1,
        33
    ], $Vf = [
        1,
        34
    ], $Vg = [
        1,
        35
    ], $Vh = [
        1,
        36
    ], $Vi = [
        1,
        37
    ], $Vj = [
        1,
        43
    ], $Vk = [
        1,
        42
    ], $Vl = [
        1,
        47
    ], $Vm = [
        1,
        50
    ], $Vn = [
        1,
        10,
        12,
        14,
        16,
        18,
        19,
        21,
        23,
        34,
        35,
        36
    ], $Vo = [
        1,
        10,
        12,
        14,
        16,
        18,
        19,
        21,
        23,
        24,
        26,
        27,
        28,
        34,
        35,
        36
    ], $Vp = [
        1,
        10,
        12,
        14,
        16,
        18,
        19,
        21,
        23,
        24,
        26,
        27,
        28,
        34,
        35,
        36,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50
    ], $Vq = [
        1,
        64
    ];
    var parser2 = {
        trace: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function trace() {}, "trace"),
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "eol": 4,
            "XYCHART": 5,
            "chartConfig": 6,
            "document": 7,
            "CHART_ORIENTATION": 8,
            "statement": 9,
            "title": 10,
            "text": 11,
            "X_AXIS": 12,
            "parseXAxis": 13,
            "Y_AXIS": 14,
            "parseYAxis": 15,
            "LINE": 16,
            "plotData": 17,
            "BAR": 18,
            "acc_title": 19,
            "acc_title_value": 20,
            "acc_descr": 21,
            "acc_descr_value": 22,
            "acc_descr_multiline_value": 23,
            "SQUARE_BRACES_START": 24,
            "commaSeparatedNumbers": 25,
            "SQUARE_BRACES_END": 26,
            "NUMBER_WITH_DECIMAL": 27,
            "COMMA": 28,
            "xAxisData": 29,
            "bandData": 30,
            "ARROW_DELIMITER": 31,
            "commaSeparatedTexts": 32,
            "yAxisData": 33,
            "NEWLINE": 34,
            "SEMI": 35,
            "EOF": 36,
            "alphaNum": 37,
            "STR": 38,
            "MD_STR": 39,
            "alphaNumToken": 40,
            "AMP": 41,
            "NUM": 42,
            "ALPHA": 43,
            "PLUS": 44,
            "EQUALS": 45,
            "MULT": 46,
            "DOT": 47,
            "BRKT": 48,
            "MINUS": 49,
            "UNDERSCORE": 50,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            5: "XYCHART",
            8: "CHART_ORIENTATION",
            10: "title",
            12: "X_AXIS",
            14: "Y_AXIS",
            16: "LINE",
            18: "BAR",
            19: "acc_title",
            20: "acc_title_value",
            21: "acc_descr",
            22: "acc_descr_value",
            23: "acc_descr_multiline_value",
            24: "SQUARE_BRACES_START",
            26: "SQUARE_BRACES_END",
            27: "NUMBER_WITH_DECIMAL",
            28: "COMMA",
            31: "ARROW_DELIMITER",
            34: "NEWLINE",
            35: "SEMI",
            36: "EOF",
            38: "STR",
            39: "MD_STR",
            41: "AMP",
            42: "NUM",
            43: "ALPHA",
            44: "PLUS",
            45: "EQUALS",
            46: "MULT",
            47: "DOT",
            48: "BRKT",
            49: "MINUS",
            50: "UNDERSCORE"
        },
        productions_: [
            0,
            [
                3,
                2
            ],
            [
                3,
                3
            ],
            [
                3,
                2
            ],
            [
                3,
                1
            ],
            [
                6,
                1
            ],
            [
                7,
                0
            ],
            [
                7,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                3
            ],
            [
                9,
                2
            ],
            [
                9,
                3
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                1
            ],
            [
                17,
                3
            ],
            [
                25,
                3
            ],
            [
                25,
                1
            ],
            [
                13,
                1
            ],
            [
                13,
                2
            ],
            [
                13,
                1
            ],
            [
                29,
                1
            ],
            [
                29,
                3
            ],
            [
                30,
                3
            ],
            [
                32,
                3
            ],
            [
                32,
                1
            ],
            [
                15,
                1
            ],
            [
                15,
                2
            ],
            [
                15,
                1
            ],
            [
                33,
                3
            ],
            [
                4,
                1
            ],
            [
                4,
                1
            ],
            [
                4,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                37,
                1
            ],
            [
                37,
                2
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ],
            [
                40,
                1
            ]
        ],
        performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 5:
                    yy.setOrientation($$[$0]);
                    break;
                case 9:
                    yy.setDiagramTitle($$[$0].text.trim());
                    break;
                case 12:
                    yy.setLineData({
                        text: "",
                        type: "text"
                    }, $$[$0]);
                    break;
                case 13:
                    yy.setLineData($$[$0 - 1], $$[$0]);
                    break;
                case 14:
                    yy.setBarData({
                        text: "",
                        type: "text"
                    }, $$[$0]);
                    break;
                case 15:
                    yy.setBarData($$[$0 - 1], $$[$0]);
                    break;
                case 16:
                    this.$ = $$[$0].trim();
                    yy.setAccTitle(this.$);
                    break;
                case 17:
                case 18:
                    this.$ = $$[$0].trim();
                    yy.setAccDescription(this.$);
                    break;
                case 19:
                    this.$ = $$[$0 - 1];
                    break;
                case 20:
                    this.$ = [
                        Number($$[$0 - 2]),
                        ...$$[$0]
                    ];
                    break;
                case 21:
                    this.$ = [
                        Number($$[$0])
                    ];
                    break;
                case 22:
                    yy.setXAxisTitle($$[$0]);
                    break;
                case 23:
                    yy.setXAxisTitle($$[$0 - 1]);
                    break;
                case 24:
                    yy.setXAxisTitle({
                        type: "text",
                        text: ""
                    });
                    break;
                case 25:
                    yy.setXAxisBand($$[$0]);
                    break;
                case 26:
                    yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));
                    break;
                case 27:
                    this.$ = $$[$0 - 1];
                    break;
                case 28:
                    this.$ = [
                        $$[$0 - 2],
                        ...$$[$0]
                    ];
                    break;
                case 29:
                    this.$ = [
                        $$[$0]
                    ];
                    break;
                case 30:
                    yy.setYAxisTitle($$[$0]);
                    break;
                case 31:
                    yy.setYAxisTitle($$[$0 - 1]);
                    break;
                case 32:
                    yy.setYAxisTitle({
                        type: "text",
                        text: ""
                    });
                    break;
                case 33:
                    yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));
                    break;
                case 37:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 38:
                    this.$ = {
                        text: $$[$0],
                        type: "text"
                    };
                    break;
                case 39:
                    this.$ = {
                        text: $$[$0],
                        type: "markdown"
                    };
                    break;
                case 40:
                    this.$ = $$[$0];
                    break;
                case 41:
                    this.$ = $$[$0 - 1] + "" + $$[$0];
                    break;
            }
        }, "anonymous"),
        table: [
            o($V0, $V1, {
                3: 1,
                4: 2,
                7: 4,
                5: $V2,
                34: $V3,
                35: $V4,
                36: $V5
            }),
            {
                1: [
                    3
                ]
            },
            o($V0, $V1, {
                4: 2,
                7: 4,
                3: 8,
                5: $V2,
                34: $V3,
                35: $V4,
                36: $V5
            }),
            o($V0, $V1, {
                4: 2,
                7: 4,
                6: 9,
                3: 10,
                5: $V2,
                8: [
                    1,
                    11
                ],
                34: $V3,
                35: $V4,
                36: $V5
            }),
            {
                1: [
                    2,
                    4
                ],
                9: 12,
                10: [
                    1,
                    13
                ],
                12: [
                    1,
                    14
                ],
                14: [
                    1,
                    15
                ],
                16: [
                    1,
                    16
                ],
                18: [
                    1,
                    17
                ],
                19: [
                    1,
                    18
                ],
                21: [
                    1,
                    19
                ],
                23: [
                    1,
                    20
                ]
            },
            o($V6, [
                2,
                34
            ]),
            o($V6, [
                2,
                35
            ]),
            o($V6, [
                2,
                36
            ]),
            {
                1: [
                    2,
                    1
                ]
            },
            o($V0, $V1, {
                4: 2,
                7: 4,
                3: 21,
                5: $V2,
                34: $V3,
                35: $V4,
                36: $V5
            }),
            {
                1: [
                    2,
                    3
                ]
            },
            o($V6, [
                2,
                5
            ]),
            o($V0, [
                2,
                7
            ], {
                4: 22,
                34: $V3,
                35: $V4,
                36: $V5
            }),
            {
                11: 23,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            {
                11: 39,
                13: 38,
                24: $Vj,
                27: $Vk,
                29: 40,
                30: 41,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            {
                11: 45,
                15: 44,
                27: $Vl,
                33: 46,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            {
                11: 49,
                17: 48,
                24: $Vm,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            {
                11: 52,
                17: 51,
                24: $Vm,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            {
                20: [
                    1,
                    53
                ]
            },
            {
                22: [
                    1,
                    54
                ]
            },
            o($Vn, [
                2,
                18
            ]),
            {
                1: [
                    2,
                    2
                ]
            },
            o($Vn, [
                2,
                8
            ]),
            o($Vn, [
                2,
                9
            ]),
            o($Vo, [
                2,
                37
            ], {
                40: 55,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            }),
            o($Vo, [
                2,
                38
            ]),
            o($Vo, [
                2,
                39
            ]),
            o($Vp, [
                2,
                40
            ]),
            o($Vp, [
                2,
                42
            ]),
            o($Vp, [
                2,
                43
            ]),
            o($Vp, [
                2,
                44
            ]),
            o($Vp, [
                2,
                45
            ]),
            o($Vp, [
                2,
                46
            ]),
            o($Vp, [
                2,
                47
            ]),
            o($Vp, [
                2,
                48
            ]),
            o($Vp, [
                2,
                49
            ]),
            o($Vp, [
                2,
                50
            ]),
            o($Vp, [
                2,
                51
            ]),
            o($Vn, [
                2,
                10
            ]),
            o($Vn, [
                2,
                22
            ], {
                30: 41,
                29: 56,
                24: $Vj,
                27: $Vk
            }),
            o($Vn, [
                2,
                24
            ]),
            o($Vn, [
                2,
                25
            ]),
            {
                31: [
                    1,
                    57
                ]
            },
            {
                11: 59,
                32: 58,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            o($Vn, [
                2,
                11
            ]),
            o($Vn, [
                2,
                30
            ], {
                33: 60,
                27: $Vl
            }),
            o($Vn, [
                2,
                32
            ]),
            {
                31: [
                    1,
                    61
                ]
            },
            o($Vn, [
                2,
                12
            ]),
            {
                17: 62,
                24: $Vm
            },
            {
                25: 63,
                27: $Vq
            },
            o($Vn, [
                2,
                14
            ]),
            {
                17: 65,
                24: $Vm
            },
            o($Vn, [
                2,
                16
            ]),
            o($Vn, [
                2,
                17
            ]),
            o($Vp, [
                2,
                41
            ]),
            o($Vn, [
                2,
                23
            ]),
            {
                27: [
                    1,
                    66
                ]
            },
            {
                26: [
                    1,
                    67
                ]
            },
            {
                26: [
                    2,
                    29
                ],
                28: [
                    1,
                    68
                ]
            },
            o($Vn, [
                2,
                31
            ]),
            {
                27: [
                    1,
                    69
                ]
            },
            o($Vn, [
                2,
                13
            ]),
            {
                26: [
                    1,
                    70
                ]
            },
            {
                26: [
                    2,
                    21
                ],
                28: [
                    1,
                    71
                ]
            },
            o($Vn, [
                2,
                15
            ]),
            o($Vn, [
                2,
                26
            ]),
            o($Vn, [
                2,
                27
            ]),
            {
                11: 59,
                32: 72,
                37: 24,
                38: $V7,
                39: $V8,
                40: 27,
                41: $V9,
                42: $Va,
                43: $Vb,
                44: $Vc,
                45: $Vd,
                46: $Ve,
                47: $Vf,
                48: $Vg,
                49: $Vh,
                50: $Vi
            },
            o($Vn, [
                2,
                33
            ]),
            o($Vn, [
                2,
                19
            ]),
            {
                25: 73,
                27: $Vq
            },
            {
                26: [
                    2,
                    28
                ]
            },
            {
                26: [
                    2,
                    20
                ]
            }
        ],
        defaultActions: {
            8: [
                2,
                1
            ],
            10: [
                2,
                3
            ],
            21: [
                2,
                2
            ],
            72: [
                2,
                28
            ],
            73: [
                2,
                20
            ]
        },
        parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        }, "parseError"),
        parse: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function popStack(n) {
                stack.length = stack.length - 2 * n;
                vstack.length = vstack.length - n;
                lstack.length = lstack.length - n;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(popStack, "popStack");
            function lex() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(lex, "lex");
            var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        if (!preErrorSymbol) {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                            if (recovering > 0) {
                                recovering--;
                            }
                        } else {
                            symbol = preErrorSymbol;
                            preErrorSymbol = null;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }, "parse")
    };
    var lexer = /* @__PURE__ */ function() {
        var lexer2 = {
            EOF: 1,
            parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            }, "parseError"),
            // resets the lexer, sets new input
            setInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            }, "setInput"),
            // consumes and returns one char from the input
            input: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            }, "input"),
            // unshifts one char (or a string) into the input
            unput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            }, "unput"),
            // When called from action, caches matched text and appends it on next action
            more: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                this._more = true;
                return this;
            }, "more"),
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            }, "reject"),
            // retain first n characters of the match
            less: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(n) {
                this.unput(this.match.slice(n));
            }, "less"),
            // displays already matched input, i.e. for error messages
            pastInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            }, "pastInput"),
            // displays upcoming input, i.e. for error messages
            upcomingInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            }, "upcomingInput"),
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            }, "showPosition"),
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            }, "test_match"),
            // return next match in input
            next: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            }, "next"),
            // return next match that has a token
            lex: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            }, "lex"),
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function begin(condition) {
                this.conditionStack.push(condition);
            }, "begin"),
            // pop the previously active lexer condition state off the condition stack
            popState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            }, "popState"),
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            }, "_currentRules"),
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            }, "topState"),
            // alias for begin(condition)
            pushState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function pushState(condition) {
                this.begin(condition);
            }, "pushState"),
            // return the number of states currently on the stack
            stateStackSize: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function stateStackSize() {
                return this.conditionStack.length;
            }, "stateStackSize"),
            options: {
                "case-insensitive": true
            },
            performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                var YYSTATE = YY_START;
                switch($avoiding_name_collisions){
                    case 0:
                        break;
                    case 1:
                        break;
                    case 2:
                        this.popState();
                        return 34;
                        //TURBOPACK unreachable
                        ;
                    case 3:
                        this.popState();
                        return 34;
                        //TURBOPACK unreachable
                        ;
                    case 4:
                        return 34;
                        //TURBOPACK unreachable
                        ;
                    case 5:
                        break;
                    case 6:
                        return 10;
                        //TURBOPACK unreachable
                        ;
                    case 7:
                        this.pushState("acc_title");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 8:
                        this.popState();
                        return "acc_title_value";
                        //TURBOPACK unreachable
                        ;
                    case 9:
                        this.pushState("acc_descr");
                        return 21;
                        //TURBOPACK unreachable
                        ;
                    case 10:
                        this.popState();
                        return "acc_descr_value";
                        //TURBOPACK unreachable
                        ;
                    case 11:
                        this.pushState("acc_descr_multiline");
                        break;
                    case 12:
                        this.popState();
                        break;
                    case 13:
                        return "acc_descr_multiline_value";
                        //TURBOPACK unreachable
                        ;
                    case 14:
                        return 5;
                        //TURBOPACK unreachable
                        ;
                    case 15:
                        return 8;
                        //TURBOPACK unreachable
                        ;
                    case 16:
                        this.pushState("axis_data");
                        return "X_AXIS";
                        //TURBOPACK unreachable
                        ;
                    case 17:
                        this.pushState("axis_data");
                        return "Y_AXIS";
                        //TURBOPACK unreachable
                        ;
                    case 18:
                        this.pushState("axis_band_data");
                        return 24;
                        //TURBOPACK unreachable
                        ;
                    case 19:
                        return 31;
                        //TURBOPACK unreachable
                        ;
                    case 20:
                        this.pushState("data");
                        return 16;
                        //TURBOPACK unreachable
                        ;
                    case 21:
                        this.pushState("data");
                        return 18;
                        //TURBOPACK unreachable
                        ;
                    case 22:
                        this.pushState("data_inner");
                        return 24;
                        //TURBOPACK unreachable
                        ;
                    case 23:
                        return 27;
                        //TURBOPACK unreachable
                        ;
                    case 24:
                        this.popState();
                        return 26;
                        //TURBOPACK unreachable
                        ;
                    case 25:
                        this.popState();
                        break;
                    case 26:
                        this.pushState("string");
                        break;
                    case 27:
                        this.popState();
                        break;
                    case 28:
                        return "STR";
                        //TURBOPACK unreachable
                        ;
                    case 29:
                        return 24;
                        //TURBOPACK unreachable
                        ;
                    case 30:
                        return 26;
                        //TURBOPACK unreachable
                        ;
                    case 31:
                        return 43;
                        //TURBOPACK unreachable
                        ;
                    case 32:
                        return "COLON";
                        //TURBOPACK unreachable
                        ;
                    case 33:
                        return 44;
                        //TURBOPACK unreachable
                        ;
                    case 34:
                        return 28;
                        //TURBOPACK unreachable
                        ;
                    case 35:
                        return 45;
                        //TURBOPACK unreachable
                        ;
                    case 36:
                        return 46;
                        //TURBOPACK unreachable
                        ;
                    case 37:
                        return 48;
                        //TURBOPACK unreachable
                        ;
                    case 38:
                        return 50;
                        //TURBOPACK unreachable
                        ;
                    case 39:
                        return 47;
                        //TURBOPACK unreachable
                        ;
                    case 40:
                        return 41;
                        //TURBOPACK unreachable
                        ;
                    case 41:
                        return 49;
                        //TURBOPACK unreachable
                        ;
                    case 42:
                        return 42;
                        //TURBOPACK unreachable
                        ;
                    case 43:
                        break;
                    case 44:
                        return 35;
                        //TURBOPACK unreachable
                        ;
                    case 45:
                        return 36;
                        //TURBOPACK unreachable
                        ;
                }
            }, "anonymous"),
            rules: [
                /^(?:%%(?!\{)[^\n]*)/i,
                /^(?:[^\}]%%[^\n]*)/i,
                /^(?:(\r?\n))/i,
                /^(?:(\r?\n))/i,
                /^(?:[\n\r]+)/i,
                /^(?:%%[^\n]*)/i,
                /^(?:title\b)/i,
                /^(?:accTitle\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*\{\s*)/i,
                /^(?:\{)/i,
                /^(?:[^\}]*)/i,
                /^(?:xychart-beta\b)/i,
                /^(?:(?:vertical|horizontal))/i,
                /^(?:x-axis\b)/i,
                /^(?:y-axis\b)/i,
                /^(?:\[)/i,
                /^(?:-->)/i,
                /^(?:line\b)/i,
                /^(?:bar\b)/i,
                /^(?:\[)/i,
                /^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,
                /^(?:\])/i,
                /^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,
                /^(?:["])/i,
                /^(?:["])/i,
                /^(?:[^"]*)/i,
                /^(?:\[)/i,
                /^(?:\])/i,
                /^(?:[A-Za-z]+)/i,
                /^(?::)/i,
                /^(?:\+)/i,
                /^(?:,)/i,
                /^(?:=)/i,
                /^(?:\*)/i,
                /^(?:#)/i,
                /^(?:[\_])/i,
                /^(?:\.)/i,
                /^(?:&)/i,
                /^(?:-)/i,
                /^(?:[0-9]+)/i,
                /^(?:\s+)/i,
                /^(?:;)/i,
                /^(?:$)/i
            ],
            conditions: {
                "data_inner": {
                    "rules": [
                        0,
                        1,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        20,
                        21,
                        23,
                        24,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45
                    ],
                    "inclusive": true
                },
                "data": {
                    "rules": [
                        0,
                        1,
                        3,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        20,
                        21,
                        22,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45
                    ],
                    "inclusive": true
                },
                "axis_band_data": {
                    "rules": [
                        0,
                        1,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        20,
                        21,
                        24,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45
                    ],
                    "inclusive": true
                },
                "axis_data": {
                    "rules": [
                        0,
                        1,
                        2,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        21,
                        23,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45
                    ],
                    "inclusive": true
                },
                "acc_descr_multiline": {
                    "rules": [
                        12,
                        13
                    ],
                    "inclusive": false
                },
                "acc_descr": {
                    "rules": [
                        10
                    ],
                    "inclusive": false
                },
                "acc_title": {
                    "rules": [
                        8
                    ],
                    "inclusive": false
                },
                "title": {
                    "rules": [],
                    "inclusive": false
                },
                "md_string": {
                    "rules": [],
                    "inclusive": false
                },
                "string": {
                    "rules": [
                        27,
                        28
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        1,
                        4,
                        5,
                        6,
                        7,
                        9,
                        11,
                        14,
                        15,
                        16,
                        17,
                        20,
                        21,
                        25,
                        26,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(Parser, "Parser");
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
var xychart_default = parser;
// src/diagrams/xychart/chartBuilder/interfaces.ts
function isBarPlot(data) {
    return data.type === "bar";
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(isBarPlot, "isBarPlot");
function isBandAxisData(data) {
    return data.type === "band";
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(isBandAxisData, "isBandAxisData");
function isLinearAxisData(data) {
    return data.type === "linear";
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(isLinearAxisData, "isLinearAxisData");
// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts
var TextDimensionCalculatorWithFont = (_class = class {
    getMaxDimension(texts, fontSize) {
        if (!this.parentGroup) {
            return {
                width: texts.reduce((acc, cur)=>Math.max(cur.length, acc), 0) * fontSize,
                height: fontSize
            };
        }
        const dimension = {
            width: 0,
            height: 0
        };
        const elem = this.parentGroup.append("g").attr("visibility", "hidden").attr("font-size", fontSize);
        for (const t of texts){
            const bbox = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$QESNASVV$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["computeDimensionOfText"])(elem, 1, t);
            const width = bbox ? bbox.width : t.length * fontSize;
            const height = bbox ? bbox.height : fontSize;
            dimension.width = Math.max(dimension.width, width);
            dimension.height = Math.max(dimension.height, height);
        }
        elem.remove();
        return dimension;
    }
    constructor(parentGroup){
        this.parentGroup = parentGroup;
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class, "TextDimensionCalculatorWithFont"), _class);
;
// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts
var BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;
var MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;
var BaseAxis = (_class1 = class {
    setRange(range) {
        this.range = range;
        if (this.axisPosition === "left" || this.axisPosition === "right") {
            this.boundingRect.height = range[1] - range[0];
        } else {
            this.boundingRect.width = range[1] - range[0];
        }
        this.recalculateScale();
    }
    getRange() {
        return [
            this.range[0] + this.outerPadding,
            this.range[1] - this.outerPadding
        ];
    }
    setAxisPosition(axisPosition) {
        this.axisPosition = axisPosition;
        this.setRange(this.range);
    }
    getTickDistance() {
        const range = this.getRange();
        return Math.abs(range[0] - range[1]) / this.getTickValues().length;
    }
    getAxisOuterPadding() {
        return this.outerPadding;
    }
    getLabelDimension() {
        return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map((tick)=>tick.toString()), this.axisConfig.labelFontSize);
    }
    recalculateOuterPaddingToDrawBar() {
        if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {
            this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);
        }
        this.recalculateScale();
    }
    calculateSpaceIfDrawnHorizontally(availableSpace) {
        let availableHeight = availableSpace.height;
        if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {
            availableHeight -= this.axisConfig.axisLineWidth;
            this.showAxisLine = true;
        }
        if (this.axisConfig.showLabel) {
            const spaceRequired = this.getLabelDimension();
            const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;
            this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);
            const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;
            this.labelTextHeight = spaceRequired.height;
            if (heightRequired <= availableHeight) {
                availableHeight -= heightRequired;
                this.showLabel = true;
            }
        }
        if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {
            this.showTick = true;
            availableHeight -= this.axisConfig.tickLength;
        }
        if (this.axisConfig.showTitle && this.title) {
            const spaceRequired = this.textDimensionCalculator.getMaxDimension([
                this.title
            ], this.axisConfig.titleFontSize);
            const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;
            this.titleTextHeight = spaceRequired.height;
            if (heightRequired <= availableHeight) {
                availableHeight -= heightRequired;
                this.showTitle = true;
            }
        }
        this.boundingRect.width = availableSpace.width;
        this.boundingRect.height = availableSpace.height - availableHeight;
    }
    calculateSpaceIfDrawnVertical(availableSpace) {
        let availableWidth = availableSpace.width;
        if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {
            availableWidth -= this.axisConfig.axisLineWidth;
            this.showAxisLine = true;
        }
        if (this.axisConfig.showLabel) {
            const spaceRequired = this.getLabelDimension();
            const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;
            this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);
            const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;
            if (widthRequired <= availableWidth) {
                availableWidth -= widthRequired;
                this.showLabel = true;
            }
        }
        if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {
            this.showTick = true;
            availableWidth -= this.axisConfig.tickLength;
        }
        if (this.axisConfig.showTitle && this.title) {
            const spaceRequired = this.textDimensionCalculator.getMaxDimension([
                this.title
            ], this.axisConfig.titleFontSize);
            const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;
            this.titleTextHeight = spaceRequired.height;
            if (widthRequired <= availableWidth) {
                availableWidth -= widthRequired;
                this.showTitle = true;
            }
        }
        this.boundingRect.width = availableSpace.width - availableWidth;
        this.boundingRect.height = availableSpace.height;
    }
    calculateSpace(availableSpace) {
        if (this.axisPosition === "left" || this.axisPosition === "right") {
            this.calculateSpaceIfDrawnVertical(availableSpace);
        } else {
            this.calculateSpaceIfDrawnHorizontally(availableSpace);
        }
        this.recalculateScale();
        return {
            width: this.boundingRect.width,
            height: this.boundingRect.height
        };
    }
    setBoundingBoxXY(point) {
        this.boundingRect.x = point.x;
        this.boundingRect.y = point.y;
    }
    getDrawableElementsForLeftAxis() {
        const drawableElement = [];
        if (this.showAxisLine) {
            const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "left-axis",
                    "axisl-line"
                ],
                data: [
                    {
                        path: "M ".concat(x, ",").concat(this.boundingRect.y, " L ").concat(x, ",").concat(this.boundingRect.y + this.boundingRect.height, " "),
                        strokeFill: this.axisThemeConfig.axisLineColor,
                        strokeWidth: this.axisConfig.axisLineWidth
                    }
                ]
            });
        }
        if (this.showLabel) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "left-axis",
                    "label"
                ],
                data: this.getTickValues().map((tick)=>({
                        text: tick.toString(),
                        x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),
                        y: this.getScaleValue(tick),
                        fill: this.axisThemeConfig.labelColor,
                        fontSize: this.axisConfig.labelFontSize,
                        rotation: 0,
                        verticalPos: "middle",
                        horizontalPos: "right"
                    }))
            });
        }
        if (this.showTick) {
            const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "left-axis",
                    "ticks"
                ],
                data: this.getTickValues().map((tick)=>({
                        path: "M ".concat(x, ",").concat(this.getScaleValue(tick), " L ").concat(x - this.axisConfig.tickLength, ",").concat(this.getScaleValue(tick)),
                        strokeFill: this.axisThemeConfig.tickColor,
                        strokeWidth: this.axisConfig.tickWidth
                    }))
            });
        }
        if (this.showTitle) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "left-axis",
                    "title"
                ],
                data: [
                    {
                        text: this.title,
                        x: this.boundingRect.x + this.axisConfig.titlePadding,
                        y: this.boundingRect.y + this.boundingRect.height / 2,
                        fill: this.axisThemeConfig.titleColor,
                        fontSize: this.axisConfig.titleFontSize,
                        rotation: 270,
                        verticalPos: "top",
                        horizontalPos: "center"
                    }
                ]
            });
        }
        return drawableElement;
    }
    getDrawableElementsForBottomAxis() {
        const drawableElement = [];
        if (this.showAxisLine) {
            const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "bottom-axis",
                    "axis-line"
                ],
                data: [
                    {
                        path: "M ".concat(this.boundingRect.x, ",").concat(y, " L ").concat(this.boundingRect.x + this.boundingRect.width, ",").concat(y),
                        strokeFill: this.axisThemeConfig.axisLineColor,
                        strokeWidth: this.axisConfig.axisLineWidth
                    }
                ]
            });
        }
        if (this.showLabel) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "bottom-axis",
                    "label"
                ],
                data: this.getTickValues().map((tick)=>({
                        text: tick.toString(),
                        x: this.getScaleValue(tick),
                        y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),
                        fill: this.axisThemeConfig.labelColor,
                        fontSize: this.axisConfig.labelFontSize,
                        rotation: 0,
                        verticalPos: "top",
                        horizontalPos: "center"
                    }))
            });
        }
        if (this.showTick) {
            const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "bottom-axis",
                    "ticks"
                ],
                data: this.getTickValues().map((tick)=>({
                        path: "M ".concat(this.getScaleValue(tick), ",").concat(y, " L ").concat(this.getScaleValue(tick), ",").concat(y + this.axisConfig.tickLength),
                        strokeFill: this.axisThemeConfig.tickColor,
                        strokeWidth: this.axisConfig.tickWidth
                    }))
            });
        }
        if (this.showTitle) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "bottom-axis",
                    "title"
                ],
                data: [
                    {
                        text: this.title,
                        x: this.range[0] + (this.range[1] - this.range[0]) / 2,
                        y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,
                        fill: this.axisThemeConfig.titleColor,
                        fontSize: this.axisConfig.titleFontSize,
                        rotation: 0,
                        verticalPos: "top",
                        horizontalPos: "center"
                    }
                ]
            });
        }
        return drawableElement;
    }
    getDrawableElementsForTopAxis() {
        const drawableElement = [];
        if (this.showAxisLine) {
            const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "top-axis",
                    "axis-line"
                ],
                data: [
                    {
                        path: "M ".concat(this.boundingRect.x, ",").concat(y, " L ").concat(this.boundingRect.x + this.boundingRect.width, ",").concat(y),
                        strokeFill: this.axisThemeConfig.axisLineColor,
                        strokeWidth: this.axisConfig.axisLineWidth
                    }
                ]
            });
        }
        if (this.showLabel) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "top-axis",
                    "label"
                ],
                data: this.getTickValues().map((tick)=>({
                        text: tick.toString(),
                        x: this.getScaleValue(tick),
                        y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,
                        fill: this.axisThemeConfig.labelColor,
                        fontSize: this.axisConfig.labelFontSize,
                        rotation: 0,
                        verticalPos: "top",
                        horizontalPos: "center"
                    }))
            });
        }
        if (this.showTick) {
            const y = this.boundingRect.y;
            drawableElement.push({
                type: "path",
                groupTexts: [
                    "top-axis",
                    "ticks"
                ],
                data: this.getTickValues().map((tick)=>({
                        path: "M ".concat(this.getScaleValue(tick), ",").concat(y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0), " L ").concat(this.getScaleValue(tick), ",").concat(y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)),
                        strokeFill: this.axisThemeConfig.tickColor,
                        strokeWidth: this.axisConfig.tickWidth
                    }))
            });
        }
        if (this.showTitle) {
            drawableElement.push({
                type: "text",
                groupTexts: [
                    "top-axis",
                    "title"
                ],
                data: [
                    {
                        text: this.title,
                        x: this.boundingRect.x + this.boundingRect.width / 2,
                        y: this.boundingRect.y + this.axisConfig.titlePadding,
                        fill: this.axisThemeConfig.titleColor,
                        fontSize: this.axisConfig.titleFontSize,
                        rotation: 0,
                        verticalPos: "top",
                        horizontalPos: "center"
                    }
                ]
            });
        }
        return drawableElement;
    }
    getDrawableElements() {
        if (this.axisPosition === "left") {
            return this.getDrawableElementsForLeftAxis();
        }
        if (this.axisPosition === "right") {
            throw Error("Drawing of right axis is not implemented");
        }
        if (this.axisPosition === "bottom") {
            return this.getDrawableElementsForBottomAxis();
        }
        if (this.axisPosition === "top") {
            return this.getDrawableElementsForTopAxis();
        }
        return [];
    }
    constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig){
        this.axisConfig = axisConfig;
        this.title = title;
        this.textDimensionCalculator = textDimensionCalculator;
        this.axisThemeConfig = axisThemeConfig;
        this.boundingRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        this.axisPosition = "left";
        this.showTitle = false;
        this.showLabel = false;
        this.showTick = false;
        this.showAxisLine = false;
        this.outerPadding = 0;
        this.titleTextHeight = 0;
        this.labelTextHeight = 0;
        this.range = [
            0,
            10
        ];
        this.boundingRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        this.axisPosition = "left";
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class1, "BaseAxis"), _class1);
// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts
var BandAxis = (_class2 = class extends BaseAxis {
    setRange(range) {
        super.setRange(range);
    }
    recalculateScale() {
        this.scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$band$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleBand$3e$__["scaleBand"])().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].trace("BandAxis axis final categories, range: ", this.categories, this.getRange());
    }
    getTickValues() {
        return this.categories;
    }
    getScaleValue(value) {
        var _this_scale;
        return (_this_scale = this.scale(value)) !== null && _this_scale !== void 0 ? _this_scale : this.getRange()[0];
    }
    constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator){
        super(axisConfig, title, textDimensionCalculator, axisThemeConfig);
        this.categories = categories;
        this.scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$band$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleBand$3e$__["scaleBand"])().domain(this.categories).range(this.getRange());
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class2, "BandAxis"), _class2);
;
var LinearAxis = (_class3 = class extends BaseAxis {
    getTickValues() {
        return this.scale.ticks();
    }
    recalculateScale() {
        const domain = [
            ...this.domain
        ];
        if (this.axisPosition === "left") {
            domain.reverse();
        }
        this.scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain(domain).range(this.getRange());
    }
    getScaleValue(value) {
        return this.scale(value);
    }
    constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator){
        super(axisConfig, title, textDimensionCalculator, axisThemeConfig);
        this.domain = domain;
        this.scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain(this.domain).range(this.getRange());
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class3, "LinearAxis"), _class3);
// src/diagrams/xychart/chartBuilder/components/axis/index.ts
function getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {
    const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);
    if (isBandAxisData(data)) {
        return new BandAxis(axisConfig, axisThemeConfig, data.categories, data.title, textDimensionCalculator);
    }
    return new LinearAxis(axisConfig, axisThemeConfig, [
        data.min,
        data.max
    ], data.title, textDimensionCalculator);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getAxis, "getAxis");
// src/diagrams/xychart/chartBuilder/components/chartTitle.ts
var ChartTitle = (_class4 = class {
    setBoundingBoxXY(point) {
        this.boundingRect.x = point.x;
        this.boundingRect.y = point.y;
    }
    calculateSpace(availableSpace) {
        const titleDimension = this.textDimensionCalculator.getMaxDimension([
            this.chartData.title
        ], this.chartConfig.titleFontSize);
        const widthRequired = Math.max(titleDimension.width, availableSpace.width);
        const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;
        if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {
            this.boundingRect.width = widthRequired;
            this.boundingRect.height = heightRequired;
            this.showChartTitle = true;
        }
        return {
            width: this.boundingRect.width,
            height: this.boundingRect.height
        };
    }
    getDrawableElements() {
        const drawableElem = [];
        if (this.showChartTitle) {
            drawableElem.push({
                groupTexts: [
                    "chart-title"
                ],
                type: "text",
                data: [
                    {
                        fontSize: this.chartConfig.titleFontSize,
                        text: this.chartData.title,
                        verticalPos: "middle",
                        horizontalPos: "center",
                        x: this.boundingRect.x + this.boundingRect.width / 2,
                        y: this.boundingRect.y + this.boundingRect.height / 2,
                        fill: this.chartThemeConfig.titleColor,
                        rotation: 0
                    }
                ]
            });
        }
        return drawableElem;
    }
    constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig){
        this.textDimensionCalculator = textDimensionCalculator;
        this.chartConfig = chartConfig;
        this.chartData = chartData;
        this.chartThemeConfig = chartThemeConfig;
        this.boundingRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        this.showChartTitle = false;
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class4, "ChartTitle"), _class4);
function getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {
    const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);
    return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartTitleComponent, "getChartTitleComponent");
;
var LinePlot = (_class5 = class {
    getDrawableElement() {
        const finalData = this.plotData.data.map((d)=>[
                this.xAxis.getScaleValue(d[0]),
                this.yAxis.getScaleValue(d[1])
            ]);
        let path;
        if (this.orientation === "horizontal") {
            path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__line$3e$__["line"])().y((d)=>d[0]).x((d)=>d[1])(finalData);
        } else {
            path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$shape$2f$src$2f$line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__line$3e$__["line"])().x((d)=>d[0]).y((d)=>d[1])(finalData);
        }
        if (!path) {
            return [];
        }
        return [
            {
                groupTexts: [
                    "plot",
                    "line-plot-".concat(this.plotIndex)
                ],
                type: "path",
                data: [
                    {
                        path,
                        strokeFill: this.plotData.strokeFill,
                        strokeWidth: this.plotData.strokeWidth
                    }
                ]
            }
        ];
    }
    constructor(plotData, xAxis, yAxis, orientation, plotIndex2){
        this.plotData = plotData;
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        this.orientation = orientation;
        this.plotIndex = plotIndex2;
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class5, "LinePlot"), _class5);
// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts
var BarPlot = (_class6 = class {
    getDrawableElement() {
        const finalData = this.barData.data.map((d)=>[
                this.xAxis.getScaleValue(d[0]),
                this.yAxis.getScaleValue(d[1])
            ]);
        const barPaddingPercent = 0.05;
        const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);
        const barWidthHalf = barWidth / 2;
        if (this.orientation === "horizontal") {
            return [
                {
                    groupTexts: [
                        "plot",
                        "bar-plot-".concat(this.plotIndex)
                    ],
                    type: "rect",
                    data: finalData.map((data)=>({
                            x: this.boundingRect.x,
                            y: data[0] - barWidthHalf,
                            height: barWidth,
                            width: data[1] - this.boundingRect.x,
                            fill: this.barData.fill,
                            strokeWidth: 0,
                            strokeFill: this.barData.fill
                        }))
                }
            ];
        }
        return [
            {
                groupTexts: [
                    "plot",
                    "bar-plot-".concat(this.plotIndex)
                ],
                type: "rect",
                data: finalData.map((data)=>({
                        x: data[0] - barWidthHalf,
                        y: data[1],
                        width: barWidth,
                        height: this.boundingRect.y + this.boundingRect.height - data[1],
                        fill: this.barData.fill,
                        strokeWidth: 0,
                        strokeFill: this.barData.fill
                    }))
            }
        ];
    }
    constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2){
        this.barData = barData;
        this.boundingRect = boundingRect;
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        this.orientation = orientation;
        this.plotIndex = plotIndex2;
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class6, "BarPlot"), _class6);
// src/diagrams/xychart/chartBuilder/components/plot/index.ts
var BasePlot = (_class7 = class {
    setAxes(xAxis, yAxis) {
        this.xAxis = xAxis;
        this.yAxis = yAxis;
    }
    setBoundingBoxXY(point) {
        this.boundingRect.x = point.x;
        this.boundingRect.y = point.y;
    }
    calculateSpace(availableSpace) {
        this.boundingRect.width = availableSpace.width;
        this.boundingRect.height = availableSpace.height;
        return {
            width: this.boundingRect.width,
            height: this.boundingRect.height
        };
    }
    getDrawableElements() {
        if (!(this.xAxis && this.yAxis)) {
            throw Error("Axes must be passed to render Plots");
        }
        const drawableElem = [];
        for (const [i, plot] of this.chartData.plots.entries()){
            switch(plot.type){
                case "line":
                    {
                        const linePlot = new LinePlot(plot, this.xAxis, this.yAxis, this.chartConfig.chartOrientation, i);
                        drawableElem.push(...linePlot.getDrawableElement());
                    }
                    break;
                case "bar":
                    {
                        const barPlot = new BarPlot(plot, this.boundingRect, this.xAxis, this.yAxis, this.chartConfig.chartOrientation, i);
                        drawableElem.push(...barPlot.getDrawableElement());
                    }
                    break;
            }
        }
        return drawableElem;
    }
    constructor(chartConfig, chartData, chartThemeConfig){
        this.chartConfig = chartConfig;
        this.chartData = chartData;
        this.chartThemeConfig = chartThemeConfig;
        this.boundingRect = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class7, "BasePlot"), _class7);
function getPlotComponent(chartConfig, chartData, chartThemeConfig) {
    return new BasePlot(chartConfig, chartData, chartThemeConfig);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getPlotComponent, "getPlotComponent");
// src/diagrams/xychart/chartBuilder/orchestrator.ts
var Orchestrator = (_class8 = class {
    calculateVerticalSpace() {
        let availableWidth = this.chartConfig.width;
        let availableHeight = this.chartConfig.height;
        let plotX = 0;
        let plotY = 0;
        let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);
        let chartHeight = Math.floor(availableHeight * this.chartConfig.plotReservedSpacePercent / 100);
        let spaceUsed = this.componentStore.plot.calculateSpace({
            width: chartWidth,
            height: chartHeight
        });
        availableWidth -= spaceUsed.width;
        availableHeight -= spaceUsed.height;
        spaceUsed = this.componentStore.title.calculateSpace({
            width: this.chartConfig.width,
            height: availableHeight
        });
        plotY = spaceUsed.height;
        availableHeight -= spaceUsed.height;
        this.componentStore.xAxis.setAxisPosition("bottom");
        spaceUsed = this.componentStore.xAxis.calculateSpace({
            width: availableWidth,
            height: availableHeight
        });
        availableHeight -= spaceUsed.height;
        this.componentStore.yAxis.setAxisPosition("left");
        spaceUsed = this.componentStore.yAxis.calculateSpace({
            width: availableWidth,
            height: availableHeight
        });
        plotX = spaceUsed.width;
        availableWidth -= spaceUsed.width;
        if (availableWidth > 0) {
            chartWidth += availableWidth;
            availableWidth = 0;
        }
        if (availableHeight > 0) {
            chartHeight += availableHeight;
            availableHeight = 0;
        }
        this.componentStore.plot.calculateSpace({
            width: chartWidth,
            height: chartHeight
        });
        this.componentStore.plot.setBoundingBoxXY({
            x: plotX,
            y: plotY
        });
        this.componentStore.xAxis.setRange([
            plotX,
            plotX + chartWidth
        ]);
        this.componentStore.xAxis.setBoundingBoxXY({
            x: plotX,
            y: plotY + chartHeight
        });
        this.componentStore.yAxis.setRange([
            plotY,
            plotY + chartHeight
        ]);
        this.componentStore.yAxis.setBoundingBoxXY({
            x: 0,
            y: plotY
        });
        if (this.chartData.plots.some((p)=>isBarPlot(p))) {
            this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();
        }
    }
    calculateHorizontalSpace() {
        let availableWidth = this.chartConfig.width;
        let availableHeight = this.chartConfig.height;
        let titleYEnd = 0;
        let plotX = 0;
        let plotY = 0;
        let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);
        let chartHeight = Math.floor(availableHeight * this.chartConfig.plotReservedSpacePercent / 100);
        let spaceUsed = this.componentStore.plot.calculateSpace({
            width: chartWidth,
            height: chartHeight
        });
        availableWidth -= spaceUsed.width;
        availableHeight -= spaceUsed.height;
        spaceUsed = this.componentStore.title.calculateSpace({
            width: this.chartConfig.width,
            height: availableHeight
        });
        titleYEnd = spaceUsed.height;
        availableHeight -= spaceUsed.height;
        this.componentStore.xAxis.setAxisPosition("left");
        spaceUsed = this.componentStore.xAxis.calculateSpace({
            width: availableWidth,
            height: availableHeight
        });
        availableWidth -= spaceUsed.width;
        plotX = spaceUsed.width;
        this.componentStore.yAxis.setAxisPosition("top");
        spaceUsed = this.componentStore.yAxis.calculateSpace({
            width: availableWidth,
            height: availableHeight
        });
        availableHeight -= spaceUsed.height;
        plotY = titleYEnd + spaceUsed.height;
        if (availableWidth > 0) {
            chartWidth += availableWidth;
            availableWidth = 0;
        }
        if (availableHeight > 0) {
            chartHeight += availableHeight;
            availableHeight = 0;
        }
        this.componentStore.plot.calculateSpace({
            width: chartWidth,
            height: chartHeight
        });
        this.componentStore.plot.setBoundingBoxXY({
            x: plotX,
            y: plotY
        });
        this.componentStore.yAxis.setRange([
            plotX,
            plotX + chartWidth
        ]);
        this.componentStore.yAxis.setBoundingBoxXY({
            x: plotX,
            y: titleYEnd
        });
        this.componentStore.xAxis.setRange([
            plotY,
            plotY + chartHeight
        ]);
        this.componentStore.xAxis.setBoundingBoxXY({
            x: 0,
            y: plotY
        });
        if (this.chartData.plots.some((p)=>isBarPlot(p))) {
            this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();
        }
    }
    calculateSpace() {
        if (this.chartConfig.chartOrientation === "horizontal") {
            this.calculateHorizontalSpace();
        } else {
            this.calculateVerticalSpace();
        }
    }
    getDrawableElement() {
        this.calculateSpace();
        const drawableElem = [];
        this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);
        for (const component of Object.values(this.componentStore)){
            drawableElem.push(...component.getDrawableElements());
        }
        return drawableElem;
    }
    constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2){
        this.chartConfig = chartConfig;
        this.chartData = chartData;
        this.componentStore = {
            title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),
            plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),
            xAxis: getAxis(chartData.xAxis, chartConfig.xAxis, {
                titleColor: chartThemeConfig.xAxisTitleColor,
                labelColor: chartThemeConfig.xAxisLabelColor,
                tickColor: chartThemeConfig.xAxisTickColor,
                axisLineColor: chartThemeConfig.xAxisLineColor
            }, tmpSVGGroup2),
            yAxis: getAxis(chartData.yAxis, chartConfig.yAxis, {
                titleColor: chartThemeConfig.yAxisTitleColor,
                labelColor: chartThemeConfig.yAxisLabelColor,
                tickColor: chartThemeConfig.yAxisTickColor,
                axisLineColor: chartThemeConfig.yAxisLineColor
            }, tmpSVGGroup2)
        };
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class8, "Orchestrator"), _class8);
// src/diagrams/xychart/chartBuilder/index.ts
var XYChartBuilder = (_class9 = class {
    static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {
        const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);
        return orchestrator.getDrawableElement();
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class9, "XYChartBuilder"), _class9);
// src/diagrams/xychart/xychartDb.ts
var plotIndex = 0;
var tmpSVGGroup;
var xyChartConfig = getChartDefaultConfig();
var xyChartThemeConfig = getChartDefaultThemeConfig();
var xyChartData = getChartDefaultData();
var plotColorPalette = xyChartThemeConfig.plotColorPalette.split(",").map((color)=>color.trim());
var hasSetXAxis = false;
var hasSetYAxis = false;
function getChartDefaultThemeConfig() {
    const defaultThemeVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getThemeVariables"])();
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanAndMerge"])(defaultThemeVariables.xyChart, config.themeVariables.xyChart);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartDefaultThemeConfig, "getChartDefaultThemeConfig");
function getChartDefaultConfig() {
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanAndMerge"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].xyChart, config.xyChart);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartDefaultConfig, "getChartDefaultConfig");
function getChartDefaultData() {
    return {
        yAxis: {
            type: "linear",
            title: "",
            min: Infinity,
            max: -Infinity
        },
        xAxis: {
            type: "band",
            title: "",
            categories: []
        },
        title: "",
        plots: []
    };
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartDefaultData, "getChartDefaultData");
function textSanitizer(text) {
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeText"])(text.trim(), config);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(textSanitizer, "textSanitizer");
function setTmpSVGG(SVGG) {
    tmpSVGGroup = SVGG;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setTmpSVGG, "setTmpSVGG");
function setOrientation(orientation) {
    if (orientation === "horizontal") {
        xyChartConfig.chartOrientation = "horizontal";
    } else {
        xyChartConfig.chartOrientation = "vertical";
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setOrientation, "setOrientation");
function setXAxisTitle(title) {
    xyChartData.xAxis.title = textSanitizer(title.text);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setXAxisTitle, "setXAxisTitle");
function setXAxisRangeData(min, max) {
    xyChartData.xAxis = {
        type: "linear",
        title: xyChartData.xAxis.title,
        min,
        max
    };
    hasSetXAxis = true;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setXAxisRangeData, "setXAxisRangeData");
function setXAxisBand(categories) {
    xyChartData.xAxis = {
        type: "band",
        title: xyChartData.xAxis.title,
        categories: categories.map((c)=>textSanitizer(c.text))
    };
    hasSetXAxis = true;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setXAxisBand, "setXAxisBand");
function setYAxisTitle(title) {
    xyChartData.yAxis.title = textSanitizer(title.text);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setYAxisTitle, "setYAxisTitle");
function setYAxisRangeData(min, max) {
    xyChartData.yAxis = {
        type: "linear",
        title: xyChartData.yAxis.title,
        min,
        max
    };
    hasSetYAxis = true;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setYAxisRangeData, "setYAxisRangeData");
function setYAxisRangeFromPlotData(data) {
    const minValue = Math.min(...data);
    const maxValue = Math.max(...data);
    const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;
    const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;
    xyChartData.yAxis = {
        type: "linear",
        title: xyChartData.yAxis.title,
        min: Math.min(prevMinValue, minValue),
        max: Math.max(prevMaxValue, maxValue)
    };
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setYAxisRangeFromPlotData, "setYAxisRangeFromPlotData");
function transformDataWithoutCategory(data) {
    let retData = [];
    if (data.length === 0) {
        return retData;
    }
    if (!hasSetXAxis) {
        const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;
        const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;
        setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));
    }
    if (!hasSetYAxis) {
        setYAxisRangeFromPlotData(data);
    }
    if (isBandAxisData(xyChartData.xAxis)) {
        retData = xyChartData.xAxis.categories.map((c, i)=>[
                c,
                data[i]
            ]);
    }
    if (isLinearAxisData(xyChartData.xAxis)) {
        const min = xyChartData.xAxis.min;
        const max = xyChartData.xAxis.max;
        const step = (max - min) / (data.length - 1);
        const categories = [];
        for(let i = min; i <= max; i += step){
            categories.push("".concat(i));
        }
        retData = categories.map((c, i)=>[
                c,
                data[i]
            ]);
    }
    return retData;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(transformDataWithoutCategory, "transformDataWithoutCategory");
function getPlotColorFromPalette(plotIndex2) {
    return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getPlotColorFromPalette, "getPlotColorFromPalette");
function setLineData(title, data) {
    const plotData = transformDataWithoutCategory(data);
    xyChartData.plots.push({
        type: "line",
        strokeFill: getPlotColorFromPalette(plotIndex),
        strokeWidth: 2,
        data: plotData
    });
    plotIndex++;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setLineData, "setLineData");
function setBarData(title, data) {
    const plotData = transformDataWithoutCategory(data);
    xyChartData.plots.push({
        type: "bar",
        fill: getPlotColorFromPalette(plotIndex),
        data: plotData
    });
    plotIndex++;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(setBarData, "setBarData");
function getDrawableElem() {
    if (xyChartData.plots.length === 0) {
        throw Error("No Plot to render, please provide a plot with some data");
    }
    xyChartData.title = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDiagramTitle"])();
    return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getDrawableElem, "getDrawableElem");
function getChartThemeConfig() {
    return xyChartThemeConfig;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartThemeConfig, "getChartThemeConfig");
function getChartConfig() {
    return xyChartConfig;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getChartConfig, "getChartConfig");
function getXYChartData() {
    return xyChartData;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getXYChartData, "getXYChartData");
var clear2 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clear"])();
    plotIndex = 0;
    xyChartConfig = getChartDefaultConfig();
    xyChartData = getChartDefaultData();
    xyChartThemeConfig = getChartDefaultThemeConfig();
    plotColorPalette = xyChartThemeConfig.plotColorPalette.split(",").map((color)=>color.trim());
    hasSetXAxis = false;
    hasSetYAxis = false;
}, "clear");
var xychartDb_default = {
    getDrawableElem,
    clear: clear2,
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccTitle"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccTitle"],
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setDiagramTitle"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDiagramTitle"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccDescription"],
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccDescription"],
    setOrientation,
    setXAxisTitle,
    setXAxisRangeData,
    setXAxisBand,
    setYAxisTitle,
    setYAxisRangeData,
    setLineData,
    setBarData,
    setTmpSVGG,
    getChartThemeConfig,
    getChartConfig,
    getXYChartData
};
// src/diagrams/xychart/xychartRenderer.ts
var draw = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((txt, id, _version, diagObj)=>{
    const db = diagObj.db;
    const themeConfig = db.getChartThemeConfig();
    const chartConfig = db.getChartConfig();
    const labelData = db.getXYChartData().plots[0].data.map((data)=>data[1]);
    function getDominantBaseLine(horizontalPos) {
        return horizontalPos === "top" ? "text-before-edge" : "middle";
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getDominantBaseLine, "getDominantBaseLine");
    function getTextAnchor(verticalPos) {
        return verticalPos === "left" ? "start" : verticalPos === "right" ? "end" : "middle";
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getTextAnchor, "getTextAnchor");
    function getTextTransformation(data) {
        return "translate(".concat(data.x, ", ").concat(data.y, ") rotate(").concat(data.rotation || 0, ")");
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getTextTransformation, "getTextTransformation");
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("Rendering xychart chart\n" + txt);
    const svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectSvgElement"])(id);
    const group = svg.append("g").attr("class", "main");
    const background = group.append("rect").attr("width", chartConfig.width).attr("height", chartConfig.height).attr("class", "background");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["configureSvgSize"])(svg, chartConfig.height, chartConfig.width, true);
    svg.attr("viewBox", "0 0 ".concat(chartConfig.width, " ").concat(chartConfig.height));
    background.attr("fill", themeConfig.backgroundColor);
    db.setTmpSVGG(svg.append("g").attr("class", "mermaid-tmp-group"));
    const shapes = db.getDrawableElem();
    const groups = {};
    function getGroup(gList) {
        let elem = group;
        let prefix = "";
        for (const [i] of gList.entries()){
            let parent = group;
            if (i > 0 && groups[prefix]) {
                parent = groups[prefix];
            }
            prefix += gList[i];
            elem = groups[prefix];
            if (!elem) {
                elem = groups[prefix] = parent.append("g").attr("class", gList[i]);
            }
        }
        return elem;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getGroup, "getGroup");
    for (const shape of shapes){
        if (shape.data.length === 0) {
            continue;
        }
        const shapeGroup = getGroup(shape.groupTexts);
        switch(shape.type){
            case "rect":
                shapeGroup.selectAll("rect").data(shape.data).enter().append("rect").attr("x", (data)=>data.x).attr("y", (data)=>data.y).attr("width", (data)=>data.width).attr("height", (data)=>data.height).attr("fill", (data)=>data.fill).attr("stroke", (data)=>data.strokeFill).attr("stroke-width", (data)=>data.strokeWidth);
                if (chartConfig.showDataLabel) {
                    if (chartConfig.chartOrientation === "horizontal") {
                        let fitsHorizontally2 = function(item, fontSize) {
                            const { data, label } = item;
                            const textWidth = fontSize * label.length * charWidthFactor;
                            return textWidth <= data.width - 10;
                        };
                        var fitsHorizontally = fitsHorizontally2;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(fitsHorizontally2, "fitsHorizontally");
                        const charWidthFactor = 0.7;
                        const validItems = shape.data.map((d, i)=>({
                                data: d,
                                label: labelData[i].toString()
                            })).filter((item)=>item.data.width > 0 && item.data.height > 0);
                        const candidateFontSizes = validItems.map((item)=>{
                            const { data } = item;
                            let fontSize = data.height * 0.7;
                            while(!fitsHorizontally2(item, fontSize) && fontSize > 0){
                                fontSize -= 1;
                            }
                            return fontSize;
                        });
                        const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));
                        shapeGroup.selectAll("text").data(validItems).enter().append("text").attr("x", (item)=>item.data.x + item.data.width - 10).attr("y", (item)=>item.data.y + item.data.height / 2).attr("text-anchor", "end").attr("dominant-baseline", "middle").attr("fill", "black").attr("font-size", "".concat(uniformFontSize, "px")).text((item)=>item.label);
                    } else {
                        let fitsInBar2 = function(item, fontSize, yOffset2) {
                            const { data, label } = item;
                            const charWidthFactor = 0.7;
                            const textWidth = fontSize * label.length * charWidthFactor;
                            const centerX = data.x + data.width / 2;
                            const leftEdge = centerX - textWidth / 2;
                            const rightEdge = centerX + textWidth / 2;
                            const horizontalFits = leftEdge >= data.x && rightEdge <= data.x + data.width;
                            const verticalFits = data.y + yOffset2 + fontSize <= data.y + data.height;
                            return horizontalFits && verticalFits;
                        };
                        var fitsInBar = fitsInBar2;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(fitsInBar2, "fitsInBar");
                        const yOffset = 10;
                        const validItems = shape.data.map((d, i)=>({
                                data: d,
                                label: labelData[i].toString()
                            })).filter((item)=>item.data.width > 0 && item.data.height > 0);
                        const candidateFontSizes = validItems.map((item)=>{
                            const { data, label } = item;
                            let fontSize = data.width / (label.length * 0.7);
                            while(!fitsInBar2(item, fontSize, yOffset) && fontSize > 0){
                                fontSize -= 1;
                            }
                            return fontSize;
                        });
                        const uniformFontSize = Math.floor(Math.min(...candidateFontSizes));
                        shapeGroup.selectAll("text").data(validItems).enter().append("text").attr("x", (item)=>item.data.x + item.data.width / 2).attr("y", (item)=>item.data.y + yOffset).attr("text-anchor", "middle").attr("dominant-baseline", "hanging").attr("fill", "black").attr("font-size", "".concat(uniformFontSize, "px")).text((item)=>item.label);
                    }
                }
                break;
            case "text":
                shapeGroup.selectAll("text").data(shape.data).enter().append("text").attr("x", 0).attr("y", 0).attr("fill", (data)=>data.fill).attr("font-size", (data)=>data.fontSize).attr("dominant-baseline", (data)=>getDominantBaseLine(data.verticalPos)).attr("text-anchor", (data)=>getTextAnchor(data.horizontalPos)).attr("transform", (data)=>getTextTransformation(data)).text((data)=>data.text);
                break;
            case "path":
                shapeGroup.selectAll("path").data(shape.data).enter().append("path").attr("d", (data)=>data.path).attr("fill", (data)=>data.fill ? data.fill : "none").attr("stroke", (data)=>data.strokeFill).attr("stroke-width", (data)=>data.strokeWidth);
                break;
        }
    }
}, "draw");
var xychartRenderer_default = {
    draw
};
// src/diagrams/xychart/xychartDiagram.ts
var diagram = {
    parser: xychart_default,
    db: xychartDb_default,
    renderer: xychartRenderer_default
};
;
}),
}]);

//# sourceMappingURL=node_modules_af58a80d._.js.map