(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/cose-base/cose-base.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
(function webpackUniversalModuleDefinition(root, factory) {
    if ("TURBOPACK compile-time truthy", 1) module.exports = factory(__turbopack_context__.r("[project]/node_modules/layout-base/layout-base.js [app-client] (ecmascript)"));
    else //TURBOPACK unreachable
    ;
})(("TURBOPACK member replacement", __turbopack_context__.e), function(__WEBPACK_EXTERNAL_MODULE_0__) {
    return /******/ function(modules) {
        /******/ // The module cache
        /******/ var installedModules = {};
        /******/ /******/ // The require function
        /******/ function __webpack_require__(moduleId) {
            /******/ /******/ // Check if module is in cache
            /******/ if (installedModules[moduleId]) {
                /******/ return installedModules[moduleId].exports;
            /******/ }
            /******/ // Create a new module (and put it into the cache)
            /******/ var module1 = installedModules[moduleId] = {
                /******/ i: moduleId,
                /******/ l: false,
                /******/ exports: {}
            };
            /******/ /******/ // Execute the module function
            /******/ modules[moduleId].call(module1.exports, module1, module1.exports, __webpack_require__);
            /******/ /******/ // Flag the module as loaded
            /******/ module1.l = true;
            /******/ /******/ // Return the exports of the module
            /******/ return module1.exports;
        /******/ }
        /******/ /******/ /******/ // expose the modules object (__webpack_modules__)
        /******/ __webpack_require__.m = modules;
        /******/ /******/ // expose the module cache
        /******/ __webpack_require__.c = installedModules;
        /******/ /******/ // identity function for calling harmony imports with the correct context
        /******/ __webpack_require__.i = function(value) {
            return value;
        };
        /******/ /******/ // define getter function for harmony exports
        /******/ __webpack_require__.d = function(exports, name, getter) {
            /******/ if (!__webpack_require__.o(exports, name)) {
                /******/ Object.defineProperty(exports, name, {
                    /******/ configurable: false,
                    /******/ enumerable: true,
                    /******/ get: getter
                });
            /******/ }
        /******/ };
        /******/ /******/ // getDefaultExport function for compatibility with non-harmony modules
        /******/ __webpack_require__.n = function(module1) {
            /******/ var getter = module1 && module1.__esModule ? /******/ function getDefault() {
                return module1['default'];
            } : /******/ function getModuleExports() {
                return module1;
            };
            /******/ __webpack_require__.d(getter, 'a', getter);
            /******/ return getter;
        /******/ };
        /******/ /******/ // Object.prototype.hasOwnProperty.call
        /******/ __webpack_require__.o = function(object, property) {
            return Object.prototype.hasOwnProperty.call(object, property);
        };
        /******/ /******/ // __webpack_public_path__
        /******/ __webpack_require__.p = "";
        /******/ /******/ // Load entry module and return exports
        /******/ return __webpack_require__(__webpack_require__.s = 7);
    /******/ }([
        function(module1, exports) {
            module1.exports = __WEBPACK_EXTERNAL_MODULE_0__;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;
            function CoSEConstants() {}
            //CoSEConstants inherits static props in FDLayoutConstants
            for(var prop in FDLayoutConstants){
                CoSEConstants[prop] = FDLayoutConstants[prop];
            }
            CoSEConstants.DEFAULT_USE_MULTI_LEVEL_SCALING = false;
            CoSEConstants.DEFAULT_RADIAL_SEPARATION = FDLayoutConstants.DEFAULT_EDGE_LENGTH;
            CoSEConstants.DEFAULT_COMPONENT_SEPERATION = 60;
            CoSEConstants.TILE = true;
            CoSEConstants.TILING_PADDING_VERTICAL = 10;
            CoSEConstants.TILING_PADDING_HORIZONTAL = 10;
            CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = false; // make this true when cose is used incrementally as a part of other non-incremental layout
            module1.exports = CoSEConstants;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var FDLayoutEdge = __webpack_require__(0).FDLayoutEdge;
            function CoSEEdge(source, target, vEdge) {
                FDLayoutEdge.call(this, source, target, vEdge);
            }
            CoSEEdge.prototype = Object.create(FDLayoutEdge.prototype);
            for(var prop in FDLayoutEdge){
                CoSEEdge[prop] = FDLayoutEdge[prop];
            }
            module1.exports = CoSEEdge;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var LGraph = __webpack_require__(0).LGraph;
            function CoSEGraph(parent, graphMgr, vGraph) {
                LGraph.call(this, parent, graphMgr, vGraph);
            }
            CoSEGraph.prototype = Object.create(LGraph.prototype);
            for(var prop in LGraph){
                CoSEGraph[prop] = LGraph[prop];
            }
            module1.exports = CoSEGraph;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var LGraphManager = __webpack_require__(0).LGraphManager;
            function CoSEGraphManager(layout) {
                LGraphManager.call(this, layout);
            }
            CoSEGraphManager.prototype = Object.create(LGraphManager.prototype);
            for(var prop in LGraphManager){
                CoSEGraphManager[prop] = LGraphManager[prop];
            }
            module1.exports = CoSEGraphManager;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var FDLayoutNode = __webpack_require__(0).FDLayoutNode;
            var IMath = __webpack_require__(0).IMath;
            function CoSENode(gm, loc, size, vNode) {
                FDLayoutNode.call(this, gm, loc, size, vNode);
            }
            CoSENode.prototype = Object.create(FDLayoutNode.prototype);
            for(var prop in FDLayoutNode){
                CoSENode[prop] = FDLayoutNode[prop];
            }
            CoSENode.prototype.move = function() {
                var layout = this.graphManager.getLayout();
                this.displacementX = layout.coolingFactor * (this.springForceX + this.repulsionForceX + this.gravitationForceX) / this.noOfChildren;
                this.displacementY = layout.coolingFactor * (this.springForceY + this.repulsionForceY + this.gravitationForceY) / this.noOfChildren;
                if (Math.abs(this.displacementX) > layout.coolingFactor * layout.maxNodeDisplacement) {
                    this.displacementX = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementX);
                }
                if (Math.abs(this.displacementY) > layout.coolingFactor * layout.maxNodeDisplacement) {
                    this.displacementY = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementY);
                }
                // a simple node, just move it
                if (this.child == null) {
                    this.moveBy(this.displacementX, this.displacementY);
                } else if (this.child.getNodes().length == 0) {
                    this.moveBy(this.displacementX, this.displacementY);
                } else {
                    this.propogateDisplacementToChildren(this.displacementX, this.displacementY);
                }
                layout.totalDisplacement += Math.abs(this.displacementX) + Math.abs(this.displacementY);
                this.springForceX = 0;
                this.springForceY = 0;
                this.repulsionForceX = 0;
                this.repulsionForceY = 0;
                this.gravitationForceX = 0;
                this.gravitationForceY = 0;
                this.displacementX = 0;
                this.displacementY = 0;
            };
            CoSENode.prototype.propogateDisplacementToChildren = function(dX, dY) {
                var nodes = this.getChild().getNodes();
                var node;
                for(var i = 0; i < nodes.length; i++){
                    node = nodes[i];
                    if (node.getChild() == null) {
                        node.moveBy(dX, dY);
                        node.displacementX += dX;
                        node.displacementY += dY;
                    } else {
                        node.propogateDisplacementToChildren(dX, dY);
                    }
                }
            };
            CoSENode.prototype.setPred1 = function(pred11) {
                this.pred1 = pred11;
            };
            CoSENode.prototype.getPred1 = function() {
                return pred1;
            };
            CoSENode.prototype.getPred2 = function() {
                return pred2;
            };
            CoSENode.prototype.setNext = function(next1) {
                this.next = next1;
            };
            CoSENode.prototype.getNext = function() {
                return next;
            };
            CoSENode.prototype.setProcessed = function(processed1) {
                this.processed = processed1;
            };
            CoSENode.prototype.isProcessed = function() {
                return processed;
            };
            module1.exports = CoSENode;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var FDLayout = __webpack_require__(0).FDLayout;
            var CoSEGraphManager = __webpack_require__(4);
            var CoSEGraph = __webpack_require__(3);
            var CoSENode = __webpack_require__(5);
            var CoSEEdge = __webpack_require__(2);
            var CoSEConstants = __webpack_require__(1);
            var FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;
            var LayoutConstants = __webpack_require__(0).LayoutConstants;
            var Point = __webpack_require__(0).Point;
            var PointD = __webpack_require__(0).PointD;
            var Layout = __webpack_require__(0).Layout;
            var Integer = __webpack_require__(0).Integer;
            var IGeometry = __webpack_require__(0).IGeometry;
            var LGraph = __webpack_require__(0).LGraph;
            var Transform = __webpack_require__(0).Transform;
            function CoSELayout() {
                FDLayout.call(this);
                this.toBeTiled = {}; // Memorize if a node is to be tiled or is tiled
            }
            CoSELayout.prototype = Object.create(FDLayout.prototype);
            for(var prop in FDLayout){
                CoSELayout[prop] = FDLayout[prop];
            }
            CoSELayout.prototype.newGraphManager = function() {
                var gm = new CoSEGraphManager(this);
                this.graphManager = gm;
                return gm;
            };
            CoSELayout.prototype.newGraph = function(vGraph) {
                return new CoSEGraph(null, this.graphManager, vGraph);
            };
            CoSELayout.prototype.newNode = function(vNode) {
                return new CoSENode(this.graphManager, vNode);
            };
            CoSELayout.prototype.newEdge = function(vEdge) {
                return new CoSEEdge(null, null, vEdge);
            };
            CoSELayout.prototype.initParameters = function() {
                FDLayout.prototype.initParameters.call(this, arguments);
                if (!this.isSubLayout) {
                    if (CoSEConstants.DEFAULT_EDGE_LENGTH < 10) {
                        this.idealEdgeLength = 10;
                    } else {
                        this.idealEdgeLength = CoSEConstants.DEFAULT_EDGE_LENGTH;
                    }
                    this.useSmartIdealEdgeLengthCalculation = CoSEConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;
                    this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;
                    this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;
                    this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;
                    this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;
                    this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;
                    this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;
                    // variables for tree reduction support
                    this.prunedNodesAll = [];
                    this.growTreeIterations = 0;
                    this.afterGrowthIterations = 0;
                    this.isTreeGrowing = false;
                    this.isGrowthFinished = false;
                    // variables for cooling
                    this.coolingCycle = 0;
                    this.maxCoolingCycle = this.maxIterations / FDLayoutConstants.CONVERGENCE_CHECK_PERIOD;
                    this.finalTemperature = FDLayoutConstants.CONVERGENCE_CHECK_PERIOD / this.maxIterations;
                    this.coolingAdjuster = 1;
                }
            };
            CoSELayout.prototype.layout = function() {
                var createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;
                if (createBendsAsNeeded) {
                    this.createBendpoints();
                    this.graphManager.resetAllEdges();
                }
                this.level = 0;
                return this.classicLayout();
            };
            CoSELayout.prototype.classicLayout = function() {
                this.nodesWithGravity = this.calculateNodesToApplyGravitationTo();
                this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);
                this.calcNoOfChildrenForAllNodes();
                this.graphManager.calcLowestCommonAncestors();
                this.graphManager.calcInclusionTreeDepths();
                this.graphManager.getRoot().calcEstimatedSize();
                this.calcIdealEdgeLengths();
                if (!this.incremental) {
                    var forest = this.getFlatForest();
                    // The graph associated with this layout is flat and a forest
                    if (forest.length > 0) {
                        this.positionNodesRadially(forest);
                    } else {
                        // Reduce the trees when incremental mode is not enabled and graph is not a forest 
                        this.reduceTrees();
                        // Update nodes that gravity will be applied
                        this.graphManager.resetAllNodesToApplyGravitation();
                        var allNodes = new Set(this.getAllNodes());
                        var intersection = this.nodesWithGravity.filter(function(x) {
                            return allNodes.has(x);
                        });
                        this.graphManager.setAllNodesToApplyGravitation(intersection);
                        this.positionNodesRandomly();
                    }
                } else {
                    if (CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL) {
                        // Reduce the trees in incremental mode if only this constant is set to true 
                        this.reduceTrees();
                        // Update nodes that gravity will be applied
                        this.graphManager.resetAllNodesToApplyGravitation();
                        var allNodes = new Set(this.getAllNodes());
                        var intersection = this.nodesWithGravity.filter(function(x) {
                            return allNodes.has(x);
                        });
                        this.graphManager.setAllNodesToApplyGravitation(intersection);
                    }
                }
                this.initSpringEmbedder();
                this.runSpringEmbedder();
                return true;
            };
            CoSELayout.prototype.tick = function() {
                this.totalIterations++;
                if (this.totalIterations === this.maxIterations && !this.isTreeGrowing && !this.isGrowthFinished) {
                    if (this.prunedNodesAll.length > 0) {
                        this.isTreeGrowing = true;
                    } else {
                        return true;
                    }
                }
                if (this.totalIterations % FDLayoutConstants.CONVERGENCE_CHECK_PERIOD == 0 && !this.isTreeGrowing && !this.isGrowthFinished) {
                    if (this.isConverged()) {
                        if (this.prunedNodesAll.length > 0) {
                            this.isTreeGrowing = true;
                        } else {
                            return true;
                        }
                    }
                    this.coolingCycle++;
                    if (this.layoutQuality == 0) {
                        // quality - "draft"
                        this.coolingAdjuster = this.coolingCycle;
                    } else if (this.layoutQuality == 1) {
                        // quality - "default"
                        this.coolingAdjuster = this.coolingCycle / 3;
                    }
                    // cooling schedule is based on http://www.btluke.com/simanf1.html -> cooling schedule 3
                    this.coolingFactor = Math.max(this.initialCoolingFactor - Math.pow(this.coolingCycle, Math.log(100 * (this.initialCoolingFactor - this.finalTemperature)) / Math.log(this.maxCoolingCycle)) / 100 * this.coolingAdjuster, this.finalTemperature);
                    this.animationPeriod = Math.ceil(this.initialAnimationPeriod * Math.sqrt(this.coolingFactor));
                }
                // Operations while tree is growing again 
                if (this.isTreeGrowing) {
                    if (this.growTreeIterations % 10 == 0) {
                        if (this.prunedNodesAll.length > 0) {
                            this.graphManager.updateBounds();
                            this.updateGrid();
                            this.growTree(this.prunedNodesAll);
                            // Update nodes that gravity will be applied
                            this.graphManager.resetAllNodesToApplyGravitation();
                            var allNodes = new Set(this.getAllNodes());
                            var intersection = this.nodesWithGravity.filter(function(x) {
                                return allNodes.has(x);
                            });
                            this.graphManager.setAllNodesToApplyGravitation(intersection);
                            this.graphManager.updateBounds();
                            this.updateGrid();
                            this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;
                        } else {
                            this.isTreeGrowing = false;
                            this.isGrowthFinished = true;
                        }
                    }
                    this.growTreeIterations++;
                }
                // Operations after growth is finished
                if (this.isGrowthFinished) {
                    if (this.isConverged()) {
                        return true;
                    }
                    if (this.afterGrowthIterations % 10 == 0) {
                        this.graphManager.updateBounds();
                        this.updateGrid();
                    }
                    this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL * ((100 - this.afterGrowthIterations) / 100);
                    this.afterGrowthIterations++;
                }
                var gridUpdateAllowed = !this.isTreeGrowing && !this.isGrowthFinished;
                var forceToNodeSurroundingUpdate = this.growTreeIterations % 10 == 1 && this.isTreeGrowing || this.afterGrowthIterations % 10 == 1 && this.isGrowthFinished;
                this.totalDisplacement = 0;
                this.graphManager.updateBounds();
                this.calcSpringForces();
                this.calcRepulsionForces(gridUpdateAllowed, forceToNodeSurroundingUpdate);
                this.calcGravitationalForces();
                this.moveNodes();
                this.animate();
                return false; // Layout is not ended yet return false
            };
            CoSELayout.prototype.getPositionsData = function() {
                var allNodes = this.graphManager.getAllNodes();
                var pData = {};
                for(var i = 0; i < allNodes.length; i++){
                    var rect = allNodes[i].rect;
                    var id = allNodes[i].id;
                    pData[id] = {
                        id: id,
                        x: rect.getCenterX(),
                        y: rect.getCenterY(),
                        w: rect.width,
                        h: rect.height
                    };
                }
                return pData;
            };
            CoSELayout.prototype.runSpringEmbedder = function() {
                this.initialAnimationPeriod = 25;
                this.animationPeriod = this.initialAnimationPeriod;
                var layoutEnded = false;
                // If aminate option is 'during' signal that layout is supposed to start iterating
                if (FDLayoutConstants.ANIMATE === 'during') {
                    this.emit('layoutstarted');
                } else {
                    // If aminate option is 'during' tick() function will be called on index.js
                    while(!layoutEnded){
                        layoutEnded = this.tick();
                    }
                    this.graphManager.updateBounds();
                }
            };
            CoSELayout.prototype.calculateNodesToApplyGravitationTo = function() {
                var nodeList = [];
                var graph;
                var graphs = this.graphManager.getGraphs();
                var size = graphs.length;
                var i;
                for(i = 0; i < size; i++){
                    graph = graphs[i];
                    graph.updateConnected();
                    if (!graph.isConnected) {
                        nodeList = nodeList.concat(graph.getNodes());
                    }
                }
                return nodeList;
            };
            CoSELayout.prototype.createBendpoints = function() {
                var edges = [];
                edges = edges.concat(this.graphManager.getAllEdges());
                var visited = new Set();
                var i;
                for(i = 0; i < edges.length; i++){
                    var edge = edges[i];
                    if (!visited.has(edge)) {
                        var source = edge.getSource();
                        var target = edge.getTarget();
                        if (source == target) {
                            edge.getBendpoints().push(new PointD());
                            edge.getBendpoints().push(new PointD());
                            this.createDummyNodesForBendpoints(edge);
                            visited.add(edge);
                        } else {
                            var edgeList = [];
                            edgeList = edgeList.concat(source.getEdgeListToNode(target));
                            edgeList = edgeList.concat(target.getEdgeListToNode(source));
                            if (!visited.has(edgeList[0])) {
                                if (edgeList.length > 1) {
                                    var k;
                                    for(k = 0; k < edgeList.length; k++){
                                        var multiEdge = edgeList[k];
                                        multiEdge.getBendpoints().push(new PointD());
                                        this.createDummyNodesForBendpoints(multiEdge);
                                    }
                                }
                                edgeList.forEach(function(edge) {
                                    visited.add(edge);
                                });
                            }
                        }
                    }
                    if (visited.size == edges.length) {
                        break;
                    }
                }
            };
            CoSELayout.prototype.positionNodesRadially = function(forest) {
                // We tile the trees to a grid row by row; first tree starts at (0,0)
                var currentStartingPoint = new Point(0, 0);
                var numberOfColumns = Math.ceil(Math.sqrt(forest.length));
                var height = 0;
                var currentY = 0;
                var currentX = 0;
                var point = new PointD(0, 0);
                for(var i = 0; i < forest.length; i++){
                    if (i % numberOfColumns == 0) {
                        // Start of a new row, make the x coordinate 0, increment the
                        // y coordinate with the max height of the previous row
                        currentX = 0;
                        currentY = height;
                        if (i != 0) {
                            currentY += CoSEConstants.DEFAULT_COMPONENT_SEPERATION;
                        }
                        height = 0;
                    }
                    var tree = forest[i];
                    // Find the center of the tree
                    var centerNode = Layout.findCenterOfTree(tree);
                    // Set the staring point of the next tree
                    currentStartingPoint.x = currentX;
                    currentStartingPoint.y = currentY;
                    // Do a radial layout starting with the center
                    point = CoSELayout.radialLayout(tree, centerNode, currentStartingPoint);
                    if (point.y > height) {
                        height = Math.floor(point.y);
                    }
                    currentX = Math.floor(point.x + CoSEConstants.DEFAULT_COMPONENT_SEPERATION);
                }
                this.transform(new PointD(LayoutConstants.WORLD_CENTER_X - point.x / 2, LayoutConstants.WORLD_CENTER_Y - point.y / 2));
            };
            CoSELayout.radialLayout = function(tree, centerNode, startingPoint) {
                var radialSep = Math.max(this.maxDiagonalInTree(tree), CoSEConstants.DEFAULT_RADIAL_SEPARATION);
                CoSELayout.branchRadialLayout(centerNode, null, 0, 359, 0, radialSep);
                var bounds = LGraph.calculateBounds(tree);
                var transform = new Transform();
                transform.setDeviceOrgX(bounds.getMinX());
                transform.setDeviceOrgY(bounds.getMinY());
                transform.setWorldOrgX(startingPoint.x);
                transform.setWorldOrgY(startingPoint.y);
                for(var i = 0; i < tree.length; i++){
                    var node = tree[i];
                    node.transform(transform);
                }
                var bottomRight = new PointD(bounds.getMaxX(), bounds.getMaxY());
                return transform.inverseTransformPoint(bottomRight);
            };
            CoSELayout.branchRadialLayout = function(node, parentOfNode, startAngle, endAngle, distance, radialSeparation) {
                // First, position this node by finding its angle.
                var halfInterval = (endAngle - startAngle + 1) / 2;
                if (halfInterval < 0) {
                    halfInterval += 180;
                }
                var nodeAngle = (halfInterval + startAngle) % 360;
                var teta = nodeAngle * IGeometry.TWO_PI / 360;
                // Make polar to java cordinate conversion.
                var cos_teta = Math.cos(teta);
                var x_ = distance * Math.cos(teta);
                var y_ = distance * Math.sin(teta);
                node.setCenter(x_, y_);
                // Traverse all neighbors of this node and recursively call this
                // function.
                var neighborEdges = [];
                neighborEdges = neighborEdges.concat(node.getEdges());
                var childCount = neighborEdges.length;
                if (parentOfNode != null) {
                    childCount--;
                }
                var branchCount = 0;
                var incEdgesCount = neighborEdges.length;
                var startIndex;
                var edges = node.getEdgesBetween(parentOfNode);
                // If there are multiple edges, prune them until there remains only one
                // edge.
                while(edges.length > 1){
                    //neighborEdges.remove(edges.remove(0));
                    var temp = edges[0];
                    edges.splice(0, 1);
                    var index = neighborEdges.indexOf(temp);
                    if (index >= 0) {
                        neighborEdges.splice(index, 1);
                    }
                    incEdgesCount--;
                    childCount--;
                }
                if (parentOfNode != null) {
                    //assert edges.length == 1;
                    startIndex = (neighborEdges.indexOf(edges[0]) + 1) % incEdgesCount;
                } else {
                    startIndex = 0;
                }
                var stepAngle = Math.abs(endAngle - startAngle) / childCount;
                for(var i = startIndex; branchCount != childCount; i = ++i % incEdgesCount){
                    var currentNeighbor = neighborEdges[i].getOtherEnd(node);
                    // Don't back traverse to root node in current tree.
                    if (currentNeighbor == parentOfNode) {
                        continue;
                    }
                    var childStartAngle = (startAngle + branchCount * stepAngle) % 360;
                    var childEndAngle = (childStartAngle + stepAngle) % 360;
                    CoSELayout.branchRadialLayout(currentNeighbor, node, childStartAngle, childEndAngle, distance + radialSeparation, radialSeparation);
                    branchCount++;
                }
            };
            CoSELayout.maxDiagonalInTree = function(tree) {
                var maxDiagonal = Integer.MIN_VALUE;
                for(var i = 0; i < tree.length; i++){
                    var node = tree[i];
                    var diagonal = node.getDiagonal();
                    if (diagonal > maxDiagonal) {
                        maxDiagonal = diagonal;
                    }
                }
                return maxDiagonal;
            };
            CoSELayout.prototype.calcRepulsionRange = function() {
                // formula is 2 x (level + 1) x idealEdgeLength
                return 2 * (this.level + 1) * this.idealEdgeLength;
            };
            // Tiling methods
            // Group zero degree members whose parents are not to be tiled, create dummy parents where needed and fill memberGroups by their dummp parent id's
            CoSELayout.prototype.groupZeroDegreeMembers = function() {
                var self = this;
                // array of [parent_id x oneDegreeNode_id]
                var tempMemberGroups = {}; // A temporary map of parent node and its zero degree members
                this.memberGroups = {}; // A map of dummy parent node and its zero degree members whose parents are not to be tiled
                this.idToDummyNode = {}; // A map of id to dummy node 
                var zeroDegree = []; // List of zero degree nodes whose parents are not to be tiled
                var allNodes = this.graphManager.getAllNodes();
                // Fill zero degree list
                for(var i = 0; i < allNodes.length; i++){
                    var node = allNodes[i];
                    var parent = node.getParent();
                    // If a node has zero degree and its parent is not to be tiled if exists add that node to zeroDegres list
                    if (this.getNodeDegreeWithChildren(node) === 0 && (parent.id == undefined || !this.getToBeTiled(parent))) {
                        zeroDegree.push(node);
                    }
                }
                // Create a map of parent node and its zero degree members
                for(var i = 0; i < zeroDegree.length; i++){
                    var node = zeroDegree[i]; // Zero degree node itself
                    var p_id = node.getParent().id; // Parent id
                    if (typeof tempMemberGroups[p_id] === "undefined") tempMemberGroups[p_id] = [];
                    tempMemberGroups[p_id] = tempMemberGroups[p_id].concat(node); // Push node to the list belongs to its parent in tempMemberGroups
                }
                // If there are at least two nodes at a level, create a dummy compound for them
                Object.keys(tempMemberGroups).forEach(function(p_id) {
                    if (tempMemberGroups[p_id].length > 1) {
                        var dummyCompoundId = "DummyCompound_" + p_id; // The id of dummy compound which will be created soon
                        self.memberGroups[dummyCompoundId] = tempMemberGroups[p_id]; // Add dummy compound to memberGroups
                        var parent = tempMemberGroups[p_id][0].getParent(); // The parent of zero degree nodes will be the parent of new dummy compound
                        // Create a dummy compound with calculated id
                        var dummyCompound = new CoSENode(self.graphManager);
                        dummyCompound.id = dummyCompoundId;
                        dummyCompound.paddingLeft = parent.paddingLeft || 0;
                        dummyCompound.paddingRight = parent.paddingRight || 0;
                        dummyCompound.paddingBottom = parent.paddingBottom || 0;
                        dummyCompound.paddingTop = parent.paddingTop || 0;
                        self.idToDummyNode[dummyCompoundId] = dummyCompound;
                        var dummyParentGraph = self.getGraphManager().add(self.newGraph(), dummyCompound);
                        var parentGraph = parent.getChild();
                        // Add dummy compound to parent the graph
                        parentGraph.add(dummyCompound);
                        // For each zero degree node in this level remove it from its parent graph and add it to the graph of dummy parent
                        for(var i = 0; i < tempMemberGroups[p_id].length; i++){
                            var node = tempMemberGroups[p_id][i];
                            parentGraph.remove(node);
                            dummyParentGraph.add(node);
                        }
                    }
                });
            };
            CoSELayout.prototype.clearCompounds = function() {
                var childGraphMap = {};
                var idToNode = {};
                // Get compound ordering by finding the inner one first
                this.performDFSOnCompounds();
                for(var i = 0; i < this.compoundOrder.length; i++){
                    idToNode[this.compoundOrder[i].id] = this.compoundOrder[i];
                    childGraphMap[this.compoundOrder[i].id] = [].concat(this.compoundOrder[i].getChild().getNodes());
                    // Remove children of compounds
                    this.graphManager.remove(this.compoundOrder[i].getChild());
                    this.compoundOrder[i].child = null;
                }
                this.graphManager.resetAllNodes();
                // Tile the removed children
                this.tileCompoundMembers(childGraphMap, idToNode);
            };
            CoSELayout.prototype.clearZeroDegreeMembers = function() {
                var self = this;
                var tiledZeroDegreePack = this.tiledZeroDegreePack = [];
                Object.keys(this.memberGroups).forEach(function(id) {
                    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound
                    tiledZeroDegreePack[id] = self.tileNodes(self.memberGroups[id], compoundNode.paddingLeft + compoundNode.paddingRight);
                    // Set the width and height of the dummy compound as calculated
                    compoundNode.rect.width = tiledZeroDegreePack[id].width;
                    compoundNode.rect.height = tiledZeroDegreePack[id].height;
                });
            };
            CoSELayout.prototype.repopulateCompounds = function() {
                for(var i = this.compoundOrder.length - 1; i >= 0; i--){
                    var lCompoundNode = this.compoundOrder[i];
                    var id = lCompoundNode.id;
                    var horizontalMargin = lCompoundNode.paddingLeft;
                    var verticalMargin = lCompoundNode.paddingTop;
                    this.adjustLocations(this.tiledMemberPack[id], lCompoundNode.rect.x, lCompoundNode.rect.y, horizontalMargin, verticalMargin);
                }
            };
            CoSELayout.prototype.repopulateZeroDegreeMembers = function() {
                var self = this;
                var tiledPack = this.tiledZeroDegreePack;
                Object.keys(tiledPack).forEach(function(id) {
                    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound by its id
                    var horizontalMargin = compoundNode.paddingLeft;
                    var verticalMargin = compoundNode.paddingTop;
                    // Adjust the positions of nodes wrt its compound
                    self.adjustLocations(tiledPack[id], compoundNode.rect.x, compoundNode.rect.y, horizontalMargin, verticalMargin);
                });
            };
            CoSELayout.prototype.getToBeTiled = function(node) {
                var id = node.id;
                //firstly check the previous results
                if (this.toBeTiled[id] != null) {
                    return this.toBeTiled[id];
                }
                //only compound nodes are to be tiled
                var childGraph = node.getChild();
                if (childGraph == null) {
                    this.toBeTiled[id] = false;
                    return false;
                }
                var children = childGraph.getNodes(); // Get the children nodes
                //a compound node is not to be tiled if all of its compound children are not to be tiled
                for(var i = 0; i < children.length; i++){
                    var theChild = children[i];
                    if (this.getNodeDegree(theChild) > 0) {
                        this.toBeTiled[id] = false;
                        return false;
                    }
                    //pass the children not having the compound structure
                    if (theChild.getChild() == null) {
                        this.toBeTiled[theChild.id] = false;
                        continue;
                    }
                    if (!this.getToBeTiled(theChild)) {
                        this.toBeTiled[id] = false;
                        return false;
                    }
                }
                this.toBeTiled[id] = true;
                return true;
            };
            // Get degree of a node depending of its edges and independent of its children
            CoSELayout.prototype.getNodeDegree = function(node) {
                var id = node.id;
                var edges = node.getEdges();
                var degree = 0;
                // For the edges connected
                for(var i = 0; i < edges.length; i++){
                    var edge = edges[i];
                    if (edge.getSource().id !== edge.getTarget().id) {
                        degree = degree + 1;
                    }
                }
                return degree;
            };
            // Get degree of a node with its children
            CoSELayout.prototype.getNodeDegreeWithChildren = function(node) {
                var degree = this.getNodeDegree(node);
                if (node.getChild() == null) {
                    return degree;
                }
                var children = node.getChild().getNodes();
                for(var i = 0; i < children.length; i++){
                    var child = children[i];
                    degree += this.getNodeDegreeWithChildren(child);
                }
                return degree;
            };
            CoSELayout.prototype.performDFSOnCompounds = function() {
                this.compoundOrder = [];
                this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes());
            };
            CoSELayout.prototype.fillCompexOrderByDFS = function(children) {
                for(var i = 0; i < children.length; i++){
                    var child = children[i];
                    if (child.getChild() != null) {
                        this.fillCompexOrderByDFS(child.getChild().getNodes());
                    }
                    if (this.getToBeTiled(child)) {
                        this.compoundOrder.push(child);
                    }
                }
            };
            /**
* This method places each zero degree member wrt given (x,y) coordinates (top left).
*/ CoSELayout.prototype.adjustLocations = function(organization, x, y, compoundHorizontalMargin, compoundVerticalMargin) {
                x += compoundHorizontalMargin;
                y += compoundVerticalMargin;
                var left = x;
                for(var i = 0; i < organization.rows.length; i++){
                    var row = organization.rows[i];
                    x = left;
                    var maxHeight = 0;
                    for(var j = 0; j < row.length; j++){
                        var lnode = row[j];
                        lnode.rect.x = x; // + lnode.rect.width / 2;
                        lnode.rect.y = y; // + lnode.rect.height / 2;
                        x += lnode.rect.width + organization.horizontalPadding;
                        if (lnode.rect.height > maxHeight) maxHeight = lnode.rect.height;
                    }
                    y += maxHeight + organization.verticalPadding;
                }
            };
            CoSELayout.prototype.tileCompoundMembers = function(childGraphMap, idToNode) {
                var self = this;
                this.tiledMemberPack = [];
                Object.keys(childGraphMap).forEach(function(id) {
                    // Get the compound node
                    var compoundNode = idToNode[id];
                    self.tiledMemberPack[id] = self.tileNodes(childGraphMap[id], compoundNode.paddingLeft + compoundNode.paddingRight);
                    compoundNode.rect.width = self.tiledMemberPack[id].width;
                    compoundNode.rect.height = self.tiledMemberPack[id].height;
                });
            };
            CoSELayout.prototype.tileNodes = function(nodes, minWidth) {
                var verticalPadding = CoSEConstants.TILING_PADDING_VERTICAL;
                var horizontalPadding = CoSEConstants.TILING_PADDING_HORIZONTAL;
                var organization = {
                    rows: [],
                    rowWidth: [],
                    rowHeight: [],
                    width: 0,
                    height: minWidth,
                    verticalPadding: verticalPadding,
                    horizontalPadding: horizontalPadding
                };
                // Sort the nodes in ascending order of their areas
                nodes.sort(function(n1, n2) {
                    if (n1.rect.width * n1.rect.height > n2.rect.width * n2.rect.height) return -1;
                    if (n1.rect.width * n1.rect.height < n2.rect.width * n2.rect.height) return 1;
                    return 0;
                });
                // Create the organization -> tile members
                for(var i = 0; i < nodes.length; i++){
                    var lNode = nodes[i];
                    if (organization.rows.length == 0) {
                        this.insertNodeToRow(organization, lNode, 0, minWidth);
                    } else if (this.canAddHorizontal(organization, lNode.rect.width, lNode.rect.height)) {
                        this.insertNodeToRow(organization, lNode, this.getShortestRowIndex(organization), minWidth);
                    } else {
                        this.insertNodeToRow(organization, lNode, organization.rows.length, minWidth);
                    }
                    this.shiftToLastRow(organization);
                }
                return organization;
            };
            CoSELayout.prototype.insertNodeToRow = function(organization, node, rowIndex, minWidth) {
                var minCompoundSize = minWidth;
                // Add new row if needed
                if (rowIndex == organization.rows.length) {
                    var secondDimension = [];
                    organization.rows.push(secondDimension);
                    organization.rowWidth.push(minCompoundSize);
                    organization.rowHeight.push(0);
                }
                // Update row width
                var w = organization.rowWidth[rowIndex] + node.rect.width;
                if (organization.rows[rowIndex].length > 0) {
                    w += organization.horizontalPadding;
                }
                organization.rowWidth[rowIndex] = w;
                // Update compound width
                if (organization.width < w) {
                    organization.width = w;
                }
                // Update height
                var h = node.rect.height;
                if (rowIndex > 0) h += organization.verticalPadding;
                var extraHeight = 0;
                if (h > organization.rowHeight[rowIndex]) {
                    extraHeight = organization.rowHeight[rowIndex];
                    organization.rowHeight[rowIndex] = h;
                    extraHeight = organization.rowHeight[rowIndex] - extraHeight;
                }
                organization.height += extraHeight;
                // Insert node
                organization.rows[rowIndex].push(node);
            };
            //Scans the rows of an organization and returns the one with the min width
            CoSELayout.prototype.getShortestRowIndex = function(organization) {
                var r = -1;
                var min = Number.MAX_VALUE;
                for(var i = 0; i < organization.rows.length; i++){
                    if (organization.rowWidth[i] < min) {
                        r = i;
                        min = organization.rowWidth[i];
                    }
                }
                return r;
            };
            //Scans the rows of an organization and returns the one with the max width
            CoSELayout.prototype.getLongestRowIndex = function(organization) {
                var r = -1;
                var max = Number.MIN_VALUE;
                for(var i = 0; i < organization.rows.length; i++){
                    if (organization.rowWidth[i] > max) {
                        r = i;
                        max = organization.rowWidth[i];
                    }
                }
                return r;
            };
            /**
* This method checks whether adding extra width to the organization violates
* the aspect ratio(1) or not.
*/ CoSELayout.prototype.canAddHorizontal = function(organization, extraWidth, extraHeight) {
                var sri = this.getShortestRowIndex(organization);
                if (sri < 0) {
                    return true;
                }
                var min = organization.rowWidth[sri];
                if (min + organization.horizontalPadding + extraWidth <= organization.width) return true;
                var hDiff = 0;
                // Adding to an existing row
                if (organization.rowHeight[sri] < extraHeight) {
                    if (sri > 0) hDiff = extraHeight + organization.verticalPadding - organization.rowHeight[sri];
                }
                var add_to_row_ratio;
                if (organization.width - min >= extraWidth + organization.horizontalPadding) {
                    add_to_row_ratio = (organization.height + hDiff) / (min + extraWidth + organization.horizontalPadding);
                } else {
                    add_to_row_ratio = (organization.height + hDiff) / organization.width;
                }
                // Adding a new row for this node
                hDiff = extraHeight + organization.verticalPadding;
                var add_new_row_ratio;
                if (organization.width < extraWidth) {
                    add_new_row_ratio = (organization.height + hDiff) / extraWidth;
                } else {
                    add_new_row_ratio = (organization.height + hDiff) / organization.width;
                }
                if (add_new_row_ratio < 1) add_new_row_ratio = 1 / add_new_row_ratio;
                if (add_to_row_ratio < 1) add_to_row_ratio = 1 / add_to_row_ratio;
                return add_to_row_ratio < add_new_row_ratio;
            };
            //If moving the last node from the longest row and adding it to the last
            //row makes the bounding box smaller, do it.
            CoSELayout.prototype.shiftToLastRow = function(organization) {
                var longest = this.getLongestRowIndex(organization);
                var last = organization.rowWidth.length - 1;
                var row = organization.rows[longest];
                var node = row[row.length - 1];
                var diff = node.width + organization.horizontalPadding;
                // Check if there is enough space on the last row
                if (organization.width - organization.rowWidth[last] > diff && longest != last) {
                    // Remove the last element of the longest row
                    row.splice(-1, 1);
                    // Push it to the last row
                    organization.rows[last].push(node);
                    organization.rowWidth[longest] = organization.rowWidth[longest] - diff;
                    organization.rowWidth[last] = organization.rowWidth[last] + diff;
                    organization.width = organization.rowWidth[instance.getLongestRowIndex(organization)];
                    // Update heights of the organization
                    var maxHeight = Number.MIN_VALUE;
                    for(var i = 0; i < row.length; i++){
                        if (row[i].height > maxHeight) maxHeight = row[i].height;
                    }
                    if (longest > 0) maxHeight += organization.verticalPadding;
                    var prevTotal = organization.rowHeight[longest] + organization.rowHeight[last];
                    organization.rowHeight[longest] = maxHeight;
                    if (organization.rowHeight[last] < node.height + organization.verticalPadding) organization.rowHeight[last] = node.height + organization.verticalPadding;
                    var finalTotal = organization.rowHeight[longest] + organization.rowHeight[last];
                    organization.height += finalTotal - prevTotal;
                    this.shiftToLastRow(organization);
                }
            };
            CoSELayout.prototype.tilingPreLayout = function() {
                if (CoSEConstants.TILE) {
                    // Find zero degree nodes and create a compound for each level
                    this.groupZeroDegreeMembers();
                    // Tile and clear children of each compound
                    this.clearCompounds();
                    // Separately tile and clear zero degree nodes for each level
                    this.clearZeroDegreeMembers();
                }
            };
            CoSELayout.prototype.tilingPostLayout = function() {
                if (CoSEConstants.TILE) {
                    this.repopulateZeroDegreeMembers();
                    this.repopulateCompounds();
                }
            };
            // -----------------------------------------------------------------------------
            // Section: Tree Reduction methods
            // -----------------------------------------------------------------------------
            // Reduce trees 
            CoSELayout.prototype.reduceTrees = function() {
                var prunedNodesAll = [];
                var containsLeaf = true;
                var node;
                while(containsLeaf){
                    var allNodes = this.graphManager.getAllNodes();
                    var prunedNodesInStepTemp = [];
                    containsLeaf = false;
                    for(var i = 0; i < allNodes.length; i++){
                        node = allNodes[i];
                        if (node.getEdges().length == 1 && !node.getEdges()[0].isInterGraph && node.getChild() == null) {
                            prunedNodesInStepTemp.push([
                                node,
                                node.getEdges()[0],
                                node.getOwner()
                            ]);
                            containsLeaf = true;
                        }
                    }
                    if (containsLeaf == true) {
                        var prunedNodesInStep = [];
                        for(var j = 0; j < prunedNodesInStepTemp.length; j++){
                            if (prunedNodesInStepTemp[j][0].getEdges().length == 1) {
                                prunedNodesInStep.push(prunedNodesInStepTemp[j]);
                                prunedNodesInStepTemp[j][0].getOwner().remove(prunedNodesInStepTemp[j][0]);
                            }
                        }
                        prunedNodesAll.push(prunedNodesInStep);
                        this.graphManager.resetAllNodes();
                        this.graphManager.resetAllEdges();
                    }
                }
                this.prunedNodesAll = prunedNodesAll;
            };
            // Grow tree one step 
            CoSELayout.prototype.growTree = function(prunedNodesAll) {
                var lengthOfPrunedNodesInStep = prunedNodesAll.length;
                var prunedNodesInStep = prunedNodesAll[lengthOfPrunedNodesInStep - 1];
                var nodeData;
                for(var i = 0; i < prunedNodesInStep.length; i++){
                    nodeData = prunedNodesInStep[i];
                    this.findPlaceforPrunedNode(nodeData);
                    nodeData[2].add(nodeData[0]);
                    nodeData[2].add(nodeData[1], nodeData[1].source, nodeData[1].target);
                }
                prunedNodesAll.splice(prunedNodesAll.length - 1, 1);
                this.graphManager.resetAllNodes();
                this.graphManager.resetAllEdges();
            };
            // Find an appropriate position to replace pruned node, this method can be improved
            CoSELayout.prototype.findPlaceforPrunedNode = function(nodeData) {
                var gridForPrunedNode;
                var nodeToConnect;
                var prunedNode = nodeData[0];
                if (prunedNode == nodeData[1].source) {
                    nodeToConnect = nodeData[1].target;
                } else {
                    nodeToConnect = nodeData[1].source;
                }
                var startGridX = nodeToConnect.startX;
                var finishGridX = nodeToConnect.finishX;
                var startGridY = nodeToConnect.startY;
                var finishGridY = nodeToConnect.finishY;
                var upNodeCount = 0;
                var downNodeCount = 0;
                var rightNodeCount = 0;
                var leftNodeCount = 0;
                var controlRegions = [
                    upNodeCount,
                    rightNodeCount,
                    downNodeCount,
                    leftNodeCount
                ];
                if (startGridY > 0) {
                    for(var i = startGridX; i <= finishGridX; i++){
                        controlRegions[0] += this.grid[i][startGridY - 1].length + this.grid[i][startGridY].length - 1;
                    }
                }
                if (finishGridX < this.grid.length - 1) {
                    for(var i = startGridY; i <= finishGridY; i++){
                        controlRegions[1] += this.grid[finishGridX + 1][i].length + this.grid[finishGridX][i].length - 1;
                    }
                }
                if (finishGridY < this.grid[0].length - 1) {
                    for(var i = startGridX; i <= finishGridX; i++){
                        controlRegions[2] += this.grid[i][finishGridY + 1].length + this.grid[i][finishGridY].length - 1;
                    }
                }
                if (startGridX > 0) {
                    for(var i = startGridY; i <= finishGridY; i++){
                        controlRegions[3] += this.grid[startGridX - 1][i].length + this.grid[startGridX][i].length - 1;
                    }
                }
                var min = Integer.MAX_VALUE;
                var minCount;
                var minIndex;
                for(var j = 0; j < controlRegions.length; j++){
                    if (controlRegions[j] < min) {
                        min = controlRegions[j];
                        minCount = 1;
                        minIndex = j;
                    } else if (controlRegions[j] == min) {
                        minCount++;
                    }
                }
                if (minCount == 3 && min == 0) {
                    if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[2] == 0) {
                        gridForPrunedNode = 1;
                    } else if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[3] == 0) {
                        gridForPrunedNode = 0;
                    } else if (controlRegions[0] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {
                        gridForPrunedNode = 3;
                    } else if (controlRegions[1] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {
                        gridForPrunedNode = 2;
                    }
                } else if (minCount == 2 && min == 0) {
                    var random = Math.floor(Math.random() * 2);
                    if (controlRegions[0] == 0 && controlRegions[1] == 0) {
                        ;
                        if (random == 0) {
                            gridForPrunedNode = 0;
                        } else {
                            gridForPrunedNode = 1;
                        }
                    } else if (controlRegions[0] == 0 && controlRegions[2] == 0) {
                        if (random == 0) {
                            gridForPrunedNode = 0;
                        } else {
                            gridForPrunedNode = 2;
                        }
                    } else if (controlRegions[0] == 0 && controlRegions[3] == 0) {
                        if (random == 0) {
                            gridForPrunedNode = 0;
                        } else {
                            gridForPrunedNode = 3;
                        }
                    } else if (controlRegions[1] == 0 && controlRegions[2] == 0) {
                        if (random == 0) {
                            gridForPrunedNode = 1;
                        } else {
                            gridForPrunedNode = 2;
                        }
                    } else if (controlRegions[1] == 0 && controlRegions[3] == 0) {
                        if (random == 0) {
                            gridForPrunedNode = 1;
                        } else {
                            gridForPrunedNode = 3;
                        }
                    } else {
                        if (random == 0) {
                            gridForPrunedNode = 2;
                        } else {
                            gridForPrunedNode = 3;
                        }
                    }
                } else if (minCount == 4 && min == 0) {
                    var random = Math.floor(Math.random() * 4);
                    gridForPrunedNode = random;
                } else {
                    gridForPrunedNode = minIndex;
                }
                if (gridForPrunedNode == 0) {
                    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() - nodeToConnect.getHeight() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getHeight() / 2);
                } else if (gridForPrunedNode == 1) {
                    prunedNode.setCenter(nodeToConnect.getCenterX() + nodeToConnect.getWidth() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getWidth() / 2, nodeToConnect.getCenterY());
                } else if (gridForPrunedNode == 2) {
                    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() + nodeToConnect.getHeight() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getHeight() / 2);
                } else {
                    prunedNode.setCenter(nodeToConnect.getCenterX() - nodeToConnect.getWidth() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getWidth() / 2, nodeToConnect.getCenterY());
                }
            };
            module1.exports = CoSELayout;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var coseBase = {};
            coseBase.layoutBase = __webpack_require__(0);
            coseBase.CoSEConstants = __webpack_require__(1);
            coseBase.CoSEEdge = __webpack_require__(2);
            coseBase.CoSEGraph = __webpack_require__(3);
            coseBase.CoSEGraphManager = __webpack_require__(4);
            coseBase.CoSELayout = __webpack_require__(6);
            coseBase.CoSENode = __webpack_require__(5);
            module1.exports = coseBase;
        /***/ }
    ]);
});
}}),
"[project]/node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
(function webpackUniversalModuleDefinition(root, factory) {
    if ("TURBOPACK compile-time truthy", 1) module.exports = factory(__turbopack_context__.r("[project]/node_modules/cose-base/cose-base.js [app-client] (ecmascript)"));
    else //TURBOPACK unreachable
    ;
})(("TURBOPACK member replacement", __turbopack_context__.e), function(__WEBPACK_EXTERNAL_MODULE_0__) {
    return /******/ function(modules) {
        /******/ // The module cache
        /******/ var installedModules = {};
        /******/ /******/ // The require function
        /******/ function __webpack_require__(moduleId) {
            /******/ /******/ // Check if module is in cache
            /******/ if (installedModules[moduleId]) {
                /******/ return installedModules[moduleId].exports;
            /******/ }
            /******/ // Create a new module (and put it into the cache)
            /******/ var module1 = installedModules[moduleId] = {
                /******/ i: moduleId,
                /******/ l: false,
                /******/ exports: {}
            };
            /******/ /******/ // Execute the module function
            /******/ modules[moduleId].call(module1.exports, module1, module1.exports, __webpack_require__);
            /******/ /******/ // Flag the module as loaded
            /******/ module1.l = true;
            /******/ /******/ // Return the exports of the module
            /******/ return module1.exports;
        /******/ }
        /******/ /******/ /******/ // expose the modules object (__webpack_modules__)
        /******/ __webpack_require__.m = modules;
        /******/ /******/ // expose the module cache
        /******/ __webpack_require__.c = installedModules;
        /******/ /******/ // identity function for calling harmony imports with the correct context
        /******/ __webpack_require__.i = function(value) {
            return value;
        };
        /******/ /******/ // define getter function for harmony exports
        /******/ __webpack_require__.d = function(exports, name, getter) {
            /******/ if (!__webpack_require__.o(exports, name)) {
                /******/ Object.defineProperty(exports, name, {
                    /******/ configurable: false,
                    /******/ enumerable: true,
                    /******/ get: getter
                });
            /******/ }
        /******/ };
        /******/ /******/ // getDefaultExport function for compatibility with non-harmony modules
        /******/ __webpack_require__.n = function(module1) {
            /******/ var getter = module1 && module1.__esModule ? /******/ function getDefault() {
                return module1['default'];
            } : /******/ function getModuleExports() {
                return module1;
            };
            /******/ __webpack_require__.d(getter, 'a', getter);
            /******/ return getter;
        /******/ };
        /******/ /******/ // Object.prototype.hasOwnProperty.call
        /******/ __webpack_require__.o = function(object, property) {
            return Object.prototype.hasOwnProperty.call(object, property);
        };
        /******/ /******/ // __webpack_public_path__
        /******/ __webpack_require__.p = "";
        /******/ /******/ // Load entry module and return exports
        /******/ return __webpack_require__(__webpack_require__.s = 1);
    /******/ }([
        function(module1, exports) {
            module1.exports = __WEBPACK_EXTERNAL_MODULE_0__;
        /***/ },
        function(module1, exports, __webpack_require__) {
            "use strict";
            var LayoutConstants = __webpack_require__(0).layoutBase.LayoutConstants;
            var FDLayoutConstants = __webpack_require__(0).layoutBase.FDLayoutConstants;
            var CoSEConstants = __webpack_require__(0).CoSEConstants;
            var CoSELayout = __webpack_require__(0).CoSELayout;
            var CoSENode = __webpack_require__(0).CoSENode;
            var PointD = __webpack_require__(0).layoutBase.PointD;
            var DimensionD = __webpack_require__(0).layoutBase.DimensionD;
            var defaults = {
                // Called on `layoutready`
                ready: function ready() {},
                // Called on `layoutstop`
                stop: function stop() {},
                // 'draft', 'default' or 'proof" 
                // - 'draft' fast cooling rate 
                // - 'default' moderate cooling rate 
                // - "proof" slow cooling rate
                quality: 'default',
                // include labels in node dimensions
                nodeDimensionsIncludeLabels: false,
                // number of ticks per frame; higher is faster but more jerky
                refresh: 30,
                // Whether to fit the network view after when done
                fit: true,
                // Padding on fit
                padding: 10,
                // Whether to enable incremental mode
                randomize: true,
                // Node repulsion (non overlapping) multiplier
                nodeRepulsion: 4500,
                // Ideal edge (non nested) length
                idealEdgeLength: 50,
                // Divisor to compute edge forces
                edgeElasticity: 0.45,
                // Nesting factor (multiplier) to compute ideal edge length for nested edges
                nestingFactor: 0.1,
                // Gravity force (constant)
                gravity: 0.25,
                // Maximum number of iterations to perform
                numIter: 2500,
                // For enabling tiling
                tile: true,
                // Type of layout animation. The option set is {'during', 'end', false}
                animate: 'end',
                // Duration for animate:end
                animationDuration: 500,
                // Represents the amount of the vertical space to put between the zero degree members during the tiling operation(can also be a function)
                tilingPaddingVertical: 10,
                // Represents the amount of the horizontal space to put between the zero degree members during the tiling operation(can also be a function)
                tilingPaddingHorizontal: 10,
                // Gravity range (constant) for compounds
                gravityRangeCompound: 1.5,
                // Gravity force (constant) for compounds
                gravityCompound: 1.0,
                // Gravity range (constant)
                gravityRange: 3.8,
                // Initial cooling factor for incremental layout
                initialEnergyOnIncremental: 0.5
            };
            function extend(defaults, options) {
                var obj = {};
                for(var i in defaults){
                    obj[i] = defaults[i];
                }
                for(var i in options){
                    obj[i] = options[i];
                }
                return obj;
            }
            ;
            function _CoSELayout(_options) {
                this.options = extend(defaults, _options);
                getUserOptions(this.options);
            }
            var getUserOptions = function getUserOptions(options) {
                if (options.nodeRepulsion != null) CoSEConstants.DEFAULT_REPULSION_STRENGTH = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH = options.nodeRepulsion;
                if (options.idealEdgeLength != null) CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = options.idealEdgeLength;
                if (options.edgeElasticity != null) CoSEConstants.DEFAULT_SPRING_STRENGTH = FDLayoutConstants.DEFAULT_SPRING_STRENGTH = options.edgeElasticity;
                if (options.nestingFactor != null) CoSEConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = options.nestingFactor;
                if (options.gravity != null) CoSEConstants.DEFAULT_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = options.gravity;
                if (options.numIter != null) CoSEConstants.MAX_ITERATIONS = FDLayoutConstants.MAX_ITERATIONS = options.numIter;
                if (options.gravityRange != null) CoSEConstants.DEFAULT_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = options.gravityRange;
                if (options.gravityCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = options.gravityCompound;
                if (options.gravityRangeCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = options.gravityRangeCompound;
                if (options.initialEnergyOnIncremental != null) CoSEConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = options.initialEnergyOnIncremental;
                if (options.quality == 'draft') LayoutConstants.QUALITY = 0;
                else if (options.quality == 'proof') LayoutConstants.QUALITY = 2;
                else LayoutConstants.QUALITY = 1;
                CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS = FDLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = options.nodeDimensionsIncludeLabels;
                CoSEConstants.DEFAULT_INCREMENTAL = FDLayoutConstants.DEFAULT_INCREMENTAL = LayoutConstants.DEFAULT_INCREMENTAL = !options.randomize;
                CoSEConstants.ANIMATE = FDLayoutConstants.ANIMATE = LayoutConstants.ANIMATE = options.animate;
                CoSEConstants.TILE = options.tile;
                CoSEConstants.TILING_PADDING_VERTICAL = typeof options.tilingPaddingVertical === 'function' ? options.tilingPaddingVertical.call() : options.tilingPaddingVertical;
                CoSEConstants.TILING_PADDING_HORIZONTAL = typeof options.tilingPaddingHorizontal === 'function' ? options.tilingPaddingHorizontal.call() : options.tilingPaddingHorizontal;
            };
            _CoSELayout.prototype.run = function() {
                var ready;
                var frameId;
                var options = this.options;
                var idToLNode = this.idToLNode = {};
                var layout = this.layout = new CoSELayout();
                var self = this;
                self.stopped = false;
                this.cy = this.options.cy;
                this.cy.trigger({
                    type: 'layoutstart',
                    layout: this
                });
                var gm = layout.newGraphManager();
                this.gm = gm;
                var nodes = this.options.eles.nodes();
                var edges = this.options.eles.edges();
                this.root = gm.addRoot();
                this.processChildrenList(this.root, this.getTopMostNodes(nodes), layout);
                for(var i = 0; i < edges.length; i++){
                    var edge = edges[i];
                    var sourceNode = this.idToLNode[edge.data("source")];
                    var targetNode = this.idToLNode[edge.data("target")];
                    if (sourceNode !== targetNode && sourceNode.getEdgesBetween(targetNode).length == 0) {
                        var e1 = gm.add(layout.newEdge(), sourceNode, targetNode);
                        e1.id = edge.id();
                    }
                }
                var getPositions = function getPositions(ele, i) {
                    if (typeof ele === "number") {
                        ele = i;
                    }
                    var theId = ele.data('id');
                    var lNode = self.idToLNode[theId];
                    return {
                        x: lNode.getRect().getCenterX(),
                        y: lNode.getRect().getCenterY()
                    };
                };
                /*
   * Reposition nodes in iterations animatedly
   */ var iterateAnimated = function iterateAnimated() {
                    // Thigs to perform after nodes are repositioned on screen
                    var afterReposition = function afterReposition() {
                        if (options.fit) {
                            options.cy.fit(options.eles, options.padding);
                        }
                        if (!ready) {
                            ready = true;
                            self.cy.one('layoutready', options.ready);
                            self.cy.trigger({
                                type: 'layoutready',
                                layout: self
                            });
                        }
                    };
                    var ticksPerFrame = self.options.refresh;
                    var isDone;
                    for(var i = 0; i < ticksPerFrame && !isDone; i++){
                        isDone = self.stopped || self.layout.tick();
                    }
                    // If layout is done
                    if (isDone) {
                        // If the layout is not a sublayout and it is successful perform post layout.
                        if (layout.checkLayoutSuccess() && !layout.isSubLayout) {
                            layout.doPostLayout();
                        }
                        // If layout has a tilingPostLayout function property call it.
                        if (layout.tilingPostLayout) {
                            layout.tilingPostLayout();
                        }
                        layout.isLayoutFinished = true;
                        self.options.eles.nodes().positions(getPositions);
                        afterReposition();
                        // trigger layoutstop when the layout stops (e.g. finishes)
                        self.cy.one('layoutstop', self.options.stop);
                        self.cy.trigger({
                            type: 'layoutstop',
                            layout: self
                        });
                        if (frameId) {
                            cancelAnimationFrame(frameId);
                        }
                        ready = false;
                        return;
                    }
                    var animationData = self.layout.getPositionsData(); // Get positions of layout nodes note that all nodes may not be layout nodes because of tiling
                    // Position nodes, for the nodes whose id does not included in data (because they are removed from their parents and included in dummy compounds)
                    // use position of their ancestors or dummy ancestors
                    options.eles.nodes().positions(function(ele, i) {
                        if (typeof ele === "number") {
                            ele = i;
                        }
                        // If ele is a compound node, then its position will be defined by its children
                        if (!ele.isParent()) {
                            var theId = ele.id();
                            var pNode = animationData[theId];
                            var temp = ele;
                            // If pNode is undefined search until finding position data of its first ancestor (It may be dummy as well)
                            while(pNode == null){
                                pNode = animationData[temp.data('parent')] || animationData['DummyCompound_' + temp.data('parent')];
                                animationData[theId] = pNode;
                                temp = temp.parent()[0];
                                if (temp == undefined) {
                                    break;
                                }
                            }
                            if (pNode != null) {
                                return {
                                    x: pNode.x,
                                    y: pNode.y
                                };
                            } else {
                                return {
                                    x: ele.position('x'),
                                    y: ele.position('y')
                                };
                            }
                        }
                    });
                    afterReposition();
                    frameId = requestAnimationFrame(iterateAnimated);
                };
                /*
  * Listen 'layoutstarted' event and start animated iteration if animate option is 'during'
  */ layout.addListener('layoutstarted', function() {
                    if (self.options.animate === 'during') {
                        frameId = requestAnimationFrame(iterateAnimated);
                    }
                });
                layout.runLayout(); // Run cose layout
                /*
   * If animate option is not 'during' ('end' or false) perform these here (If it is 'during' similar things are already performed)
   */ if (this.options.animate !== "during") {
                    self.options.eles.nodes().not(":parent").layoutPositions(self, self.options, getPositions); // Use layout positions to reposition the nodes it considers the options parameter
                    ready = false;
                }
                return this; // chaining
            };
            //Get the top most ones of a list of nodes
            _CoSELayout.prototype.getTopMostNodes = function(nodes) {
                var nodesMap = {};
                for(var i = 0; i < nodes.length; i++){
                    nodesMap[nodes[i].id()] = true;
                }
                var roots = nodes.filter(function(ele, i) {
                    if (typeof ele === "number") {
                        ele = i;
                    }
                    var parent = ele.parent()[0];
                    while(parent != null){
                        if (nodesMap[parent.id()]) {
                            return false;
                        }
                        parent = parent.parent()[0];
                    }
                    return true;
                });
                return roots;
            };
            _CoSELayout.prototype.processChildrenList = function(parent, children, layout) {
                var size = children.length;
                for(var i = 0; i < size; i++){
                    var theChild = children[i];
                    var children_of_children = theChild.children();
                    var theNode;
                    var dimensions = theChild.layoutDimensions({
                        nodeDimensionsIncludeLabels: this.options.nodeDimensionsIncludeLabels
                    });
                    if (theChild.outerWidth() != null && theChild.outerHeight() != null) {
                        theNode = parent.add(new CoSENode(layout.graphManager, new PointD(theChild.position('x') - dimensions.w / 2, theChild.position('y') - dimensions.h / 2), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));
                    } else {
                        theNode = parent.add(new CoSENode(this.graphManager));
                    }
                    // Attach id to the layout node
                    theNode.id = theChild.data("id");
                    // Attach the paddings of cy node to layout node
                    theNode.paddingLeft = parseInt(theChild.css('padding'));
                    theNode.paddingTop = parseInt(theChild.css('padding'));
                    theNode.paddingRight = parseInt(theChild.css('padding'));
                    theNode.paddingBottom = parseInt(theChild.css('padding'));
                    //Attach the label properties to compound if labels will be included in node dimensions  
                    if (this.options.nodeDimensionsIncludeLabels) {
                        if (theChild.isParent()) {
                            var labelWidth = theChild.boundingBox({
                                includeLabels: true,
                                includeNodes: false
                            }).w;
                            var labelHeight = theChild.boundingBox({
                                includeLabels: true,
                                includeNodes: false
                            }).h;
                            var labelPos = theChild.css("text-halign");
                            theNode.labelWidth = labelWidth;
                            theNode.labelHeight = labelHeight;
                            theNode.labelPos = labelPos;
                        }
                    }
                    // Map the layout node
                    this.idToLNode[theChild.data("id")] = theNode;
                    if (isNaN(theNode.rect.x)) {
                        theNode.rect.x = 0;
                    }
                    if (isNaN(theNode.rect.y)) {
                        theNode.rect.y = 0;
                    }
                    if (children_of_children != null && children_of_children.length > 0) {
                        var theNewGraph;
                        theNewGraph = layout.getGraphManager().add(layout.newGraph(), theNode);
                        this.processChildrenList(theNewGraph, children_of_children, layout);
                    }
                }
            };
            /**
 * @brief : called on continuous layouts to stop them before they finish
 */ _CoSELayout.prototype.stop = function() {
                this.stopped = true;
                return this; // chaining
            };
            var register = function register(cytoscape1) {
                //  var Layout = getLayout( cytoscape );
                cytoscape1('layout', 'cose-bilkent', _CoSELayout);
            };
            // auto reg for globals
            if (typeof cytoscape !== 'undefined') {
                register(cytoscape);
            }
            module1.exports = register;
        /***/ }
    ]);
});
}}),
"[project]/node_modules/mermaid/dist/chunks/mermaid.core/mindmap-definition-6CBA2TL7.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "diagram": ()=>diagram
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$QESNASVV$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-QESNASVV.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-P3VETL53.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs [app-client] (ecmascript)");
// src/diagrams/mindmap/mindmapRenderer.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cytoscape$2f$dist$2f$cytoscape$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cytoscape/dist/cytoscape.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cytoscape$2d$cose$2d$bilkent$2f$cytoscape$2d$cose$2d$bilkent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
// src/diagrams/mindmap/styles.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$darken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__darken$3e$__ = __turbopack_context__.i("[project]/node_modules/khroma/dist/methods/darken.js [app-client] (ecmascript) <export default as darken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$lighten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__lighten$3e$__ = __turbopack_context__.i("[project]/node_modules/khroma/dist/methods/lighten.js [app-client] (ecmascript) <export default as lighten>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$is_dark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isDark$3e$__ = __turbopack_context__.i("[project]/node_modules/khroma/dist/methods/is_dark.js [app-client] (ecmascript) <export default as isDark>");
var _class;
;
;
;
;
// src/diagrams/mindmap/parser/mindmap.jison
var parser = function() {
    var o = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, "o"), $V0 = [
        1,
        4
    ], $V1 = [
        1,
        13
    ], $V2 = [
        1,
        12
    ], $V3 = [
        1,
        15
    ], $V4 = [
        1,
        16
    ], $V5 = [
        1,
        20
    ], $V6 = [
        1,
        19
    ], $V7 = [
        6,
        7,
        8
    ], $V8 = [
        1,
        26
    ], $V9 = [
        1,
        24
    ], $Va = [
        1,
        25
    ], $Vb = [
        6,
        7,
        11
    ], $Vc = [
        1,
        6,
        13,
        15,
        16,
        19,
        22
    ], $Vd = [
        1,
        33
    ], $Ve = [
        1,
        34
    ], $Vf = [
        1,
        6,
        7,
        11,
        13,
        15,
        16,
        19,
        22
    ];
    var parser2 = {
        trace: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function trace() {}, "trace"),
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "mindMap": 4,
            "spaceLines": 5,
            "SPACELINE": 6,
            "NL": 7,
            "MINDMAP": 8,
            "document": 9,
            "stop": 10,
            "EOF": 11,
            "statement": 12,
            "SPACELIST": 13,
            "node": 14,
            "ICON": 15,
            "CLASS": 16,
            "nodeWithId": 17,
            "nodeWithoutId": 18,
            "NODE_DSTART": 19,
            "NODE_DESCR": 20,
            "NODE_DEND": 21,
            "NODE_ID": 22,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            6: "SPACELINE",
            7: "NL",
            8: "MINDMAP",
            11: "EOF",
            13: "SPACELIST",
            15: "ICON",
            16: "CLASS",
            19: "NODE_DSTART",
            20: "NODE_DESCR",
            21: "NODE_DEND",
            22: "NODE_ID"
        },
        productions_: [
            0,
            [
                3,
                1
            ],
            [
                3,
                2
            ],
            [
                5,
                1
            ],
            [
                5,
                2
            ],
            [
                5,
                2
            ],
            [
                4,
                2
            ],
            [
                4,
                3
            ],
            [
                10,
                1
            ],
            [
                10,
                1
            ],
            [
                10,
                1
            ],
            [
                10,
                2
            ],
            [
                10,
                2
            ],
            [
                9,
                3
            ],
            [
                9,
                2
            ],
            [
                12,
                2
            ],
            [
                12,
                2
            ],
            [
                12,
                2
            ],
            [
                12,
                1
            ],
            [
                12,
                1
            ],
            [
                12,
                1
            ],
            [
                12,
                1
            ],
            [
                12,
                1
            ],
            [
                14,
                1
            ],
            [
                14,
                1
            ],
            [
                18,
                3
            ],
            [
                17,
                1
            ],
            [
                17,
                4
            ]
        ],
        performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 6:
                case 7:
                    return yy;
                    //TURBOPACK unreachable
                    ;
                case 8:
                    yy.getLogger().trace("Stop NL ");
                    break;
                case 9:
                    yy.getLogger().trace("Stop EOF ");
                    break;
                case 11:
                    yy.getLogger().trace("Stop NL2 ");
                    break;
                case 12:
                    yy.getLogger().trace("Stop EOF2 ");
                    break;
                case 15:
                    yy.getLogger().info("Node: ", $$[$0].id);
                    yy.addNode($$[$0 - 1].length, $$[$0].id, $$[$0].descr, $$[$0].type);
                    break;
                case 16:
                    yy.getLogger().trace("Icon: ", $$[$0]);
                    yy.decorateNode({
                        icon: $$[$0]
                    });
                    break;
                case 17:
                case 21:
                    yy.decorateNode({
                        class: $$[$0]
                    });
                    break;
                case 18:
                    yy.getLogger().trace("SPACELIST");
                    break;
                case 19:
                    yy.getLogger().trace("Node: ", $$[$0].id);
                    yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);
                    break;
                case 20:
                    yy.decorateNode({
                        icon: $$[$0]
                    });
                    break;
                case 25:
                    yy.getLogger().trace("node found ..", $$[$0 - 2]);
                    this.$ = {
                        id: $$[$0 - 1],
                        descr: $$[$0 - 1],
                        type: yy.getType($$[$0 - 2], $$[$0])
                    };
                    break;
                case 26:
                    this.$ = {
                        id: $$[$0],
                        descr: $$[$0],
                        type: yy.nodeType.DEFAULT
                    };
                    break;
                case 27:
                    yy.getLogger().trace("node found ..", $$[$0 - 3]);
                    this.$ = {
                        id: $$[$0 - 3],
                        descr: $$[$0 - 1],
                        type: yy.getType($$[$0 - 2], $$[$0])
                    };
                    break;
            }
        }, "anonymous"),
        table: [
            {
                3: 1,
                4: 2,
                5: 3,
                6: [
                    1,
                    5
                ],
                8: $V0
            },
            {
                1: [
                    3
                ]
            },
            {
                1: [
                    2,
                    1
                ]
            },
            {
                4: 6,
                6: [
                    1,
                    7
                ],
                7: [
                    1,
                    8
                ],
                8: $V0
            },
            {
                6: $V1,
                7: [
                    1,
                    10
                ],
                9: 9,
                12: 11,
                13: $V2,
                14: 14,
                15: $V3,
                16: $V4,
                17: 17,
                18: 18,
                19: $V5,
                22: $V6
            },
            o($V7, [
                2,
                3
            ]),
            {
                1: [
                    2,
                    2
                ]
            },
            o($V7, [
                2,
                4
            ]),
            o($V7, [
                2,
                5
            ]),
            {
                1: [
                    2,
                    6
                ],
                6: $V1,
                12: 21,
                13: $V2,
                14: 14,
                15: $V3,
                16: $V4,
                17: 17,
                18: 18,
                19: $V5,
                22: $V6
            },
            {
                6: $V1,
                9: 22,
                12: 11,
                13: $V2,
                14: 14,
                15: $V3,
                16: $V4,
                17: 17,
                18: 18,
                19: $V5,
                22: $V6
            },
            {
                6: $V8,
                7: $V9,
                10: 23,
                11: $Va
            },
            o($Vb, [
                2,
                22
            ], {
                17: 17,
                18: 18,
                14: 27,
                15: [
                    1,
                    28
                ],
                16: [
                    1,
                    29
                ],
                19: $V5,
                22: $V6
            }),
            o($Vb, [
                2,
                18
            ]),
            o($Vb, [
                2,
                19
            ]),
            o($Vb, [
                2,
                20
            ]),
            o($Vb, [
                2,
                21
            ]),
            o($Vb, [
                2,
                23
            ]),
            o($Vb, [
                2,
                24
            ]),
            o($Vb, [
                2,
                26
            ], {
                19: [
                    1,
                    30
                ]
            }),
            {
                20: [
                    1,
                    31
                ]
            },
            {
                6: $V8,
                7: $V9,
                10: 32,
                11: $Va
            },
            {
                1: [
                    2,
                    7
                ],
                6: $V1,
                12: 21,
                13: $V2,
                14: 14,
                15: $V3,
                16: $V4,
                17: 17,
                18: 18,
                19: $V5,
                22: $V6
            },
            o($Vc, [
                2,
                14
            ], {
                7: $Vd,
                11: $Ve
            }),
            o($Vf, [
                2,
                8
            ]),
            o($Vf, [
                2,
                9
            ]),
            o($Vf, [
                2,
                10
            ]),
            o($Vb, [
                2,
                15
            ]),
            o($Vb, [
                2,
                16
            ]),
            o($Vb, [
                2,
                17
            ]),
            {
                20: [
                    1,
                    35
                ]
            },
            {
                21: [
                    1,
                    36
                ]
            },
            o($Vc, [
                2,
                13
            ], {
                7: $Vd,
                11: $Ve
            }),
            o($Vf, [
                2,
                11
            ]),
            o($Vf, [
                2,
                12
            ]),
            {
                21: [
                    1,
                    37
                ]
            },
            o($Vb, [
                2,
                25
            ]),
            o($Vb, [
                2,
                27
            ])
        ],
        defaultActions: {
            2: [
                2,
                1
            ],
            6: [
                2,
                2
            ]
        },
        parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        }, "parseError"),
        parse: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function popStack(n) {
                stack.length = stack.length - 2 * n;
                vstack.length = vstack.length - n;
                lstack.length = lstack.length - n;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(popStack, "popStack");
            function lex() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(lex, "lex");
            var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        if (!preErrorSymbol) {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                            if (recovering > 0) {
                                recovering--;
                            }
                        } else {
                            symbol = preErrorSymbol;
                            preErrorSymbol = null;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }, "parse")
    };
    var lexer = /* @__PURE__ */ function() {
        var lexer2 = {
            EOF: 1,
            parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            }, "parseError"),
            // resets the lexer, sets new input
            setInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            }, "setInput"),
            // consumes and returns one char from the input
            input: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            }, "input"),
            // unshifts one char (or a string) into the input
            unput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            }, "unput"),
            // When called from action, caches matched text and appends it on next action
            more: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                this._more = true;
                return this;
            }, "more"),
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            }, "reject"),
            // retain first n characters of the match
            less: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(n) {
                this.unput(this.match.slice(n));
            }, "less"),
            // displays already matched input, i.e. for error messages
            pastInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            }, "pastInput"),
            // displays upcoming input, i.e. for error messages
            upcomingInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            }, "upcomingInput"),
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            }, "showPosition"),
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            }, "test_match"),
            // return next match in input
            next: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            }, "next"),
            // return next match that has a token
            lex: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            }, "lex"),
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function begin(condition) {
                this.conditionStack.push(condition);
            }, "begin"),
            // pop the previously active lexer condition state off the condition stack
            popState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            }, "popState"),
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            }, "_currentRules"),
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            }, "topState"),
            // alias for begin(condition)
            pushState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function pushState(condition) {
                this.begin(condition);
            }, "pushState"),
            // return the number of states currently on the stack
            stateStackSize: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function stateStackSize() {
                return this.conditionStack.length;
            }, "stateStackSize"),
            options: {
                "case-insensitive": true
            },
            performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                var YYSTATE = YY_START;
                switch($avoiding_name_collisions){
                    case 0:
                        yy.getLogger().trace("Found comment", yy_.yytext);
                        return 6;
                        //TURBOPACK unreachable
                        ;
                    case 1:
                        return 8;
                        //TURBOPACK unreachable
                        ;
                    case 2:
                        this.begin("CLASS");
                        break;
                    case 3:
                        this.popState();
                        return 16;
                        //TURBOPACK unreachable
                        ;
                    case 4:
                        this.popState();
                        break;
                    case 5:
                        yy.getLogger().trace("Begin icon");
                        this.begin("ICON");
                        break;
                    case 6:
                        yy.getLogger().trace("SPACELINE");
                        return 6;
                        //TURBOPACK unreachable
                        ;
                    case 7:
                        return 7;
                        //TURBOPACK unreachable
                        ;
                    case 8:
                        return 15;
                        //TURBOPACK unreachable
                        ;
                    case 9:
                        yy.getLogger().trace("end icon");
                        this.popState();
                        break;
                    case 10:
                        yy.getLogger().trace("Exploding node");
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 11:
                        yy.getLogger().trace("Cloud");
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 12:
                        yy.getLogger().trace("Explosion Bang");
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 13:
                        yy.getLogger().trace("Cloud Bang");
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 14:
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 15:
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 16:
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 17:
                        this.begin("NODE");
                        return 19;
                        //TURBOPACK unreachable
                        ;
                    case 18:
                        return 13;
                        //TURBOPACK unreachable
                        ;
                    case 19:
                        return 22;
                        //TURBOPACK unreachable
                        ;
                    case 20:
                        return 11;
                        //TURBOPACK unreachable
                        ;
                    case 21:
                        this.begin("NSTR2");
                        break;
                    case 22:
                        return "NODE_DESCR";
                        //TURBOPACK unreachable
                        ;
                    case 23:
                        this.popState();
                        break;
                    case 24:
                        yy.getLogger().trace("Starting NSTR");
                        this.begin("NSTR");
                        break;
                    case 25:
                        yy.getLogger().trace("description:", yy_.yytext);
                        return "NODE_DESCR";
                        //TURBOPACK unreachable
                        ;
                    case 26:
                        this.popState();
                        break;
                    case 27:
                        this.popState();
                        yy.getLogger().trace("node end ))");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 28:
                        this.popState();
                        yy.getLogger().trace("node end )");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 29:
                        this.popState();
                        yy.getLogger().trace("node end ...", yy_.yytext);
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 30:
                        this.popState();
                        yy.getLogger().trace("node end ((");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 31:
                        this.popState();
                        yy.getLogger().trace("node end (-");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 32:
                        this.popState();
                        yy.getLogger().trace("node end (-");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 33:
                        this.popState();
                        yy.getLogger().trace("node end ((");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 34:
                        this.popState();
                        yy.getLogger().trace("node end ((");
                        return "NODE_DEND";
                        //TURBOPACK unreachable
                        ;
                    case 35:
                        yy.getLogger().trace("Long description:", yy_.yytext);
                        return 20;
                        //TURBOPACK unreachable
                        ;
                    case 36:
                        yy.getLogger().trace("Long description:", yy_.yytext);
                        return 20;
                        //TURBOPACK unreachable
                        ;
                }
            }, "anonymous"),
            rules: [
                /^(?:\s*%%.*)/i,
                /^(?:mindmap\b)/i,
                /^(?::::)/i,
                /^(?:.+)/i,
                /^(?:\n)/i,
                /^(?:::icon\()/i,
                /^(?:[\s]+[\n])/i,
                /^(?:[\n]+)/i,
                /^(?:[^\)]+)/i,
                /^(?:\))/i,
                /^(?:-\))/i,
                /^(?:\(-)/i,
                /^(?:\)\))/i,
                /^(?:\))/i,
                /^(?:\(\()/i,
                /^(?:\{\{)/i,
                /^(?:\()/i,
                /^(?:\[)/i,
                /^(?:[\s]+)/i,
                /^(?:[^\(\[\n\)\{\}]+)/i,
                /^(?:$)/i,
                /^(?:["][`])/i,
                /^(?:[^`"]+)/i,
                /^(?:[`]["])/i,
                /^(?:["])/i,
                /^(?:[^"]+)/i,
                /^(?:["])/i,
                /^(?:[\)]\))/i,
                /^(?:[\)])/i,
                /^(?:[\]])/i,
                /^(?:\}\})/i,
                /^(?:\(-)/i,
                /^(?:-\))/i,
                /^(?:\(\()/i,
                /^(?:\()/i,
                /^(?:[^\)\]\(\}]+)/i,
                /^(?:.+(?!\(\())/i
            ],
            conditions: {
                "CLASS": {
                    "rules": [
                        3,
                        4
                    ],
                    "inclusive": false
                },
                "ICON": {
                    "rules": [
                        8,
                        9
                    ],
                    "inclusive": false
                },
                "NSTR2": {
                    "rules": [
                        22,
                        23
                    ],
                    "inclusive": false
                },
                "NSTR": {
                    "rules": [
                        25,
                        26
                    ],
                    "inclusive": false
                },
                "NODE": {
                    "rules": [
                        21,
                        24,
                        27,
                        28,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        1,
                        2,
                        5,
                        6,
                        7,
                        10,
                        11,
                        12,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(Parser, "Parser");
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
var mindmap_default = parser;
// src/diagrams/mindmap/mindmapDb.ts
var nodeType = {
    DEFAULT: 0,
    NO_BORDER: 0,
    ROUNDED_RECT: 1,
    RECT: 2,
    CIRCLE: 3,
    CLOUD: 4,
    BANG: 5,
    HEXAGON: 6
};
var MindmapDB = (_class = class {
    clear() {
        this.nodes = [];
        this.count = 0;
        this.elements = {};
    }
    getParent(level) {
        for(let i = this.nodes.length - 1; i >= 0; i--){
            if (this.nodes[i].level < level) {
                return this.nodes[i];
            }
        }
        return null;
    }
    getMindmap() {
        return this.nodes.length > 0 ? this.nodes[0] : null;
    }
    addNode(level, id, descr, type) {
        var _conf_mindmap, _conf_mindmap1;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].info("addNode", level, id, descr, type);
        const conf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])();
        var _conf_mindmap_padding;
        let padding = (_conf_mindmap_padding = (_conf_mindmap = conf.mindmap) === null || _conf_mindmap === void 0 ? void 0 : _conf_mindmap.padding) !== null && _conf_mindmap_padding !== void 0 ? _conf_mindmap_padding : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].mindmap.padding;
        switch(type){
            case this.nodeType.ROUNDED_RECT:
            case this.nodeType.RECT:
            case this.nodeType.HEXAGON:
                padding *= 2;
                break;
        }
        var _conf_mindmap_maxNodeWidth;
        const node = {
            id: this.count++,
            nodeId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeText"])(id, conf),
            level,
            descr: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeText"])(descr, conf),
            type,
            children: [],
            width: (_conf_mindmap_maxNodeWidth = (_conf_mindmap1 = conf.mindmap) === null || _conf_mindmap1 === void 0 ? void 0 : _conf_mindmap1.maxNodeWidth) !== null && _conf_mindmap_maxNodeWidth !== void 0 ? _conf_mindmap_maxNodeWidth : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].mindmap.maxNodeWidth,
            padding
        };
        const parent = this.getParent(level);
        if (parent) {
            parent.children.push(node);
            this.nodes.push(node);
        } else {
            if (this.nodes.length === 0) {
                this.nodes.push(node);
            } else {
                throw new Error('There can be only one root. No parent could be found for ("'.concat(node.descr, '")'));
            }
        }
    }
    getType(startStr, endStr) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("In get type", startStr, endStr);
        switch(startStr){
            case "[":
                return this.nodeType.RECT;
            case "(":
                return endStr === ")" ? this.nodeType.ROUNDED_RECT : this.nodeType.CLOUD;
            case "((":
                return this.nodeType.CIRCLE;
            case ")":
                return this.nodeType.CLOUD;
            case "))":
                return this.nodeType.BANG;
            case "{{":
                return this.nodeType.HEXAGON;
            default:
                return this.nodeType.DEFAULT;
        }
    }
    setElementForId(id, element) {
        this.elements[id] = element;
    }
    getElementById(id) {
        return this.elements[id];
    }
    decorateNode(decoration) {
        if (!decoration) {
            return;
        }
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])();
        const node = this.nodes[this.nodes.length - 1];
        if (decoration.icon) {
            node.icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeText"])(decoration.icon, config);
        }
        if (decoration.class) {
            node.class = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeText"])(decoration.class, config);
        }
    }
    type2Str(type) {
        switch(type){
            case this.nodeType.DEFAULT:
                return "no-border";
            case this.nodeType.RECT:
                return "rect";
            case this.nodeType.ROUNDED_RECT:
                return "rounded-rect";
            case this.nodeType.CIRCLE:
                return "circle";
            case this.nodeType.CLOUD:
                return "cloud";
            case this.nodeType.BANG:
                return "bang";
            case this.nodeType.HEXAGON:
                return "hexgon";
            // cspell: disable-line
            default:
                return "no-border";
        }
    }
    getLogger() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"];
    }
    constructor(){
        this.nodes = [];
        this.count = 0;
        this.elements = {};
        this.getLogger = this.getLogger.bind(this);
        this.nodeType = nodeType;
        this.clear();
        this.getType = this.getType.bind(this);
        this.getMindmap = this.getMindmap.bind(this);
        this.getElementById = this.getElementById.bind(this);
        this.getParent = this.getParent.bind(this);
        this.getMindmap = this.getMindmap.bind(this);
        this.addNode = this.addNode.bind(this);
        this.decorateNode = this.decorateNode.bind(this);
    }
}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(_class, "MindmapDB"), _class);
;
;
;
// src/diagrams/mindmap/svgDraw.ts
var MAX_SECTIONS = 12;
var defaultBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node, section) {
    const rd = 5;
    elem.append("path").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("d", "M0 ".concat(node.height - rd, " v").concat(-node.height + 2 * rd, " q0,-5 5,-5 h").concat(node.width - 2 * rd, " q5,0 5,5 v").concat(node.height - rd, " H0 Z"));
    elem.append("line").attr("class", "node-line-" + section).attr("x1", 0).attr("y1", node.height).attr("x2", node.width).attr("y2", node.height);
}, "defaultBkg");
var rectBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node) {
    elem.append("rect").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("height", node.height).attr("width", node.width);
}, "rectBkg");
var cloudBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node) {
    const w = node.width;
    const h = node.height;
    const r1 = 0.15 * w;
    const r2 = 0.25 * w;
    const r3 = 0.35 * w;
    const r4 = 0.2 * w;
    elem.append("path").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("d", "M0 0 a".concat(r1, ",").concat(r1, " 0 0,1 ").concat(w * 0.25, ",").concat(-1 * w * 0.1, "\n      a").concat(r3, ",").concat(r3, " 1 0,1 ").concat(w * 0.4, ",").concat(-1 * w * 0.1, "\n      a").concat(r2, ",").concat(r2, " 1 0,1 ").concat(w * 0.35, ",").concat(1 * w * 0.2, "\n\n      a").concat(r1, ",").concat(r1, " 1 0,1 ").concat(w * 0.15, ",").concat(1 * h * 0.35, "\n      a").concat(r4, ",").concat(r4, " 1 0,1 ").concat(-1 * w * 0.15, ",").concat(1 * h * 0.65, "\n\n      a").concat(r2, ",").concat(r1, " 1 0,1 ").concat(-1 * w * 0.25, ",").concat(w * 0.15, "\n      a").concat(r3, ",").concat(r3, " 1 0,1 ").concat(-1 * w * 0.5, ",", 0, "\n      a").concat(r1, ",").concat(r1, " 1 0,1 ").concat(-1 * w * 0.25, ",").concat(-1 * w * 0.15, "\n\n      a").concat(r1, ",").concat(r1, " 1 0,1 ").concat(-1 * w * 0.1, ",").concat(-1 * h * 0.35, "\n      a").concat(r4, ",").concat(r4, " 1 0,1 ").concat(w * 0.1, ",").concat(-1 * h * 0.65, "\n\n    H0 V0 Z"));
}, "cloudBkg");
var bangBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node) {
    const w = node.width;
    const h = node.height;
    const r = 0.15 * w;
    elem.append("path").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("d", "M0 0 a".concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.25, ",").concat(-1 * h * 0.1, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.25, ",", 0, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.25, ",", 0, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.25, ",").concat(1 * h * 0.1, "\n\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.15, ",").concat(1 * h * 0.33, "\n      a").concat(r * 0.8, ",").concat(r * 0.8, " 1 0,0 ", 0, ",").concat(1 * h * 0.34, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.15, ",").concat(1 * h * 0.33, "\n\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.25, ",").concat(h * 0.15, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.25, ",", 0, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.25, ",", 0, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.25, ",").concat(-1 * h * 0.15, "\n\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(-1 * w * 0.1, ",").concat(-1 * h * 0.33, "\n      a").concat(r * 0.8, ",").concat(r * 0.8, " 1 0,0 ", 0, ",").concat(-1 * h * 0.34, "\n      a").concat(r, ",").concat(r, " 1 0,0 ").concat(w * 0.1, ",").concat(-1 * h * 0.33, "\n\n    H0 V0 Z"));
}, "bangBkg");
var circleBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node) {
    elem.append("circle").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("r", node.width / 2);
}, "circleBkg");
function insertPolygonShape(parent, w, h, points, node) {
    return parent.insert("polygon", ":first-child").attr("points", points.map(function(d) {
        return d.x + "," + d.y;
    }).join(" ")).attr("transform", "translate(" + (node.width - w) / 2 + ", " + h + ")");
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(insertPolygonShape, "insertPolygonShape");
var hexagonBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(_db, elem, node) {
    const h = node.height;
    const f = 4;
    const m = h / f;
    const w = node.width - node.padding + 2 * m;
    const points = [
        {
            x: m,
            y: 0
        },
        {
            x: w - m,
            y: 0
        },
        {
            x: w,
            y: -h / 2
        },
        {
            x: w - m,
            y: -h
        },
        {
            x: m,
            y: -h
        },
        {
            x: 0,
            y: -h / 2
        }
    ];
    insertPolygonShape(elem, w, h, points, node);
}, "hexagonBkg");
var roundedRectBkg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, elem, node) {
    elem.append("rect").attr("id", "node-" + node.id).attr("class", "node-bkg node-" + db.type2Str(node.type)).attr("height", node.height).attr("rx", node.padding).attr("ry", node.padding).attr("width", node.width);
}, "roundedRectBkg");
var drawNode = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(async function(db, elem, node, fullSection, conf) {
    const htmlLabels = conf.htmlLabels;
    const section = fullSection % (MAX_SECTIONS - 1);
    const nodeElem = elem.append("g");
    node.section = section;
    let sectionClass = "section-" + section;
    if (section < 0) {
        sectionClass += " section-root";
    }
    nodeElem.attr("class", (node.class ? node.class + " " : "") + "mindmap-node " + sectionClass);
    const bkgElem = nodeElem.append("g");
    const textElem = nodeElem.append("g");
    const description = node.descr.replace(/(<br\/*>)/g, "\n");
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$QESNASVV$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createText"])(textElem, description, {
        useHtmlLabels: htmlLabels,
        width: node.width,
        classes: "mindmap-node-label"
    }, conf);
    if (!htmlLabels) {
        textElem.attr("dy", "1em").attr("alignment-baseline", "middle").attr("dominant-baseline", "middle").attr("text-anchor", "middle");
    }
    const bbox = textElem.node().getBBox();
    const [fontSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFontSize"])(conf.fontSize);
    node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;
    node.width = bbox.width + 2 * node.padding;
    if (node.icon) {
        if (node.type === db.nodeType.CIRCLE) {
            node.height += 50;
            node.width += 50;
            const icon = nodeElem.append("foreignObject").attr("height", "50px").attr("width", node.width).attr("style", "text-align: center;");
            icon.append("div").attr("class", "icon-container").append("i").attr("class", "node-icon-" + section + " " + node.icon);
            textElem.attr("transform", "translate(" + node.width / 2 + ", " + (node.height / 2 - 1.5 * node.padding) + ")");
        } else {
            node.width += 50;
            const orgHeight = node.height;
            node.height = Math.max(orgHeight, 60);
            const heightDiff = Math.abs(node.height - orgHeight);
            const icon = nodeElem.append("foreignObject").attr("width", "60px").attr("height", node.height).attr("style", "text-align: center;margin-top:" + heightDiff / 2 + "px;");
            icon.append("div").attr("class", "icon-container").append("i").attr("class", "node-icon-" + section + " " + node.icon);
            textElem.attr("transform", "translate(" + (25 + node.width / 2) + ", " + (heightDiff / 2 + node.padding / 2) + ")");
        }
    } else {
        if (!htmlLabels) {
            const dx = node.width / 2;
            const dy = node.padding / 2;
            textElem.attr("transform", "translate(" + dx + ", " + dy + ")");
        } else {
            const dx = (node.width - bbox.width) / 2;
            const dy = (node.height - bbox.height) / 2;
            textElem.attr("transform", "translate(" + dx + ", " + dy + ")");
        }
    }
    switch(node.type){
        case db.nodeType.DEFAULT:
            defaultBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.ROUNDED_RECT:
            roundedRectBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.RECT:
            rectBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.CIRCLE:
            bkgElem.attr("transform", "translate(" + node.width / 2 + ", " + +node.height / 2 + ")");
            circleBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.CLOUD:
            cloudBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.BANG:
            bangBkg(db, bkgElem, node, section);
            break;
        case db.nodeType.HEXAGON:
            hexagonBkg(db, bkgElem, node, section);
            break;
    }
    db.setElementForId(node.id, nodeElem);
    return node.height;
}, "drawNode");
var positionNode = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(db, node) {
    const nodeElem = db.getElementById(node.id);
    const x = node.x || 0;
    const y = node.y || 0;
    nodeElem.attr("transform", "translate(" + x + "," + y + ")");
}, "positionNode");
// src/diagrams/mindmap/mindmapRenderer.ts
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cytoscape$2f$dist$2f$cytoscape$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cytoscape$2d$cose$2d$bilkent$2f$cytoscape$2d$cose$2d$bilkent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
async function drawNodes(db, svg, mindmap, section, conf) {
    await drawNode(db, svg, mindmap, section, conf);
    if (mindmap.children) {
        await Promise.all(mindmap.children.map((child, index)=>drawNodes(db, svg, child, section < 0 ? index : section, conf)));
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawNodes, "drawNodes");
function drawEdges(edgesEl, cy) {
    cy.edges().map((edge, id)=>{
        const data = edge.data();
        if (edge[0]._private.bodyBounds) {
            const bounds = edge[0]._private.rscratch;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].trace("Edge: ", id, data);
            edgesEl.insert("path").attr("d", "M ".concat(bounds.startX, ",").concat(bounds.startY, " L ").concat(bounds.midX, ",").concat(bounds.midY, " L").concat(bounds.endX, ",").concat(bounds.endY, " ")).attr("class", "edge section-edge-" + data.section + " edge-depth-" + data.depth);
        }
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawEdges, "drawEdges");
function addNodes(mindmap, cy, conf, level) {
    cy.add({
        group: "nodes",
        data: {
            id: mindmap.id.toString(),
            labelText: mindmap.descr,
            height: mindmap.height,
            width: mindmap.width,
            level,
            nodeId: mindmap.id,
            padding: mindmap.padding,
            type: mindmap.type
        },
        position: {
            x: mindmap.x,
            y: mindmap.y
        }
    });
    if (mindmap.children) {
        mindmap.children.forEach((child)=>{
            addNodes(child, cy, conf, level + 1);
            cy.add({
                group: "edges",
                data: {
                    id: "".concat(mindmap.id, "_").concat(child.id),
                    source: mindmap.id,
                    target: child.id,
                    depth: level,
                    section: child.section
                }
            });
        });
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(addNodes, "addNodes");
function layoutMindmap(node, conf) {
    return new Promise((resolve)=>{
        const renderEl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body").append("div").attr("id", "cy").attr("style", "display:none");
        const cy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cytoscape$2f$dist$2f$cytoscape$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            container: document.getElementById("cy"),
            // container to render in
            style: [
                {
                    selector: "edge",
                    style: {
                        "curve-style": "bezier"
                    }
                }
            ]
        });
        renderEl.remove();
        addNodes(node, cy, conf, 0);
        cy.nodes().forEach(function(n) {
            n.layoutDimensions = ()=>{
                const data = n.data();
                return {
                    w: data.width,
                    h: data.height
                };
            };
        });
        cy.layout({
            name: "cose-bilkent",
            // @ts-ignore Types for cose-bilkent are not correct?
            quality: "proof",
            styleEnabled: false,
            animate: false
        }).run();
        cy.ready((e)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].info("Ready", e);
            resolve(cy);
        });
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(layoutMindmap, "layoutMindmap");
function positionNodes(db, cy) {
    cy.nodes().map((node, id)=>{
        const data = node.data();
        data.x = node.position().x;
        data.y = node.position().y;
        positionNode(db, data);
        const el = db.getElementById(data.nodeId);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].info("id:", id, "Position: (", node.position().x, ", ", node.position().y, ")", data);
        el.attr("transform", "translate(".concat(node.position().x - data.width / 2, ", ").concat(node.position().y - data.height / 2, ")"));
        el.attr("attr", "apa-".concat(id, ")"));
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(positionNodes, "positionNodes");
var draw = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(async (text, id, _version, diagObj)=>{
    var _conf_mindmap, _conf_mindmap1;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("Rendering mindmap diagram\n" + text);
    const db = diagObj.db;
    const mm = db.getMindmap();
    if (!mm) {
        return;
    }
    const conf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])();
    conf.htmlLabels = false;
    const svg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$P3VETL53$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectSvgElement"])(id);
    const edgesElem = svg.append("g");
    edgesElem.attr("class", "mindmap-edges");
    const nodesElem = svg.append("g");
    nodesElem.attr("class", "mindmap-nodes");
    await drawNodes(db, nodesElem, mm, -1, conf);
    const cy = await layoutMindmap(mm, conf);
    drawEdges(edgesElem, cy);
    positionNodes(db, cy);
    var _conf_mindmap_padding, _conf_mindmap_useMaxWidth;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setupGraphViewbox"])(void 0, svg, (_conf_mindmap_padding = (_conf_mindmap = conf.mindmap) === null || _conf_mindmap === void 0 ? void 0 : _conf_mindmap.padding) !== null && _conf_mindmap_padding !== void 0 ? _conf_mindmap_padding : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].mindmap.padding, (_conf_mindmap_useMaxWidth = (_conf_mindmap1 = conf.mindmap) === null || _conf_mindmap1 === void 0 ? void 0 : _conf_mindmap1.useMaxWidth) !== null && _conf_mindmap_useMaxWidth !== void 0 ? _conf_mindmap_useMaxWidth : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultConfig_default"].mindmap.useMaxWidth);
}, "draw");
var mindmapRenderer_default = {
    draw
};
;
var genSections = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((options)=>{
    let sections = "";
    for(let i = 0; i < options.THEME_COLOR_LIMIT; i++){
        options["lineColor" + i] = options["lineColor" + i] || options["cScaleInv" + i];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$is_dark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__isDark$3e$__["isDark"])(options["lineColor" + i])) {
            options["lineColor" + i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$lighten$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__lighten$3e$__["lighten"])(options["lineColor" + i], 20);
        } else {
            options["lineColor" + i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$khroma$2f$dist$2f$methods$2f$darken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__darken$3e$__["darken"])(options["lineColor" + i], 20);
        }
    }
    for(let i = 0; i < options.THEME_COLOR_LIMIT; i++){
        const sw = "" + (17 - 3 * i);
        sections += "\n    .section-".concat(i - 1, " rect, .section-").concat(i - 1, " path, .section-").concat(i - 1, " circle, .section-").concat(i - 1, " polygon, .section-").concat(i - 1, " path  {\n      fill: ").concat(options["cScale" + i], ";\n    }\n    .section-").concat(i - 1, " text {\n     fill: ").concat(options["cScaleLabel" + i], ";\n    }\n    .node-icon-").concat(i - 1, " {\n      font-size: 40px;\n      color: ").concat(options["cScaleLabel" + i], ";\n    }\n    .section-edge-").concat(i - 1, "{\n      stroke: ").concat(options["cScale" + i], ";\n    }\n    .edge-depth-").concat(i - 1, "{\n      stroke-width: ").concat(sw, ";\n    }\n    .section-").concat(i - 1, " line {\n      stroke: ").concat(options["cScaleInv" + i], " ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    ");
    }
    return sections;
}, "genSections");
var getStyles = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((options)=>"\n  .edge {\n    stroke-width: 3;\n  }\n  ".concat(genSections(options), "\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ").concat(options.git0, ";\n  }\n  .section-root text {\n    fill: ").concat(options.gitBranchLabel0, ";\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n"), "getStyles");
var styles_default = getStyles;
// src/diagrams/mindmap/mindmap-definition.ts
var diagram = {
    get db () {
        return new MindmapDB();
    },
    renderer: mindmapRenderer_default,
    parser: mindmap_default,
    styles: styles_default
};
;
}),
}]);

//# sourceMappingURL=node_modules_6a1bbf5a._.js.map