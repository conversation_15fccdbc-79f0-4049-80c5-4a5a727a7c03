(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/info-63CPKGFF.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_info-63CPKGFF_mjs_c67e161d._.js",
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_info-63CPKGFF_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/info-63CPKGFF.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/packet-HUATNLJX.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_packet-HUATNLJX_mjs_a6d3ed30._.js",
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_packet-HUATNLJX_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/packet-HUATNLJX.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/pie-WTHONI2E.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_pie-WTHONI2E_mjs_fa1b0995._.js",
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_pie-WTHONI2E_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/pie-WTHONI2E.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/architecture-O4VJ6CD3.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_architecture-O4VJ6CD3_mjs_07057fe2._.js",
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_architecture-O4VJ6CD3_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/architecture-O4VJ6CD3.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/gitGraph-ZV4HHKMB.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_gitGraph-ZV4HHKMB_mjs_1ed7f7c4._.js",
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_gitGraph-ZV4HHKMB_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/gitGraph-ZV4HHKMB.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/radar-NJJJXTRR.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_radar-NJJJXTRR_mjs_7836de7c._.js",
  "static/chunks/8069e_@mermaid-js_parser_dist_chunks_mermaid-parser_core_radar-NJJJXTRR_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/radar-NJJJXTRR.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/treemap-75Q7IDZK.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_treemap-75Q7IDZK_mjs_c410d9a7._.js",
  "static/chunks/17da3_parser_dist_chunks_mermaid-parser_core_treemap-75Q7IDZK_mjs_0e753f6f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/treemap-75Q7IDZK.mjs [app-client] (ecmascript)");
    });
});
}),
}]);