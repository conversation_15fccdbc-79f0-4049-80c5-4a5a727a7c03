(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/dayjs/plugin/isoWeek.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
!function(e, t) {
    ("TURBOPACK compile-time truthy", 1) ? module.exports = t() : "TURBOPACK unreachable";
}(("TURBOPACK member replacement", __turbopack_context__.e), function() {
    "use strict";
    var e = "day";
    return function(t, i, s) {
        var a = function(t) {
            return t.add(4 - t.isoWeekday(), e);
        }, d = i.prototype;
        d.isoWeekYear = function() {
            return a(this).year();
        }, d.isoWeek = function(t) {
            if (!this.$utils().u(t)) return this.add(7 * (t - this.isoWeek()), e);
            var i, d, n, o, r = a(this), u = (i = this.isoWeekYear(), d = this.$u, n = (d ? s.utc : s)().year(i).startOf("year"), o = 4 - n.isoWeekday(), n.isoWeekday() > 4 && (o += 7), n.add(o, e));
            return r.diff(u, "week") + 1;
        }, d.isoWeekday = function(e) {
            return this.$utils().u(e) ? this.day() || 7 : this.day(this.day() % 7 ? e : e - 7);
        };
        var n = d.startOf;
        d.startOf = function(e, t) {
            var i = this.$utils(), s = !!i.u(t) || t;
            return "isoweek" === i.p(e) ? s ? this.date(this.date() - (this.isoWeekday() - 1)).startOf("day") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf("day") : n.bind(this)(e, t);
        };
    };
});
}}),
"[project]/node_modules/dayjs/plugin/customParseFormat.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
!function(e, t) {
    ("TURBOPACK compile-time truthy", 1) ? module.exports = t() : "TURBOPACK unreachable";
}(("TURBOPACK member replacement", __turbopack_context__.e), function() {
    "use strict";
    var e = {
        LTS: "h:mm:ss A",
        LT: "h:mm A",
        L: "MM/DD/YYYY",
        LL: "MMMM D, YYYY",
        LLL: "MMMM D, YYYY h:mm A",
        LLLL: "dddd, MMMM D, YYYY h:mm A"
    }, t = /(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g, n = /\d/, r = /\d\d/, i = /\d\d?/, o = /\d*[^-_:/,()\s\d]+/, s = {}, a = function(e) {
        return (e = +e) + (e > 68 ? 1900 : 2e3);
    };
    var f = function(e) {
        return function(t) {
            this[e] = +t;
        };
    }, h = [
        /[+-]\d\d:?(\d\d)?|Z/,
        function(e) {
            (this.zone || (this.zone = {})).offset = function(e) {
                if (!e) return 0;
                if ("Z" === e) return 0;
                var t = e.match(/([+-]|\d\d)/g), n = 60 * t[1] + (+t[2] || 0);
                return 0 === n ? 0 : "+" === t[0] ? -n : n;
            }(e);
        }
    ], u = function(e) {
        var t = s[e];
        return t && (t.indexOf ? t : t.s.concat(t.f));
    }, d = function(e, t) {
        var n, r = s.meridiem;
        if (r) {
            for(var i = 1; i <= 24; i += 1)if (e.indexOf(r(i, 0, t)) > -1) {
                n = i > 12;
                break;
            }
        } else n = e === (t ? "pm" : "PM");
        return n;
    }, c = {
        A: [
            o,
            function(e) {
                this.afternoon = d(e, !1);
            }
        ],
        a: [
            o,
            function(e) {
                this.afternoon = d(e, !0);
            }
        ],
        Q: [
            n,
            function(e) {
                this.month = 3 * (e - 1) + 1;
            }
        ],
        S: [
            n,
            function(e) {
                this.milliseconds = 100 * +e;
            }
        ],
        SS: [
            r,
            function(e) {
                this.milliseconds = 10 * +e;
            }
        ],
        SSS: [
            /\d{3}/,
            function(e) {
                this.milliseconds = +e;
            }
        ],
        s: [
            i,
            f("seconds")
        ],
        ss: [
            i,
            f("seconds")
        ],
        m: [
            i,
            f("minutes")
        ],
        mm: [
            i,
            f("minutes")
        ],
        H: [
            i,
            f("hours")
        ],
        h: [
            i,
            f("hours")
        ],
        HH: [
            i,
            f("hours")
        ],
        hh: [
            i,
            f("hours")
        ],
        D: [
            i,
            f("day")
        ],
        DD: [
            r,
            f("day")
        ],
        Do: [
            o,
            function(e) {
                var t = s.ordinal, n = e.match(/\d+/);
                if (this.day = n[0], t) for(var r = 1; r <= 31; r += 1)t(r).replace(/\[|\]/g, "") === e && (this.day = r);
            }
        ],
        w: [
            i,
            f("week")
        ],
        ww: [
            r,
            f("week")
        ],
        M: [
            i,
            f("month")
        ],
        MM: [
            r,
            f("month")
        ],
        MMM: [
            o,
            function(e) {
                var t = u("months"), n = (u("monthsShort") || t.map(function(e) {
                    return e.slice(0, 3);
                })).indexOf(e) + 1;
                if (n < 1) throw new Error;
                this.month = n % 12 || n;
            }
        ],
        MMMM: [
            o,
            function(e) {
                var t = u("months").indexOf(e) + 1;
                if (t < 1) throw new Error;
                this.month = t % 12 || t;
            }
        ],
        Y: [
            /[+-]?\d+/,
            f("year")
        ],
        YY: [
            r,
            function(e) {
                this.year = a(e);
            }
        ],
        YYYY: [
            /\d{4}/,
            f("year")
        ],
        Z: h,
        ZZ: h
    };
    function l(n) {
        var r, i;
        r = n, i = s && s.formats;
        for(var o = (n = r.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g, function(t, n, r) {
            var o = r && r.toUpperCase();
            return n || i[r] || e[r] || i[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(e, t, n) {
                return t || n.slice(1);
            });
        })).match(t), a = o.length, f = 0; f < a; f += 1){
            var h = o[f], u = c[h], d = u && u[0], l = u && u[1];
            o[f] = l ? {
                regex: d,
                parser: l
            } : h.replace(/^\[|\]$/g, "");
        }
        return function(e) {
            for(var t = {}, n = 0, r = 0; n < a; n += 1){
                var i = o[n];
                if ("string" == typeof i) r += i.length;
                else {
                    var s = i.regex, f = i.parser, h = e.slice(r), u = s.exec(h)[0];
                    f.call(t, u), e = e.replace(u, "");
                }
            }
            return function(e) {
                var t = e.afternoon;
                if (void 0 !== t) {
                    var n = e.hours;
                    t ? n < 12 && (e.hours += 12) : 12 === n && (e.hours = 0), delete e.afternoon;
                }
            }(t), t;
        };
    }
    return function(e, t, n) {
        n.p.customParseFormat = !0, e && e.parseTwoDigitYear && (a = e.parseTwoDigitYear);
        var r = t.prototype, i = r.parse;
        r.parse = function(e) {
            var t = e.date, r = e.utc, o = e.args;
            this.$u = r;
            var a = o[1];
            if ("string" == typeof a) {
                var f = !0 === o[2], h = !0 === o[3], u = f || h, d = o[2];
                h && (d = o[2]), s = this.$locale(), !f && d && (s = n.Ls[d]), this.$d = function(e, t, n, r) {
                    try {
                        if ([
                            "x",
                            "X"
                        ].indexOf(t) > -1) return new Date(("X" === t ? 1e3 : 1) * e);
                        var i = l(t)(e), o = i.year, s = i.month, a = i.day, f = i.hours, h = i.minutes, u = i.seconds, d = i.milliseconds, c = i.zone, m = i.week, M = new Date, Y = a || (o || s ? 1 : M.getDate()), p = o || M.getFullYear(), v = 0;
                        o && !s || (v = s > 0 ? s - 1 : M.getMonth());
                        var D, w = f || 0, g = h || 0, y = u || 0, L = d || 0;
                        return c ? new Date(Date.UTC(p, v, Y, w, g, y, L + 60 * c.offset * 1e3)) : n ? new Date(Date.UTC(p, v, Y, w, g, y, L)) : (D = new Date(p, v, Y, w, g, y, L), m && (D = r(D).week(m).toDate()), D);
                    } catch (e) {
                        return new Date("");
                    }
                }(t, a, r, n), this.init(), d && !0 !== d && (this.$L = this.locale(d).$L), u && t != this.format(a) && (this.$d = new Date("")), s = {};
            } else if (a instanceof Array) for(var c = a.length, m = 1; m <= c; m += 1){
                o[1] = a[m - 1];
                var M = n.apply(this, o);
                if (M.isValid()) {
                    this.$d = M.$d, this.$L = M.$L, this.init();
                    break;
                }
                m === c && (this.$d = new Date(""));
            }
            else i.call(this, e);
        };
    };
});
}}),
"[project]/node_modules/dayjs/plugin/advancedFormat.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
!function(e, t) {
    ("TURBOPACK compile-time truthy", 1) ? module.exports = t() : "TURBOPACK unreachable";
}(("TURBOPACK member replacement", __turbopack_context__.e), function() {
    "use strict";
    return function(e, t) {
        var r = t.prototype, n = r.format;
        r.format = function(e) {
            var t = this, r = this.$locale();
            if (!this.isValid()) return n.bind(this)(e);
            var s = this.$utils(), a = (e || "YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function(e) {
                switch(e){
                    case "Q":
                        return Math.ceil((t.$M + 1) / 3);
                    case "Do":
                        return r.ordinal(t.$D);
                    case "gggg":
                        return t.weekYear();
                    case "GGGG":
                        return t.isoWeekYear();
                    case "wo":
                        return r.ordinal(t.week(), "W");
                    case "w":
                    case "ww":
                        return s.s(t.week(), "w" === e ? 1 : 2, "0");
                    case "W":
                    case "WW":
                        return s.s(t.isoWeek(), "W" === e ? 1 : 2, "0");
                    case "k":
                    case "kk":
                        return s.s(String(0 === t.$H ? 24 : t.$H), "k" === e ? 1 : 2, "0");
                    case "X":
                        return Math.floor(t.$d.getTime() / 1e3);
                    case "x":
                        return t.$d.getTime();
                    case "z":
                        return "[" + t.offsetName() + "]";
                    case "zzz":
                        return "[" + t.offsetName("long") + "]";
                    default:
                        return e;
                }
            });
            return n.bind(this)(a);
        };
    };
});
}}),
"[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeInterval": ()=>timeInterval
});
const t0 = new Date, t1 = new Date;
function timeInterval(floori, offseti, count, field) {
    function interval(date) {
        return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;
    }
    interval.floor = (date)=>{
        return floori(date = new Date(+date)), date;
    };
    interval.ceil = (date)=>{
        return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;
    };
    interval.round = (date)=>{
        const d0 = interval(date), d1 = interval.ceil(date);
        return date - d0 < d1 - date ? d0 : d1;
    };
    interval.offset = (date, step)=>{
        return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;
    };
    interval.range = (start, stop, step)=>{
        const range = [];
        start = interval.ceil(start);
        step = step == null ? 1 : Math.floor(step);
        if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date
        let previous;
        do range.push(previous = new Date(+start)), offseti(start, step), floori(start);
        while (previous < start && start < stop)
        return range;
    };
    interval.filter = (test)=>{
        return timeInterval((date)=>{
            if (date >= date) while(floori(date), !test(date))date.setTime(date - 1);
        }, (date, step)=>{
            if (date >= date) {
                if (step < 0) while(++step <= 0){
                    while(offseti(date, -1), !test(date)){} // eslint-disable-line no-empty
                }
                else while(--step >= 0){
                    while(offseti(date, +1), !test(date)){} // eslint-disable-line no-empty
                }
            }
        });
    };
    if (count) {
        interval.count = (start, end)=>{
            t0.setTime(+start), t1.setTime(+end);
            floori(t0), floori(t1);
            return Math.floor(count(t0, t1));
        };
        interval.every = (step)=>{
            step = Math.floor(step);
            return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? (d)=>field(d) % step === 0 : (d)=>interval.count(0, d) % step === 0);
        };
    }
    return interval;
}
}),
"[project]/node_modules/d3-time/src/year.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeYear": ()=>timeYear,
    "timeYears": ()=>timeYears,
    "utcYear": ()=>utcYear,
    "utcYears": ()=>utcYears
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
;
const timeYear = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setMonth(0, 1);
    date.setHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setFullYear(date.getFullYear() + step);
}, (start, end)=>{
    return end.getFullYear() - start.getFullYear();
}, (date)=>{
    return date.getFullYear();
});
// An optimized implementation for this simple case.
timeYear.every = (k)=>{
    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
        date.setFullYear(Math.floor(date.getFullYear() / k) * k);
        date.setMonth(0, 1);
        date.setHours(0, 0, 0, 0);
    }, (date, step)=>{
        date.setFullYear(date.getFullYear() + step * k);
    });
};
const timeYears = timeYear.range;
const utcYear = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCMonth(0, 1);
    date.setUTCHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setUTCFullYear(date.getUTCFullYear() + step);
}, (start, end)=>{
    return end.getUTCFullYear() - start.getUTCFullYear();
}, (date)=>{
    return date.getUTCFullYear();
});
// An optimized implementation for this simple case.
utcYear.every = (k)=>{
    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
        date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);
        date.setUTCMonth(0, 1);
        date.setUTCHours(0, 0, 0, 0);
    }, (date, step)=>{
        date.setUTCFullYear(date.getUTCFullYear() + step * k);
    });
};
const utcYears = utcYear.range;
}),
"[project]/node_modules/d3-time/src/month.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeMonth": ()=>timeMonth,
    "timeMonths": ()=>timeMonths,
    "utcMonth": ()=>utcMonth,
    "utcMonths": ()=>utcMonths
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
;
const timeMonth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setDate(1);
    date.setHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setMonth(date.getMonth() + step);
}, (start, end)=>{
    return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;
}, (date)=>{
    return date.getMonth();
});
const timeMonths = timeMonth.range;
const utcMonth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCDate(1);
    date.setUTCHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setUTCMonth(date.getUTCMonth() + step);
}, (start, end)=>{
    return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;
}, (date)=>{
    return date.getUTCMonth();
});
const utcMonths = utcMonth.range;
}),
"[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "durationDay": ()=>durationDay,
    "durationHour": ()=>durationHour,
    "durationMinute": ()=>durationMinute,
    "durationMonth": ()=>durationMonth,
    "durationSecond": ()=>durationSecond,
    "durationWeek": ()=>durationWeek,
    "durationYear": ()=>durationYear
});
const durationSecond = 1000;
const durationMinute = durationSecond * 60;
const durationHour = durationMinute * 60;
const durationDay = durationHour * 24;
const durationWeek = durationDay * 7;
const durationMonth = durationDay * 30;
const durationYear = durationDay * 365;
}),
"[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeFriday": ()=>timeFriday,
    "timeFridays": ()=>timeFridays,
    "timeMonday": ()=>timeMonday,
    "timeMondays": ()=>timeMondays,
    "timeSaturday": ()=>timeSaturday,
    "timeSaturdays": ()=>timeSaturdays,
    "timeSunday": ()=>timeSunday,
    "timeSundays": ()=>timeSundays,
    "timeThursday": ()=>timeThursday,
    "timeThursdays": ()=>timeThursdays,
    "timeTuesday": ()=>timeTuesday,
    "timeTuesdays": ()=>timeTuesdays,
    "timeWednesday": ()=>timeWednesday,
    "timeWednesdays": ()=>timeWednesdays,
    "utcFriday": ()=>utcFriday,
    "utcFridays": ()=>utcFridays,
    "utcMonday": ()=>utcMonday,
    "utcMondays": ()=>utcMondays,
    "utcSaturday": ()=>utcSaturday,
    "utcSaturdays": ()=>utcSaturdays,
    "utcSunday": ()=>utcSunday,
    "utcSundays": ()=>utcSundays,
    "utcThursday": ()=>utcThursday,
    "utcThursdays": ()=>utcThursdays,
    "utcTuesday": ()=>utcTuesday,
    "utcTuesdays": ()=>utcTuesdays,
    "utcWednesday": ()=>utcWednesday,
    "utcWednesdays": ()=>utcWednesdays
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
;
;
function timeWeekday(i) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
        date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);
        date.setHours(0, 0, 0, 0);
    }, (date, step)=>{
        date.setDate(date.getDate() + step * 7);
    }, (start, end)=>{
        return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationWeek"];
    });
}
const timeSunday = timeWeekday(0);
const timeMonday = timeWeekday(1);
const timeTuesday = timeWeekday(2);
const timeWednesday = timeWeekday(3);
const timeThursday = timeWeekday(4);
const timeFriday = timeWeekday(5);
const timeSaturday = timeWeekday(6);
const timeSundays = timeSunday.range;
const timeMondays = timeMonday.range;
const timeTuesdays = timeTuesday.range;
const timeWednesdays = timeWednesday.range;
const timeThursdays = timeThursday.range;
const timeFridays = timeFriday.range;
const timeSaturdays = timeSaturday.range;
function utcWeekday(i) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
        date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);
        date.setUTCHours(0, 0, 0, 0);
    }, (date, step)=>{
        date.setUTCDate(date.getUTCDate() + step * 7);
    }, (start, end)=>{
        return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationWeek"];
    });
}
const utcSunday = utcWeekday(0);
const utcMonday = utcWeekday(1);
const utcTuesday = utcWeekday(2);
const utcWednesday = utcWeekday(3);
const utcThursday = utcWeekday(4);
const utcFriday = utcWeekday(5);
const utcSaturday = utcWeekday(6);
const utcSundays = utcSunday.range;
const utcMondays = utcMonday.range;
const utcTuesdays = utcTuesday.range;
const utcWednesdays = utcWednesday.range;
const utcThursdays = utcThursday.range;
const utcFridays = utcFriday.range;
const utcSaturdays = utcSaturday.range;
}),
"[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript) <export timeSunday as timeWeek>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeWeek": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeSunday"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-time/src/day.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeDay": ()=>timeDay,
    "timeDays": ()=>timeDays,
    "unixDay": ()=>unixDay,
    "unixDays": ()=>unixDays,
    "utcDay": ()=>utcDay,
    "utcDays": ()=>utcDays
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
;
;
const timeDay = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>date.setHours(0, 0, 0, 0), (date, step)=>date.setDate(date.getDate() + step), (start, end)=>(end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"], (date)=>date.getDate() - 1);
const timeDays = timeDay.range;
const utcDay = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setUTCDate(date.getUTCDate() + step);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"];
}, (date)=>{
    return date.getUTCDate() - 1;
});
const utcDays = utcDay.range;
const unixDay = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCHours(0, 0, 0, 0);
}, (date, step)=>{
    date.setUTCDate(date.getUTCDate() + step);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"];
}, (date)=>{
    return Math.floor(date / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"]);
});
const unixDays = unixDay.range;
}),
"[project]/node_modules/d3-time/src/hour.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeHour": ()=>timeHour,
    "timeHours": ()=>timeHours,
    "utcHour": ()=>utcHour,
    "utcHours": ()=>utcHours
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
;
;
const timeHour = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setTime(date - date.getMilliseconds() - date.getSeconds() * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"] - date.getMinutes() * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]);
}, (date, step)=>{
    date.setTime(+date + step * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"];
}, (date)=>{
    return date.getHours();
});
const timeHours = timeHour.range;
const utcHour = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCMinutes(0, 0, 0);
}, (date, step)=>{
    date.setTime(+date + step * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"];
}, (date)=>{
    return date.getUTCHours();
});
const utcHours = utcHour.range;
}),
"[project]/node_modules/d3-time/src/minute.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeMinute": ()=>timeMinute,
    "timeMinutes": ()=>timeMinutes,
    "utcMinute": ()=>utcMinute,
    "utcMinutes": ()=>utcMinutes
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
;
;
const timeMinute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setTime(date - date.getMilliseconds() - date.getSeconds() * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]);
}, (date, step)=>{
    date.setTime(+date + step * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"];
}, (date)=>{
    return date.getMinutes();
});
const timeMinutes = timeMinute.range;
const utcMinute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setUTCSeconds(0, 0);
}, (date, step)=>{
    date.setTime(+date + step * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"];
}, (date)=>{
    return date.getUTCMinutes();
});
const utcMinutes = utcMinute.range;
}),
"[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "second": ()=>second,
    "seconds": ()=>seconds
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
;
;
const second = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
    date.setTime(date - date.getMilliseconds());
}, (date, step)=>{
    date.setTime(+date + step * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]);
}, (start, end)=>{
    return (end - start) / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"];
}, (date)=>{
    return date.getUTCSeconds();
});
const seconds = second.range;
}),
"[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript) <export second as timeSecond>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeSecond": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["second"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ascending
});
function ascending(a, b) {
    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
}
}),
"[project]/node_modules/d3-array/src/descending.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>descending
});
function descending(a, b) {
    return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;
}
}),
"[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>bisector
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/descending.js [app-client] (ecmascript)");
;
;
function bisector(f) {
    let compare1, compare2, delta;
    // If an accessor is specified, promote it to a comparator. In this case we
    // can test whether the search value is (self-) comparable. We can’t do this
    // for a comparator (except for specific, known comparators) because we can’t
    // tell if the comparator is symmetric, and an asymmetric comparator can’t be
    // used to test whether a single value is comparable.
    if (f.length !== 2) {
        compare1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        compare2 = (d, x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(f(d), x);
        delta = (d, x)=>f(d) - x;
    } else {
        compare1 = f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$descending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] ? f : zero;
        compare2 = f;
        delta = f;
    }
    function left(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) < 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function right(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        if (lo < hi) {
            if (compare1(x, x) !== 0) return hi;
            do {
                const mid = lo + hi >>> 1;
                if (compare2(a[mid], x) <= 0) lo = mid + 1;
                else hi = mid;
            }while (lo < hi)
        }
        return lo;
    }
    function center(a, x) {
        let lo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0, hi = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : a.length;
        const i = left(a, x, lo, hi - 1);
        return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;
    }
    return {
        left,
        center,
        right
    };
}
function zero() {
    return 0;
}
}),
"[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript) <export default as bisector>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bisector": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ticks,
    "tickIncrement": ()=>tickIncrement,
    "tickStep": ()=>tickStep
});
const e10 = Math.sqrt(50), e5 = Math.sqrt(10), e2 = Math.sqrt(2);
function tickSpec(start, stop, count) {
    const step = (stop - start) / Math.max(0, count), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
    let i1, i2, inc;
    if (power < 0) {
        inc = Math.pow(10, -power) / factor;
        i1 = Math.round(start * inc);
        i2 = Math.round(stop * inc);
        if (i1 / inc < start) ++i1;
        if (i2 / inc > stop) --i2;
        inc = -inc;
    } else {
        inc = Math.pow(10, power) * factor;
        i1 = Math.round(start / inc);
        i2 = Math.round(stop / inc);
        if (i1 * inc < start) ++i1;
        if (i2 * inc > stop) --i2;
    }
    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);
    return [
        i1,
        i2,
        inc
    ];
}
function ticks(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    if (!(count > 0)) return [];
    if (start === stop) return [
        start
    ];
    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);
    if (!(i2 >= i1)) return [];
    const n = i2 - i1 + 1, ticks = new Array(n);
    if (reverse) {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i2 - i) * inc;
    } else {
        if (inc < 0) for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) / -inc;
        else for(let i = 0; i < n; ++i)ticks[i] = (i1 + i) * inc;
    }
    return ticks;
}
function tickIncrement(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    return tickSpec(start, stop, count)[2];
}
function tickStep(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);
    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
}
}),
"[project]/node_modules/d3-time/src/millisecond.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "millisecond": ()=>millisecond,
    "milliseconds": ()=>milliseconds
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/interval.js [app-client] (ecmascript)");
;
const millisecond = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])(()=>{
// noop
}, (date, step)=>{
    date.setTime(+date + step);
}, (start, end)=>{
    return end - start;
});
// An optimized implementation for this simple case.
millisecond.every = (k)=>{
    k = Math.floor(k);
    if (!isFinite(k) || !(k > 0)) return null;
    if (!(k > 1)) return millisecond;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$interval$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeInterval"])((date)=>{
        date.setTime(Math.floor(date / k) * k);
    }, (date, step)=>{
        date.setTime(+date + step * k);
    }, (start, end)=>{
        return (end - start) / k;
    });
};
const milliseconds = millisecond.range;
}),
"[project]/node_modules/d3-time/src/ticks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeTickInterval": ()=>timeTickInterval,
    "timeTicks": ()=>timeTicks,
    "utcTickInterval": ()=>utcTickInterval,
    "utcTicks": ()=>utcTicks
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisector$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript) <export default as bisector>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/duration.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/millisecond.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/minute.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/hour.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/day.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/month.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/year.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
function ticker(year, month, week, day, hour, minute) {
    const tickIntervals = [
        [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["second"],
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]
        ],
        [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["second"],
            5,
            5 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]
        ],
        [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["second"],
            15,
            15 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]
        ],
        [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["second"],
            30,
            30 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationSecond"]
        ],
        [
            minute,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]
        ],
        [
            minute,
            5,
            5 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]
        ],
        [
            minute,
            15,
            15 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]
        ],
        [
            minute,
            30,
            30 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMinute"]
        ],
        [
            hour,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]
        ],
        [
            hour,
            3,
            3 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]
        ],
        [
            hour,
            6,
            6 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]
        ],
        [
            hour,
            12,
            12 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationHour"]
        ],
        [
            day,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"]
        ],
        [
            day,
            2,
            2 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationDay"]
        ],
        [
            week,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationWeek"]
        ],
        [
            month,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMonth"]
        ],
        [
            month,
            3,
            3 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationMonth"]
        ],
        [
            year,
            1,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationYear"]
        ]
    ];
    function ticks(start, stop, count) {
        const reverse = stop < start;
        if (reverse) [start, stop] = [
            stop,
            start
        ];
        const interval = count && typeof count.range === "function" ? count : tickInterval(start, stop, count);
        const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop
        return reverse ? ticks.reverse() : ticks;
    }
    function tickInterval(start, stop, count) {
        const target = Math.abs(stop - start) / count;
        const i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisector$3e$__["bisector"])((param)=>{
            let [, , step] = param;
            return step;
        }).right(tickIntervals, target);
        if (i === tickIntervals.length) return year.every((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickStep"])(start / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationYear"], stop / __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$duration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["durationYear"], count));
        if (i === 0) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["millisecond"].every(Math.max((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickStep"])(start, stop, count), 1));
        const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];
        return t.every(step);
    }
    return [
        ticks,
        tickInterval
    ];
}
const [utcTicks, utcTickInterval] = ticker(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcMonth"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcSunday"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unixDay"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcHour"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcMinute"]);
const [timeTicks, timeTickInterval] = ticker(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonth"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeSunday"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeHour"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMinute"]);
;
}),
"[project]/node_modules/d3-time-format/src/locale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>formatLocale
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/day.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/year.js [app-client] (ecmascript)");
;
function localDate(d) {
    if (0 <= d.y && d.y < 100) {
        var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);
        date.setFullYear(d.y);
        return date;
    }
    return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);
}
function utcDate(d) {
    if (0 <= d.y && d.y < 100) {
        var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));
        date.setUTCFullYear(d.y);
        return date;
    }
    return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));
}
function newDate(y, m, d) {
    return {
        y: y,
        m: m,
        d: d,
        H: 0,
        M: 0,
        S: 0,
        L: 0
    };
}
function formatLocale(locale) {
    var locale_dateTime = locale.dateTime, locale_date = locale.date, locale_time = locale.time, locale_periods = locale.periods, locale_weekdays = locale.days, locale_shortWeekdays = locale.shortDays, locale_months = locale.months, locale_shortMonths = locale.shortMonths;
    var periodRe = formatRe(locale_periods), periodLookup = formatLookup(locale_periods), weekdayRe = formatRe(locale_weekdays), weekdayLookup = formatLookup(locale_weekdays), shortWeekdayRe = formatRe(locale_shortWeekdays), shortWeekdayLookup = formatLookup(locale_shortWeekdays), monthRe = formatRe(locale_months), monthLookup = formatLookup(locale_months), shortMonthRe = formatRe(locale_shortMonths), shortMonthLookup = formatLookup(locale_shortMonths);
    var formats = {
        "a": formatShortWeekday,
        "A": formatWeekday,
        "b": formatShortMonth,
        "B": formatMonth,
        "c": null,
        "d": formatDayOfMonth,
        "e": formatDayOfMonth,
        "f": formatMicroseconds,
        "g": formatYearISO,
        "G": formatFullYearISO,
        "H": formatHour24,
        "I": formatHour12,
        "j": formatDayOfYear,
        "L": formatMilliseconds,
        "m": formatMonthNumber,
        "M": formatMinutes,
        "p": formatPeriod,
        "q": formatQuarter,
        "Q": formatUnixTimestamp,
        "s": formatUnixTimestampSeconds,
        "S": formatSeconds,
        "u": formatWeekdayNumberMonday,
        "U": formatWeekNumberSunday,
        "V": formatWeekNumberISO,
        "w": formatWeekdayNumberSunday,
        "W": formatWeekNumberMonday,
        "x": null,
        "X": null,
        "y": formatYear,
        "Y": formatFullYear,
        "Z": formatZone,
        "%": formatLiteralPercent
    };
    var utcFormats = {
        "a": formatUTCShortWeekday,
        "A": formatUTCWeekday,
        "b": formatUTCShortMonth,
        "B": formatUTCMonth,
        "c": null,
        "d": formatUTCDayOfMonth,
        "e": formatUTCDayOfMonth,
        "f": formatUTCMicroseconds,
        "g": formatUTCYearISO,
        "G": formatUTCFullYearISO,
        "H": formatUTCHour24,
        "I": formatUTCHour12,
        "j": formatUTCDayOfYear,
        "L": formatUTCMilliseconds,
        "m": formatUTCMonthNumber,
        "M": formatUTCMinutes,
        "p": formatUTCPeriod,
        "q": formatUTCQuarter,
        "Q": formatUnixTimestamp,
        "s": formatUnixTimestampSeconds,
        "S": formatUTCSeconds,
        "u": formatUTCWeekdayNumberMonday,
        "U": formatUTCWeekNumberSunday,
        "V": formatUTCWeekNumberISO,
        "w": formatUTCWeekdayNumberSunday,
        "W": formatUTCWeekNumberMonday,
        "x": null,
        "X": null,
        "y": formatUTCYear,
        "Y": formatUTCFullYear,
        "Z": formatUTCZone,
        "%": formatLiteralPercent
    };
    var parses = {
        "a": parseShortWeekday,
        "A": parseWeekday,
        "b": parseShortMonth,
        "B": parseMonth,
        "c": parseLocaleDateTime,
        "d": parseDayOfMonth,
        "e": parseDayOfMonth,
        "f": parseMicroseconds,
        "g": parseYear,
        "G": parseFullYear,
        "H": parseHour24,
        "I": parseHour24,
        "j": parseDayOfYear,
        "L": parseMilliseconds,
        "m": parseMonthNumber,
        "M": parseMinutes,
        "p": parsePeriod,
        "q": parseQuarter,
        "Q": parseUnixTimestamp,
        "s": parseUnixTimestampSeconds,
        "S": parseSeconds,
        "u": parseWeekdayNumberMonday,
        "U": parseWeekNumberSunday,
        "V": parseWeekNumberISO,
        "w": parseWeekdayNumberSunday,
        "W": parseWeekNumberMonday,
        "x": parseLocaleDate,
        "X": parseLocaleTime,
        "y": parseYear,
        "Y": parseFullYear,
        "Z": parseZone,
        "%": parseLiteralPercent
    };
    // These recursive directive definitions must be deferred.
    formats.x = newFormat(locale_date, formats);
    formats.X = newFormat(locale_time, formats);
    formats.c = newFormat(locale_dateTime, formats);
    utcFormats.x = newFormat(locale_date, utcFormats);
    utcFormats.X = newFormat(locale_time, utcFormats);
    utcFormats.c = newFormat(locale_dateTime, utcFormats);
    function newFormat(specifier, formats) {
        return function(date) {
            var string = [], i = -1, j = 0, n = specifier.length, c, pad, format;
            if (!(date instanceof Date)) date = new Date(+date);
            while(++i < n){
                if (specifier.charCodeAt(i) === 37) {
                    string.push(specifier.slice(j, i));
                    if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);
                    else pad = c === "e" ? " " : "0";
                    if (format = formats[c]) c = format(date, pad);
                    string.push(c);
                    j = i + 1;
                }
            }
            string.push(specifier.slice(j, i));
            return string.join("");
        };
    }
    function newParse(specifier, Z) {
        return function(string) {
            var d = newDate(1900, undefined, 1), i = parseSpecifier(d, specifier, string += "", 0), week, day;
            if (i != string.length) return null;
            // If a UNIX timestamp is specified, return it.
            if ("Q" in d) return new Date(d.Q);
            if ("s" in d) return new Date(d.s * 1000 + ("L" in d ? d.L : 0));
            // If this is utcParse, never use the local timezone.
            if (Z && !("Z" in d)) d.Z = 0;
            // The am-pm flag is 0 for AM, and 1 for PM.
            if ("p" in d) d.H = d.H % 12 + d.p * 12;
            // If the month was not specified, inherit from the quarter.
            if (d.m === undefined) d.m = "q" in d ? d.q : 0;
            // Convert day-of-week and week-of-year to day-of-year.
            if ("V" in d) {
                if (d.V < 1 || d.V > 53) return null;
                if (!("w" in d)) d.w = 1;
                if ("Z" in d) {
                    week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();
                    week = day > 4 || day === 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcMonday"].ceil(week) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcMonday"])(week);
                    week = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcDay"].offset(week, (d.V - 1) * 7);
                    d.y = week.getUTCFullYear();
                    d.m = week.getUTCMonth();
                    d.d = week.getUTCDate() + (d.w + 6) % 7;
                } else {
                    week = localDate(newDate(d.y, 0, 1)), day = week.getDay();
                    week = day > 4 || day === 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonday"].ceil(week) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonday"])(week);
                    week = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"].offset(week, (d.V - 1) * 7);
                    d.y = week.getFullYear();
                    d.m = week.getMonth();
                    d.d = week.getDate() + (d.w + 6) % 7;
                }
            } else if ("W" in d || "U" in d) {
                if (!("w" in d)) d.w = "u" in d ? d.u % 7 : "W" in d ? 1 : 0;
                day = "Z" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();
                d.m = 0;
                d.d = "W" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;
            }
            // If a time zone is specified, all fields are interpreted as UTC and then
            // offset according to the specified time zone.
            if ("Z" in d) {
                d.H += d.Z / 100 | 0;
                d.M += d.Z % 100;
                return utcDate(d);
            }
            // Otherwise, all fields are in local time.
            return localDate(d);
        };
    }
    function parseSpecifier(d, specifier, string, j) {
        var i = 0, n = specifier.length, m = string.length, c, parse;
        while(i < n){
            if (j >= m) return -1;
            c = specifier.charCodeAt(i++);
            if (c === 37) {
                c = specifier.charAt(i++);
                parse = parses[c in pads ? specifier.charAt(i++) : c];
                if (!parse || (j = parse(d, string, j)) < 0) return -1;
            } else if (c != string.charCodeAt(j++)) {
                return -1;
            }
        }
        return j;
    }
    function parsePeriod(d, string, i) {
        var n = periodRe.exec(string.slice(i));
        return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;
    }
    function parseShortWeekday(d, string, i) {
        var n = shortWeekdayRe.exec(string.slice(i));
        return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;
    }
    function parseWeekday(d, string, i) {
        var n = weekdayRe.exec(string.slice(i));
        return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;
    }
    function parseShortMonth(d, string, i) {
        var n = shortMonthRe.exec(string.slice(i));
        return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;
    }
    function parseMonth(d, string, i) {
        var n = monthRe.exec(string.slice(i));
        return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;
    }
    function parseLocaleDateTime(d, string, i) {
        return parseSpecifier(d, locale_dateTime, string, i);
    }
    function parseLocaleDate(d, string, i) {
        return parseSpecifier(d, locale_date, string, i);
    }
    function parseLocaleTime(d, string, i) {
        return parseSpecifier(d, locale_time, string, i);
    }
    function formatShortWeekday(d) {
        return locale_shortWeekdays[d.getDay()];
    }
    function formatWeekday(d) {
        return locale_weekdays[d.getDay()];
    }
    function formatShortMonth(d) {
        return locale_shortMonths[d.getMonth()];
    }
    function formatMonth(d) {
        return locale_months[d.getMonth()];
    }
    function formatPeriod(d) {
        return locale_periods[+(d.getHours() >= 12)];
    }
    function formatQuarter(d) {
        return 1 + ~~(d.getMonth() / 3);
    }
    function formatUTCShortWeekday(d) {
        return locale_shortWeekdays[d.getUTCDay()];
    }
    function formatUTCWeekday(d) {
        return locale_weekdays[d.getUTCDay()];
    }
    function formatUTCShortMonth(d) {
        return locale_shortMonths[d.getUTCMonth()];
    }
    function formatUTCMonth(d) {
        return locale_months[d.getUTCMonth()];
    }
    function formatUTCPeriod(d) {
        return locale_periods[+(d.getUTCHours() >= 12)];
    }
    function formatUTCQuarter(d) {
        return 1 + ~~(d.getUTCMonth() / 3);
    }
    return {
        format: function(specifier) {
            var f = newFormat(specifier += "", formats);
            f.toString = function() {
                return specifier;
            };
            return f;
        },
        parse: function(specifier) {
            var p = newParse(specifier += "", false);
            p.toString = function() {
                return specifier;
            };
            return p;
        },
        utcFormat: function(specifier) {
            var f = newFormat(specifier += "", utcFormats);
            f.toString = function() {
                return specifier;
            };
            return f;
        },
        utcParse: function(specifier) {
            var p = newParse(specifier += "", true);
            p.toString = function() {
                return specifier;
            };
            return p;
        }
    };
}
var pads = {
    "-": "",
    "_": " ",
    "0": "0"
}, numberRe = /^\s*\d+/, percentRe = /^%/, requoteRe = /[\\^$*+?|[\]().{}]/g;
function pad(value, fill, width) {
    var sign = value < 0 ? "-" : "", string = (sign ? -value : value) + "", length = string.length;
    return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);
}
function requote(s) {
    return s.replace(requoteRe, "\\$&");
}
function formatRe(names) {
    return new RegExp("^(?:" + names.map(requote).join("|") + ")", "i");
}
function formatLookup(names) {
    return new Map(names.map((name, i)=>[
            name.toLowerCase(),
            i
        ]));
}
function parseWeekdayNumberSunday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 1));
    return n ? (d.w = +n[0], i + n[0].length) : -1;
}
function parseWeekdayNumberMonday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 1));
    return n ? (d.u = +n[0], i + n[0].length) : -1;
}
function parseWeekNumberSunday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.U = +n[0], i + n[0].length) : -1;
}
function parseWeekNumberISO(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.V = +n[0], i + n[0].length) : -1;
}
function parseWeekNumberMonday(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.W = +n[0], i + n[0].length) : -1;
}
function parseFullYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 4));
    return n ? (d.y = +n[0], i + n[0].length) : -1;
}
function parseYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;
}
function parseZone(d, string, i) {
    var n = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(string.slice(i, i + 6));
    return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || "00")), i + n[0].length) : -1;
}
function parseQuarter(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 1));
    return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;
}
function parseMonthNumber(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.m = n[0] - 1, i + n[0].length) : -1;
}
function parseDayOfMonth(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.d = +n[0], i + n[0].length) : -1;
}
function parseDayOfYear(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 3));
    return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;
}
function parseHour24(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.H = +n[0], i + n[0].length) : -1;
}
function parseMinutes(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.M = +n[0], i + n[0].length) : -1;
}
function parseSeconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 2));
    return n ? (d.S = +n[0], i + n[0].length) : -1;
}
function parseMilliseconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 3));
    return n ? (d.L = +n[0], i + n[0].length) : -1;
}
function parseMicroseconds(d, string, i) {
    var n = numberRe.exec(string.slice(i, i + 6));
    return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;
}
function parseLiteralPercent(d, string, i) {
    var n = percentRe.exec(string.slice(i, i + 1));
    return n ? i + n[0].length : -1;
}
function parseUnixTimestamp(d, string, i) {
    var n = numberRe.exec(string.slice(i));
    return n ? (d.Q = +n[0], i + n[0].length) : -1;
}
function parseUnixTimestampSeconds(d, string, i) {
    var n = numberRe.exec(string.slice(i));
    return n ? (d.s = +n[0], i + n[0].length) : -1;
}
function formatDayOfMonth(d, p) {
    return pad(d.getDate(), p, 2);
}
function formatHour24(d, p) {
    return pad(d.getHours(), p, 2);
}
function formatHour12(d, p) {
    return pad(d.getHours() % 12 || 12, p, 2);
}
function formatDayOfYear(d, p) {
    return pad(1 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"])(d), d), p, 3);
}
function formatMilliseconds(d, p) {
    return pad(d.getMilliseconds(), p, 3);
}
function formatMicroseconds(d, p) {
    return formatMilliseconds(d, p) + "000";
}
function formatMonthNumber(d, p) {
    return pad(d.getMonth() + 1, p, 2);
}
function formatMinutes(d, p) {
    return pad(d.getMinutes(), p, 2);
}
function formatSeconds(d, p) {
    return pad(d.getSeconds(), p, 2);
}
function formatWeekdayNumberMonday(d) {
    var day = d.getDay();
    return day === 0 ? 7 : day;
}
function formatWeekNumberSunday(d, p) {
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeSunday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"])(d) - 1, d), p, 2);
}
function dISO(d) {
    var day = d.getDay();
    return day >= 4 || day === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"])(d) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"].ceil(d);
}
function formatWeekNumberISO(d, p) {
    d = dISO(d);
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"])(d), d) + ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"])(d).getDay() === 4), p, 2);
}
function formatWeekdayNumberSunday(d) {
    return d.getDay();
}
function formatWeekNumberMonday(d, p) {
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"])(d) - 1, d), p, 2);
}
function formatYear(d, p) {
    return pad(d.getFullYear() % 100, p, 2);
}
function formatYearISO(d, p) {
    d = dISO(d);
    return pad(d.getFullYear() % 100, p, 2);
}
function formatFullYear(d, p) {
    return pad(d.getFullYear() % 10000, p, 4);
}
function formatFullYearISO(d, p) {
    var day = d.getDay();
    d = day >= 4 || day === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"])(d) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"].ceil(d);
    return pad(d.getFullYear() % 10000, p, 4);
}
function formatZone(d) {
    var z = d.getTimezoneOffset();
    return (z > 0 ? "-" : (z *= -1, "+")) + pad(z / 60 | 0, "0", 2) + pad(z % 60, "0", 2);
}
function formatUTCDayOfMonth(d, p) {
    return pad(d.getUTCDate(), p, 2);
}
function formatUTCHour24(d, p) {
    return pad(d.getUTCHours(), p, 2);
}
function formatUTCHour12(d, p) {
    return pad(d.getUTCHours() % 12 || 12, p, 2);
}
function formatUTCDayOfYear(d, p) {
    return pad(1 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcDay"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"])(d), d), p, 3);
}
function formatUTCMilliseconds(d, p) {
    return pad(d.getUTCMilliseconds(), p, 3);
}
function formatUTCMicroseconds(d, p) {
    return formatUTCMilliseconds(d, p) + "000";
}
function formatUTCMonthNumber(d, p) {
    return pad(d.getUTCMonth() + 1, p, 2);
}
function formatUTCMinutes(d, p) {
    return pad(d.getUTCMinutes(), p, 2);
}
function formatUTCSeconds(d, p) {
    return pad(d.getUTCSeconds(), p, 2);
}
function formatUTCWeekdayNumberMonday(d) {
    var dow = d.getUTCDay();
    return dow === 0 ? 7 : dow;
}
function formatUTCWeekNumberSunday(d, p) {
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcSunday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"])(d) - 1, d), p, 2);
}
function UTCdISO(d) {
    var day = d.getUTCDay();
    return day >= 4 || day === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcThursday"])(d) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcThursday"].ceil(d);
}
function formatUTCWeekNumberISO(d, p) {
    d = UTCdISO(d);
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcThursday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"])(d), d) + ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"])(d).getUTCDay() === 4), p, 2);
}
function formatUTCWeekdayNumberSunday(d) {
    return d.getUTCDay();
}
function formatUTCWeekNumberMonday(d, p) {
    return pad(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcMonday"].count((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcYear"])(d) - 1, d), p, 2);
}
function formatUTCYear(d, p) {
    return pad(d.getUTCFullYear() % 100, p, 2);
}
function formatUTCYearISO(d, p) {
    d = UTCdISO(d);
    return pad(d.getUTCFullYear() % 100, p, 2);
}
function formatUTCFullYear(d, p) {
    return pad(d.getUTCFullYear() % 10000, p, 4);
}
function formatUTCFullYearISO(d, p) {
    var day = d.getUTCDay();
    d = day >= 4 || day === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcThursday"])(d) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utcThursday"].ceil(d);
    return pad(d.getUTCFullYear() % 10000, p, 4);
}
function formatUTCZone() {
    return "+0000";
}
function formatLiteralPercent() {
    return "%";
}
function formatUnixTimestamp(d) {
    return +d;
}
function formatUnixTimestampSeconds(d) {
    return Math.floor(+d / 1000);
}
}),
"[project]/node_modules/d3-time-format/src/defaultLocale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>defaultLocale,
    "timeFormat": ()=>timeFormat,
    "timeParse": ()=>timeParse,
    "utcFormat": ()=>utcFormat,
    "utcParse": ()=>utcParse
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time-format/src/locale.js [app-client] (ecmascript)");
;
var locale;
var timeFormat;
var timeParse;
var utcFormat;
var utcParse;
defaultLocale({
    dateTime: "%x, %X",
    date: "%-m/%-d/%Y",
    time: "%-I:%M:%S %p",
    periods: [
        "AM",
        "PM"
    ],
    days: [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday"
    ],
    shortDays: [
        "Sun",
        "Mon",
        "Tue",
        "Wed",
        "Thu",
        "Fri",
        "Sat"
    ],
    months: [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December"
    ],
    shortMonths: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    ]
});
function defaultLocale(definition) {
    locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(definition);
    timeFormat = locale.format;
    timeParse = locale.parse;
    utcFormat = locale.utcFormat;
    utcParse = locale.utcParse;
    return locale;
}
}),
"[project]/node_modules/d3-array/src/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>number,
    "numbers": ()=>numbers
});
function number(x) {
    return x === null ? NaN : +x;
}
function* numbers(values, valueof) {
    if (valueof === undefined) {
        for (let value of values){
            if (value != null && (value = +value) >= value) {
                yield value;
            }
        }
    } else {
        let index = -1;
        for (let value of values){
            if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {
                yield value;
            }
        }
    }
}
}),
"[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bisectCenter": ()=>bisectCenter,
    "bisectLeft": ()=>bisectLeft,
    "bisectRight": ()=>bisectRight,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ascending.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/number.js [app-client] (ecmascript)");
;
;
;
const ascendingBisect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ascending$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const bisectRight = ascendingBisect.right;
const bisectLeft = ascendingBisect.left;
const bisectCenter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]).center;
const __TURBOPACK__default__export__ = bisectRight;
}),
"[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript) <export default as bisect>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "bisect": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(a, b) {
    return a = +a, b = +b, function(t) {
        return Math.round(a * (1 - t) + b * t);
    };
}
}),
"[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript) <export default as interpolateRound>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "interpolateRound": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-scale/src/constant.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>constants
});
function constants(x) {
    return function() {
        return x;
    };
}
}),
"[project]/node_modules/d3-scale/src/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>number
});
function number(x) {
    return +x;
}
}),
"[project]/node_modules/d3-scale/src/continuous.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "copy": ()=>copy,
    "default": ()=>continuous,
    "identity": ()=>identity,
    "transformer": ()=>transformer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/bisect.js [app-client] (ecmascript) <export default as bisect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/value.js [app-client] (ecmascript) <export default as interpolate>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/number.js [app-client] (ecmascript) <export default as interpolateNumber>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/round.js [app-client] (ecmascript) <export default as interpolateRound>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/number.js [app-client] (ecmascript)");
;
;
;
;
var unit = [
    0,
    1
];
function identity(x) {
    return x;
}
function normalize(a, b) {
    return (b -= a = +a) ? function(x) {
        return (x - a) / b;
    } : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(isNaN(b) ? NaN : 0.5);
}
function clamper(a, b) {
    var t;
    if (a > b) t = a, a = b, b = t;
    return function(x) {
        return Math.max(a, Math.min(b, x));
    };
}
// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].
// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].
function bimap(domain, range, interpolate) {
    var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];
    if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);
    else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);
    return function(x) {
        return r0(d0(x));
    };
}
function polymap(domain, range, interpolate) {
    var j = Math.min(domain.length, range.length) - 1, d = new Array(j), r = new Array(j), i = -1;
    // Reverse descending domains.
    if (domain[j] < domain[0]) {
        domain = domain.slice().reverse();
        range = range.slice().reverse();
    }
    while(++i < j){
        d[i] = normalize(domain[i], domain[i + 1]);
        r[i] = interpolate(range[i], range[i + 1]);
    }
    return function(x) {
        var i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$bisect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__bisect$3e$__["bisect"])(domain, x, 1, j) - 1;
        return r[i](d[i](x));
    };
}
function copy(source, target) {
    return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());
}
function transformer() {
    var domain = unit, range = unit, interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$value$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolate$3e$__["interpolate"], transform, untransform, unknown, clamp = identity, piecewise, output, input;
    function rescale() {
        var n = Math.min(domain.length, range.length);
        if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);
        piecewise = n > 2 ? polymap : bimap;
        output = input = null;
        return scale;
    }
    function scale(x) {
        return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));
    }
    scale.invert = function(y) {
        return clamp(untransform((input || (input = piecewise(range, domain.map(transform), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateNumber$3e$__["interpolateNumber"])))(y)));
    };
    scale.domain = function(_) {
        return arguments.length ? (domain = Array.from(_, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), rescale()) : domain.slice();
    };
    scale.range = function(_) {
        return arguments.length ? (range = Array.from(_), rescale()) : range.slice();
    };
    scale.rangeRound = function(_) {
        return range = Array.from(_), interpolate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$round$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateRound$3e$__["interpolateRound"], rescale();
    };
    scale.clamp = function(_) {
        return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;
    };
    scale.interpolate = function(_) {
        return arguments.length ? (interpolate = _, rescale()) : interpolate;
    };
    scale.unknown = function(_) {
        return arguments.length ? (unknown = _, scale) : unknown;
    };
    return function(t, u) {
        transform = t, untransform = u;
        return rescale();
    };
}
function continuous() {
    return transformer()(identity, identity);
}
}),
"[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "initInterpolator": ()=>initInterpolator,
    "initRange": ()=>initRange
});
function initRange(domain, range) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            this.range(domain);
            break;
        default:
            this.range(range).domain(domain);
            break;
    }
    return this;
}
function initInterpolator(domain, interpolator) {
    switch(arguments.length){
        case 0:
            break;
        case 1:
            {
                if (typeof domain === "function") this.interpolator(domain);
                else this.range(domain);
                break;
            }
        default:
            {
                this.domain(domain);
                if (typeof interpolator === "function") this.interpolator(interpolator);
                else this.range(interpolator);
                break;
            }
    }
    return this;
}
}),
"[project]/node_modules/d3-scale/src/nice.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>nice
});
function nice(domain, interval) {
    domain = domain.slice();
    var i0 = 0, i1 = domain.length - 1, x0 = domain[i0], x1 = domain[i1], t;
    if (x1 < x0) {
        t = i0, i0 = i1, i1 = t;
        t = x0, x0 = x1, x1 = t;
    }
    domain[i0] = interval.floor(x0);
    domain[i1] = interval.ceil(x1);
    return domain;
}
}),
"[project]/node_modules/d3-scale/src/time.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "calendar": ()=>calendar,
    "default": ()=>time
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/year.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/month.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__timeSunday__as__timeWeek$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript) <export timeSunday as timeWeek>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/day.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/hour.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/minute.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__second__as__timeSecond$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript) <export second as timeSecond>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time-format/src/defaultLocale.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/continuous.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$nice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/nice.js [app-client] (ecmascript)");
;
;
;
;
;
function date(t) {
    return new Date(t);
}
function number(t) {
    return t instanceof Date ? +t : +new Date(+t);
}
function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {
    var scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(), invert = scale.invert, domain = scale.domain;
    var formatMillisecond = format(".%L"), formatSecond = format(":%S"), formatMinute = format("%I:%M"), formatHour = format("%I %p"), formatDay = format("%a %d"), formatWeek = format("%b %d"), formatMonth = format("%B"), formatYear = format("%Y");
    function tickFormat(date) {
        return (second(date) < date ? formatMillisecond : minute(date) < date ? formatSecond : hour(date) < date ? formatMinute : day(date) < date ? formatHour : month(date) < date ? week(date) < date ? formatDay : formatWeek : year(date) < date ? formatMonth : formatYear)(date);
    }
    scale.invert = function(y) {
        return new Date(invert(y));
    };
    scale.domain = function(_) {
        return arguments.length ? domain(Array.from(_, number)) : domain().map(date);
    };
    scale.ticks = function(interval) {
        var d = domain();
        return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);
    };
    scale.tickFormat = function(count, specifier) {
        return specifier == null ? tickFormat : format(specifier);
    };
    scale.nice = function(interval) {
        var d = domain();
        if (!interval || typeof interval.range !== "function") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);
        return interval ? domain((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$nice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(d, interval)) : scale;
    };
    scale.copy = function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["copy"])(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));
    };
    return scale;
}
function time() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initRange"].apply(calendar(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeTicks"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeTickInterval"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$year$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeYear"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonth"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__timeSunday__as__timeWeek$3e$__["timeWeek"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeHour"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMinute"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__second__as__timeSecond$3e$__["timeSecond"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeFormat"]).domain([
        new Date(2000, 0, 1),
        new Date(2000, 0, 2)
    ]), arguments);
}
}),
"[project]/node_modules/d3-scale/src/time.js [app-client] (ecmascript) <export default as scaleTime>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "scaleTime": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$time$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$time$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/time.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/min.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>min
});
function min(values, valueof) {
    let min;
    if (valueof === undefined) {
        for (const value of values){
            if (value != null && (min > value || min === undefined && value >= value)) {
                min = value;
            }
        }
    } else {
        let index = -1;
        for (let value of values){
            if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {
                min = value;
            }
        }
    }
    return min;
}
}),
"[project]/node_modules/d3-array/src/min.js [app-client] (ecmascript) <export default as min>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "min": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/min.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/max.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>max
});
function max(values, valueof) {
    let max;
    if (valueof === undefined) {
        for (const value of values){
            if (value != null && (max < value || max === undefined && value >= value)) {
                max = value;
            }
        }
    } else {
        let index = -1;
        for (let value of values){
            if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {
                max = value;
            }
        }
    }
    return max;
}
}),
"[project]/node_modules/d3-array/src/max.js [app-client] (ecmascript) <export default as max>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "max": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/max.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript) <export default as ticks>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ticks": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "formatDecimalParts": ()=>formatDecimalParts
});
function __TURBOPACK__default__export__(x) {
    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString("en").replace(/,/g, "") : x.toString(10);
}
function formatDecimalParts(x, p) {
    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf("e")) < 0) return null; // NaN, ±Infinity
    var i, coefficient = x.slice(0, i);
    // The string returned by toExponential either has the form \d\.\d+e[-+]\d+
    // (e.g., 1.2e+3) or the form \de[-+]\d+ (e.g., 1e+3).
    return [
        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
        +x.slice(i + 1)
    ];
}
}),
"[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(x) {
    return x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(Math.abs(x)), x ? x[1] : NaN;
}
}),
"[project]/node_modules/d3-format/src/formatGroup.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(grouping, thousands) {
    return function(value, width) {
        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;
        while(i > 0 && g > 0){
            if (length + g + 1 > width) g = Math.max(1, width - length);
            t.push(value.substring(i -= g, i + g));
            if ((length += g + 1) > width) break;
            g = grouping[j = (j + 1) % grouping.length];
        }
        return t.reverse().join(thousands);
    };
}
}),
"[project]/node_modules/d3-format/src/formatNumerals.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(numerals) {
    return function(value) {
        return value.replace(/[0-9]/g, function(i) {
            return numerals[+i];
        });
    };
}
}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// [[fill]align][sign][symbol][0][width][,][.precision][~][type]
__turbopack_context__.s({
    "FormatSpecifier": ()=>FormatSpecifier,
    "default": ()=>formatSpecifier
});
var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
    if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
    var match;
    return new FormatSpecifier({
        fill: match[1],
        align: match[2],
        sign: match[3],
        symbol: match[4],
        zero: match[5],
        width: match[6],
        comma: match[7],
        precision: match[8] && match[8].slice(1),
        trim: match[9],
        type: match[10]
    });
}
formatSpecifier.prototype = FormatSpecifier.prototype; // instanceof
function FormatSpecifier(specifier) {
    this.fill = specifier.fill === undefined ? " " : specifier.fill + "";
    this.align = specifier.align === undefined ? ">" : specifier.align + "";
    this.sign = specifier.sign === undefined ? "-" : specifier.sign + "";
    this.symbol = specifier.symbol === undefined ? "" : specifier.symbol + "";
    this.zero = !!specifier.zero;
    this.width = specifier.width === undefined ? undefined : +specifier.width;
    this.comma = !!specifier.comma;
    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;
    this.trim = !!specifier.trim;
    this.type = specifier.type === undefined ? "" : specifier.type + "";
}
FormatSpecifier.prototype.toString = function() {
    return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === undefined ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === undefined ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};
}),
"[project]/node_modules/d3-format/src/formatTrim.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(s) {
    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){
        switch(s[i]){
            case ".":
                i0 = i1 = i;
                break;
            case "0":
                if (i0 === 0) i0 = i;
                i1 = i;
                break;
            default:
                if (!+s[i]) break out;
                if (i0 > 0) i0 = 0;
                break;
        }
    }
    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
}
}),
"[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "prefixExponent": ()=>prefixExponent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
var prefixExponent;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;
    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join("0") : i > 0 ? coefficient.slice(0, i) + "." + coefficient.slice(i) : "0." + new Array(1 - i).join("0") + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, Math.max(0, p + i - 1))[0]; // less than 1y!
}
}),
"[project]/node_modules/d3-format/src/formatRounded.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(x, p) {
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDecimalParts"])(x, p);
    if (!d) return x + "";
    var coefficient = d[0], exponent = d[1];
    return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}
}),
"[project]/node_modules/d3-format/src/formatTypes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatDecimal.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatRounded.js [app-client] (ecmascript)");
;
;
;
const __TURBOPACK__default__export__ = {
    "%": (x, p)=>(x * 100).toFixed(p),
    "b": (x)=>Math.round(x).toString(2),
    "c": (x)=>x + "",
    "d": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatDecimal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "e": (x, p)=>x.toExponential(p),
    "f": (x, p)=>x.toFixed(p),
    "g": (x, p)=>x.toPrecision(p),
    "o": (x)=>Math.round(x).toString(8),
    "p": (x, p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(x * 100, p),
    "r": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatRounded$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "s": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    "X": (x)=>Math.round(x).toString(16).toUpperCase(),
    "x": (x)=>Math.round(x).toString(16)
};
}),
"[project]/node_modules/d3-format/src/identity.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(x) {
    return x;
}
}),
"[project]/node_modules/d3-format/src/locale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatNumerals.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTrim.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatPrefixAuto.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/identity.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
var map = Array.prototype.map, prefixes = [
    "y",
    "z",
    "a",
    "f",
    "p",
    "n",
    "µ",
    "m",
    "",
    "k",
    "M",
    "G",
    "T",
    "P",
    "E",
    "Z",
    "Y"
];
function __TURBOPACK__default__export__(locale) {
    var group = locale.grouping === undefined || locale.thousands === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(map.call(locale.grouping, Number), locale.thousands + ""), currencyPrefix = locale.currency === undefined ? "" : locale.currency[0] + "", currencySuffix = locale.currency === undefined ? "" : locale.currency[1] + "", decimal = locale.decimal === undefined ? "." : locale.decimal + "", numerals = locale.numerals === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatNumerals$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? "%" : locale.percent + "", minus = locale.minus === undefined ? "−" : locale.minus + "", nan = locale.nan === undefined ? "NaN" : locale.nan + "";
    function newFormat(specifier) {
        specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(specifier);
        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
        // The "n" type is an alias for ",g".
        if (type === "n") comma = true, type = "g";
        else if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][type]) precision === undefined && (precision = 12), trim = true, type = "g";
        // If zero fill is specified, padding goes after sign and before digits.
        if (zero || fill === "0" && align === "=") zero = true, fill = "0", align = "=";
        // Compute the prefix and suffix.
        // For SI-prefix, the suffix is lazily computed.
        var prefix = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
        // What format function should we use?
        // Is this an integer type?
        // Can this type generate exponential notation?
        var formatType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][type], maybeSuffix = /[defgprs%]/.test(type);
        // Set the default precision if not specified,
        // or clamp the specified precision to the supported range.
        // For significant precision, it must be in [1, 21].
        // For fixed precision, it must be in [0, 20].
        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
        function format(value) {
            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;
            if (type === "c") {
                valueSuffix = formatType(value) + valueSuffix;
                value = "";
            } else {
                value = +value;
                // Determine the sign. -0 is not less than 0, but 1 / -0 is!
                var valueNegative = value < 0 || 1 / value < 0;
                // Perform the initial formatting.
                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
                // Trim insignificant zeros.
                if (trim) value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatTrim$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value);
                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.
                if (valueNegative && +value === 0 && sign !== "+") valueNegative = false;
                // Compute the prefix and suffix.
                valuePrefix = (valueNegative ? sign === "(" ? sign : minus : sign === "-" || sign === "(" ? "" : sign) + valuePrefix;
                valueSuffix = (type === "s" ? prefixes[8 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatPrefixAuto$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prefixExponent"] / 3] : "") + valueSuffix + (valueNegative && sign === "(" ? ")" : "");
                // Break the formatted value into the integer “value” part that can be
                // grouped, and fractional or exponential “suffix” part that is not.
                if (maybeSuffix) {
                    i = -1, n = value.length;
                    while(++i < n){
                        if (c = value.charCodeAt(i), 48 > c || c > 57) {
                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;
                            value = value.slice(0, i);
                            break;
                        }
                    }
                }
            }
            // If the fill character is not "0", grouping is applied before padding.
            if (comma && !zero) value = group(value, Infinity);
            // Compute the padding.
            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
            // If the fill character is "0", grouping is applied after padding.
            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
            // Reconstruct the final output based on the desired alignment.
            switch(align){
                case "<":
                    value = valuePrefix + value + valueSuffix + padding;
                    break;
                case "=":
                    value = valuePrefix + padding + value + valueSuffix;
                    break;
                case "^":
                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
                    break;
                default:
                    value = padding + valuePrefix + value + valueSuffix;
                    break;
            }
            return numerals(value);
        }
        format.toString = function() {
            return specifier + "";
        };
        return format;
    }
    function formatPrefix(specifier, value) {
        var f = newFormat((specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(specifier), specifier.type = "f", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];
        return function(value) {
            return f(k * value) + prefix;
        };
    }
    return {
        format: newFormat,
        formatPrefix: formatPrefix
    };
}
}),
"[project]/node_modules/d3-format/src/defaultLocale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>defaultLocale,
    "format": ()=>format,
    "formatPrefix": ()=>formatPrefix
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/locale.js [app-client] (ecmascript)");
;
var locale;
var format;
var formatPrefix;
defaultLocale({
    thousands: ",",
    grouping: [
        3
    ],
    currency: [
        "$",
        ""
    ]
});
function defaultLocale(definition) {
    locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$locale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(definition);
    format = locale.format;
    formatPrefix = locale.formatPrefix;
    return locale;
}
}),
"[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript) <export default as formatSpecifier>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "formatSpecifier": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step) {
    return Math.max(0, -(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}),
"[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript) <export default as precisionFixed>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionFixed": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step, value) {
    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value) / 3))) * 3 - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Math.abs(step)));
}
}),
"[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript) <export default as precisionPrefix>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionPrefix": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/exponent.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__(step, max) {
    step = Math.abs(step), max = Math.abs(max) - step;
    return Math.max(0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(max) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$exponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(step)) + 1;
}
}),
"[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript) <export default as precisionRound>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "precisionRound": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-scale/src/tickFormat.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>tickFormat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/defaultLocale.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/formatSpecifier.js [app-client] (ecmascript) <export default as formatSpecifier>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionFixed.js [app-client] (ecmascript) <export default as precisionFixed>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionPrefix.js [app-client] (ecmascript) <export default as precisionPrefix>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-format/src/precisionRound.js [app-client] (ecmascript) <export default as precisionRound>");
;
;
function tickFormat(start, stop, count, specifier) {
    var step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickStep"])(start, stop, count), precision;
    specifier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$formatSpecifier$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__formatSpecifier$3e$__["formatSpecifier"])(specifier == null ? ",f" : specifier);
    switch(specifier.type){
        case "s":
            {
                var value = Math.max(Math.abs(start), Math.abs(stop));
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionPrefix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionPrefix$3e$__["precisionPrefix"])(step, value))) specifier.precision = precision;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatPrefix"])(specifier, value);
            }
        case "":
        case "e":
        case "g":
        case "p":
        case "r":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionRound$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionRound$3e$__["precisionRound"])(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === "e");
                break;
            }
        case "f":
        case "%":
            {
                if (specifier.precision == null && !isNaN(precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$precisionFixed$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__precisionFixed$3e$__["precisionFixed"])(step))) specifier.precision = precision - (specifier.type === "%") * 2;
                break;
            }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["format"])(specifier);
}
}),
"[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>linear,
    "linearish": ()=>linearish
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript) <export default as ticks>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/ticks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/continuous.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/init.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/tickFormat.js [app-client] (ecmascript)");
;
;
;
;
function linearish(scale) {
    var domain = scale.domain;
    scale.ticks = function(count) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ticks$3e$__["ticks"])(d[0], d[d.length - 1], count == null ? 10 : count);
    };
    scale.tickFormat = function(count, specifier) {
        var d = domain();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$tickFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(d[0], d[d.length - 1], count == null ? 10 : count, specifier);
    };
    scale.nice = function(count) {
        if (count == null) count = 10;
        var d = domain();
        var i0 = 0;
        var i1 = d.length - 1;
        var start = d[i0];
        var stop = d[i1];
        var prestep;
        var step;
        var maxIter = 10;
        if (stop < start) {
            step = start, start = stop, stop = step;
            step = i0, i0 = i1, i1 = step;
        }
        while(maxIter-- > 0){
            step = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$ticks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickIncrement"])(start, stop, count);
            if (step === prestep) {
                d[i0] = start;
                d[i1] = stop;
                return domain(d);
            } else if (step > 0) {
                start = Math.floor(start / step) * step;
                stop = Math.ceil(stop / step) * step;
            } else if (step < 0) {
                start = Math.ceil(start * step) / step;
                stop = Math.floor(stop * step) / step;
            } else {
                break;
            }
            prestep = step;
        }
        return scale;
    };
    return scale;
}
function linear() {
    var scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    scale.copy = function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$continuous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["copy"])(scale, linear());
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$init$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initRange"].apply(scale, arguments);
    return linearish(scale);
}
}),
"[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript) <export default as scaleLinear>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "scaleLinear": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-color/src/math.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "degrees": ()=>degrees,
    "radians": ()=>radians
});
const radians = Math.PI / 180;
const degrees = 180 / Math.PI;
}),
"[project]/node_modules/d3-color/src/lab.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Hcl": ()=>Hcl,
    "Lab": ()=>Lab,
    "default": ()=>lab,
    "gray": ()=>gray,
    "hcl": ()=>hcl,
    "lch": ()=>lch
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$define$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-color/src/define.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-color/src/color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-color/src/math.js [app-client] (ecmascript)");
;
;
;
// https://observablehq.com/@mbostock/lab-and-rgb
const K = 18, Xn = 0.96422, Yn = 1, Zn = 0.82521, t0 = 4 / 29, t1 = 6 / 29, t2 = 3 * t1 * t1, t3 = t1 * t1 * t1;
function labConvert(o) {
    if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);
    if (o instanceof Hcl) return hcl2lab(o);
    if (!(o instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rgb"])) o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rgbConvert"])(o);
    var r = rgb2lrgb(o.r), g = rgb2lrgb(o.g), b = rgb2lrgb(o.b), y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;
    if (r === g && g === b) x = z = y;
    else {
        x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);
        z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);
    }
    return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);
}
function gray(l, opacity) {
    return new Lab(l, 0, 0, opacity == null ? 1 : opacity);
}
function lab(l, a, b, opacity) {
    return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);
}
function Lab(l, a, b, opacity) {
    this.l = +l;
    this.a = +a;
    this.b = +b;
    this.opacity = +opacity;
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$define$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Lab, lab, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$define$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color"], {
    brighter (k) {
        return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);
    },
    darker (k) {
        return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);
    },
    rgb () {
        var y = (this.l + 16) / 116, x = isNaN(this.a) ? y : y + this.a / 500, z = isNaN(this.b) ? y : y - this.b / 200;
        x = Xn * lab2xyz(x);
        y = Yn * lab2xyz(y);
        z = Zn * lab2xyz(z);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rgb"](lrgb2rgb(3.1338561 * x - 1.6168667 * y - 0.4906146 * z), lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z), lrgb2rgb(0.0719453 * x - 0.2289914 * y + 1.4052427 * z), this.opacity);
    }
}));
function xyz2lab(t) {
    return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;
}
function lab2xyz(t) {
    return t > t1 ? t * t * t : t2 * (t - t0);
}
function lrgb2rgb(x) {
    return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);
}
function rgb2lrgb(x) {
    return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);
}
function hclConvert(o) {
    if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);
    if (!(o instanceof Lab)) o = labConvert(o);
    if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0 < o.l && o.l < 100 ? 0 : NaN, o.l, o.opacity);
    var h = Math.atan2(o.b, o.a) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["degrees"];
    return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);
}
function lch(l, c, h, opacity) {
    return arguments.length === 1 ? hclConvert(l) : new Hcl(h, c, l, opacity == null ? 1 : opacity);
}
function hcl(h, c, l, opacity) {
    return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);
}
function Hcl(h, c, l, opacity) {
    this.h = +h;
    this.c = +c;
    this.l = +l;
    this.opacity = +opacity;
}
function hcl2lab(o) {
    if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);
    var h = o.h * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["radians"];
    return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$define$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Hcl, hcl, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$define$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color"], {
    brighter (k) {
        return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);
    },
    darker (k) {
        return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);
    },
    rgb () {
        return hcl2lab(this).rgb();
    }
}));
}),
"[project]/node_modules/d3-interpolate/src/hcl.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "hclLong": ()=>hclLong
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$lab$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-color/src/lab.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/color.js [app-client] (ecmascript)");
;
;
function hcl(hue) {
    return function(start, end) {
        var h = hue((start = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$lab$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hcl"])(start)).h, (end = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$color$2f$src$2f$lab$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hcl"])(end)).h), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(start.c, end.c), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(start.l, end.l), opacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(start.opacity, end.opacity);
        return function(t) {
            start.h = h(t);
            start.c = c(t);
            start.l = l(t);
            start.opacity = opacity(t);
            return start + "";
        };
    };
}
const __TURBOPACK__default__export__ = hcl(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hue"]);
var hclLong = hcl(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
}),
"[project]/node_modules/d3-interpolate/src/hcl.js [app-client] (ecmascript) <export default as interpolateHcl>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "interpolateHcl": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$hcl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$hcl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/hcl.js [app-client] (ecmascript)");
}),
"[project]/node_modules/d3-axis/src/identity.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
function __TURBOPACK__default__export__(x) {
    return x;
}
}),
"[project]/node_modules/d3-axis/src/axis.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "axisBottom": ()=>axisBottom,
    "axisLeft": ()=>axisLeft,
    "axisRight": ()=>axisRight,
    "axisTop": ()=>axisTop
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$axis$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-axis/src/identity.js [app-client] (ecmascript)");
;
var top = 1, right = 2, bottom = 3, left = 4, epsilon = 1e-6;
function translateX(x) {
    return "translate(" + x + ",0)";
}
function translateY(y) {
    return "translate(0," + y + ")";
}
function number(scale) {
    return (d)=>+scale(d);
}
function center(scale, offset) {
    offset = Math.max(0, scale.bandwidth() - offset * 2) / 2;
    if (scale.round()) offset = Math.round(offset);
    return (d)=>+scale(d) + offset;
}
function entering() {
    return !this.__axis;
}
function axis(orient, scale) {
    var tickArguments = [], tickValues = null, tickFormat = null, tickSizeInner = 6, tickSizeOuter = 6, tickPadding = 3, offset = typeof window !== "undefined" && window.devicePixelRatio > 1 ? 0 : 0.5, k = orient === top || orient === left ? -1 : 1, x = orient === left || orient === right ? "x" : "y", transform = orient === top || orient === bottom ? translateX : translateY;
    function axis(context) {
        var values = tickValues == null ? scale.ticks ? scale.ticks.apply(scale, tickArguments) : scale.domain() : tickValues, format = tickFormat == null ? scale.tickFormat ? scale.tickFormat.apply(scale, tickArguments) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$axis$2f$src$2f$identity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : tickFormat, spacing = Math.max(tickSizeInner, 0) + tickPadding, range = scale.range(), range0 = +range[0] + offset, range1 = +range[range.length - 1] + offset, position = (scale.bandwidth ? center : number)(scale.copy(), offset), selection = context.selection ? context.selection() : context, path = selection.selectAll(".domain").data([
            null
        ]), tick = selection.selectAll(".tick").data(values, scale).order(), tickExit = tick.exit(), tickEnter = tick.enter().append("g").attr("class", "tick"), line = tick.select("line"), text = tick.select("text");
        path = path.merge(path.enter().insert("path", ".tick").attr("class", "domain").attr("stroke", "currentColor"));
        tick = tick.merge(tickEnter);
        line = line.merge(tickEnter.append("line").attr("stroke", "currentColor").attr(x + "2", k * tickSizeInner));
        text = text.merge(tickEnter.append("text").attr("fill", "currentColor").attr(x, k * spacing).attr("dy", orient === top ? "0em" : orient === bottom ? "0.71em" : "0.32em"));
        if (context !== selection) {
            path = path.transition(context);
            tick = tick.transition(context);
            line = line.transition(context);
            text = text.transition(context);
            tickExit = tickExit.transition(context).attr("opacity", epsilon).attr("transform", function(d) {
                return isFinite(d = position(d)) ? transform(d + offset) : this.getAttribute("transform");
            });
            tickEnter.attr("opacity", epsilon).attr("transform", function(d) {
                var p = this.parentNode.__axis;
                return transform((p && isFinite(p = p(d)) ? p : position(d)) + offset);
            });
        }
        tickExit.remove();
        path.attr("d", orient === left || orient === right ? tickSizeOuter ? "M" + k * tickSizeOuter + "," + range0 + "H" + offset + "V" + range1 + "H" + k * tickSizeOuter : "M" + offset + "," + range0 + "V" + range1 : tickSizeOuter ? "M" + range0 + "," + k * tickSizeOuter + "V" + offset + "H" + range1 + "V" + k * tickSizeOuter : "M" + range0 + "," + offset + "H" + range1);
        tick.attr("opacity", 1).attr("transform", function(d) {
            return transform(position(d) + offset);
        });
        line.attr(x + "2", k * tickSizeInner);
        text.attr(x, k * spacing).text(format);
        selection.filter(entering).attr("fill", "none").attr("font-size", 10).attr("font-family", "sans-serif").attr("text-anchor", orient === right ? "start" : orient === left ? "end" : "middle");
        selection.each(function() {
            this.__axis = position;
        });
    }
    axis.scale = function(_) {
        return arguments.length ? (scale = _, axis) : scale;
    };
    axis.ticks = function() {
        return tickArguments = Array.from(arguments), axis;
    };
    axis.tickArguments = function(_) {
        return arguments.length ? (tickArguments = _ == null ? [] : Array.from(_), axis) : tickArguments.slice();
    };
    axis.tickValues = function(_) {
        return arguments.length ? (tickValues = _ == null ? null : Array.from(_), axis) : tickValues && tickValues.slice();
    };
    axis.tickFormat = function(_) {
        return arguments.length ? (tickFormat = _, axis) : tickFormat;
    };
    axis.tickSize = function(_) {
        return arguments.length ? (tickSizeInner = tickSizeOuter = +_, axis) : tickSizeInner;
    };
    axis.tickSizeInner = function(_) {
        return arguments.length ? (tickSizeInner = +_, axis) : tickSizeInner;
    };
    axis.tickSizeOuter = function(_) {
        return arguments.length ? (tickSizeOuter = +_, axis) : tickSizeOuter;
    };
    axis.tickPadding = function(_) {
        return arguments.length ? (tickPadding = +_, axis) : tickPadding;
    };
    axis.offset = function(_) {
        return arguments.length ? (offset = +_, axis) : offset;
    };
    return axis;
}
function axisTop(scale) {
    return axis(top, scale);
}
function axisRight(scale) {
    return axis(right, scale);
}
function axisBottom(scale) {
    return axis(bottom, scale);
}
function axisLeft(scale) {
    return axis(left, scale);
}
}),
"[project]/node_modules/d3-time/src/millisecond.js [app-client] (ecmascript) <export millisecond as timeMillisecond>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "timeMillisecond": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["millisecond"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/millisecond.js [app-client] (ecmascript)");
}),
"[project]/node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-EK5VF46D.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "diagram": ()=>diagram
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-55PJQP7W.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mermaid/dist/chunks/mermaid.core/chunk-3XYRH5AP.mjs [app-client] (ecmascript)");
// src/diagrams/gantt/ganttDb.js
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@braintree/sanitize-url/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$isoWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/plugin/isoWeek.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$customParseFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/plugin/customParseFormat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$advancedFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/plugin/advancedFormat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3/src/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-client] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$time$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleTime$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/time.js [app-client] (ecmascript) <export default as scaleTime>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__min$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/min.js [app-client] (ecmascript) <export default as min>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__max$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-array/src/max.js [app-client] (ecmascript) <export default as max>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-scale/src/linear.js [app-client] (ecmascript) <export default as scaleLinear>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$hcl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateHcl$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-interpolate/src/hcl.js [app-client] (ecmascript) <export default as interpolateHcl>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$axis$2f$src$2f$axis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-axis/src/axis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time-format/src/defaultLocale.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__millisecond__as__timeMillisecond$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/millisecond.js [app-client] (ecmascript) <export millisecond as timeMillisecond>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__second__as__timeSecond$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/second.js [app-client] (ecmascript) <export second as timeSecond>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/minute.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/hour.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/day.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/week.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/d3-time/src/month.js [app-client] (ecmascript)");
;
;
// src/diagrams/gantt/parser/gantt.jison
var parser = function() {
    var o = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(k, v, o2, l) {
        for(o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v);
        return o2;
    }, "o"), $V0 = [
        6,
        8,
        10,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
        33,
        35,
        36,
        38,
        40
    ], $V1 = [
        1,
        26
    ], $V2 = [
        1,
        27
    ], $V3 = [
        1,
        28
    ], $V4 = [
        1,
        29
    ], $V5 = [
        1,
        30
    ], $V6 = [
        1,
        31
    ], $V7 = [
        1,
        32
    ], $V8 = [
        1,
        33
    ], $V9 = [
        1,
        34
    ], $Va = [
        1,
        9
    ], $Vb = [
        1,
        10
    ], $Vc = [
        1,
        11
    ], $Vd = [
        1,
        12
    ], $Ve = [
        1,
        13
    ], $Vf = [
        1,
        14
    ], $Vg = [
        1,
        15
    ], $Vh = [
        1,
        16
    ], $Vi = [
        1,
        19
    ], $Vj = [
        1,
        20
    ], $Vk = [
        1,
        21
    ], $Vl = [
        1,
        22
    ], $Vm = [
        1,
        23
    ], $Vn = [
        1,
        25
    ], $Vo = [
        1,
        35
    ];
    var parser2 = {
        trace: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function trace() {}, "trace"),
        yy: {},
        symbols_: {
            "error": 2,
            "start": 3,
            "gantt": 4,
            "document": 5,
            "EOF": 6,
            "line": 7,
            "SPACE": 8,
            "statement": 9,
            "NL": 10,
            "weekday": 11,
            "weekday_monday": 12,
            "weekday_tuesday": 13,
            "weekday_wednesday": 14,
            "weekday_thursday": 15,
            "weekday_friday": 16,
            "weekday_saturday": 17,
            "weekday_sunday": 18,
            "weekend": 19,
            "weekend_friday": 20,
            "weekend_saturday": 21,
            "dateFormat": 22,
            "inclusiveEndDates": 23,
            "topAxis": 24,
            "axisFormat": 25,
            "tickInterval": 26,
            "excludes": 27,
            "includes": 28,
            "todayMarker": 29,
            "title": 30,
            "acc_title": 31,
            "acc_title_value": 32,
            "acc_descr": 33,
            "acc_descr_value": 34,
            "acc_descr_multiline_value": 35,
            "section": 36,
            "clickStatement": 37,
            "taskTxt": 38,
            "taskData": 39,
            "click": 40,
            "callbackname": 41,
            "callbackargs": 42,
            "href": 43,
            "clickStatementDebug": 44,
            "$accept": 0,
            "$end": 1
        },
        terminals_: {
            2: "error",
            4: "gantt",
            6: "EOF",
            8: "SPACE",
            10: "NL",
            12: "weekday_monday",
            13: "weekday_tuesday",
            14: "weekday_wednesday",
            15: "weekday_thursday",
            16: "weekday_friday",
            17: "weekday_saturday",
            18: "weekday_sunday",
            20: "weekend_friday",
            21: "weekend_saturday",
            22: "dateFormat",
            23: "inclusiveEndDates",
            24: "topAxis",
            25: "axisFormat",
            26: "tickInterval",
            27: "excludes",
            28: "includes",
            29: "todayMarker",
            30: "title",
            31: "acc_title",
            32: "acc_title_value",
            33: "acc_descr",
            34: "acc_descr_value",
            35: "acc_descr_multiline_value",
            36: "section",
            38: "taskTxt",
            39: "taskData",
            40: "click",
            41: "callbackname",
            42: "callbackargs",
            43: "href"
        },
        productions_: [
            0,
            [
                3,
                3
            ],
            [
                5,
                0
            ],
            [
                5,
                2
            ],
            [
                7,
                2
            ],
            [
                7,
                1
            ],
            [
                7,
                1
            ],
            [
                7,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                11,
                1
            ],
            [
                19,
                1
            ],
            [
                19,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                2
            ],
            [
                9,
                2
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                1
            ],
            [
                9,
                2
            ],
            [
                37,
                2
            ],
            [
                37,
                3
            ],
            [
                37,
                3
            ],
            [
                37,
                4
            ],
            [
                37,
                3
            ],
            [
                37,
                4
            ],
            [
                37,
                2
            ],
            [
                44,
                2
            ],
            [
                44,
                3
            ],
            [
                44,
                3
            ],
            [
                44,
                4
            ],
            [
                44,
                3
            ],
            [
                44,
                4
            ],
            [
                44,
                2
            ]
        ],
        performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {
            var $0 = $$.length - 1;
            switch(yystate){
                case 1:
                    return $$[$0 - 1];
                    //TURBOPACK unreachable
                    ;
                case 2:
                    this.$ = [];
                    break;
                case 3:
                    $$[$0 - 1].push($$[$0]);
                    this.$ = $$[$0 - 1];
                    break;
                case 4:
                case 5:
                    this.$ = $$[$0];
                    break;
                case 6:
                case 7:
                    this.$ = [];
                    break;
                case 8:
                    yy.setWeekday("monday");
                    break;
                case 9:
                    yy.setWeekday("tuesday");
                    break;
                case 10:
                    yy.setWeekday("wednesday");
                    break;
                case 11:
                    yy.setWeekday("thursday");
                    break;
                case 12:
                    yy.setWeekday("friday");
                    break;
                case 13:
                    yy.setWeekday("saturday");
                    break;
                case 14:
                    yy.setWeekday("sunday");
                    break;
                case 15:
                    yy.setWeekend("friday");
                    break;
                case 16:
                    yy.setWeekend("saturday");
                    break;
                case 17:
                    yy.setDateFormat($$[$0].substr(11));
                    this.$ = $$[$0].substr(11);
                    break;
                case 18:
                    yy.enableInclusiveEndDates();
                    this.$ = $$[$0].substr(18);
                    break;
                case 19:
                    yy.TopAxis();
                    this.$ = $$[$0].substr(8);
                    break;
                case 20:
                    yy.setAxisFormat($$[$0].substr(11));
                    this.$ = $$[$0].substr(11);
                    break;
                case 21:
                    yy.setTickInterval($$[$0].substr(13));
                    this.$ = $$[$0].substr(13);
                    break;
                case 22:
                    yy.setExcludes($$[$0].substr(9));
                    this.$ = $$[$0].substr(9);
                    break;
                case 23:
                    yy.setIncludes($$[$0].substr(9));
                    this.$ = $$[$0].substr(9);
                    break;
                case 24:
                    yy.setTodayMarker($$[$0].substr(12));
                    this.$ = $$[$0].substr(12);
                    break;
                case 27:
                    yy.setDiagramTitle($$[$0].substr(6));
                    this.$ = $$[$0].substr(6);
                    break;
                case 28:
                    this.$ = $$[$0].trim();
                    yy.setAccTitle(this.$);
                    break;
                case 29:
                case 30:
                    this.$ = $$[$0].trim();
                    yy.setAccDescription(this.$);
                    break;
                case 31:
                    yy.addSection($$[$0].substr(8));
                    this.$ = $$[$0].substr(8);
                    break;
                case 33:
                    yy.addTask($$[$0 - 1], $$[$0]);
                    this.$ = "task";
                    break;
                case 34:
                    this.$ = $$[$0 - 1];
                    yy.setClickEvent($$[$0 - 1], $$[$0], null);
                    break;
                case 35:
                    this.$ = $$[$0 - 2];
                    yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);
                    break;
                case 36:
                    this.$ = $$[$0 - 2];
                    yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);
                    yy.setLink($$[$0 - 2], $$[$0]);
                    break;
                case 37:
                    this.$ = $$[$0 - 3];
                    yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);
                    yy.setLink($$[$0 - 3], $$[$0]);
                    break;
                case 38:
                    this.$ = $$[$0 - 2];
                    yy.setClickEvent($$[$0 - 2], $$[$0], null);
                    yy.setLink($$[$0 - 2], $$[$0 - 1]);
                    break;
                case 39:
                    this.$ = $$[$0 - 3];
                    yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);
                    yy.setLink($$[$0 - 3], $$[$0 - 2]);
                    break;
                case 40:
                    this.$ = $$[$0 - 1];
                    yy.setLink($$[$0 - 1], $$[$0]);
                    break;
                case 41:
                case 47:
                    this.$ = $$[$0 - 1] + " " + $$[$0];
                    break;
                case 42:
                case 43:
                case 45:
                    this.$ = $$[$0 - 2] + " " + $$[$0 - 1] + " " + $$[$0];
                    break;
                case 44:
                case 46:
                    this.$ = $$[$0 - 3] + " " + $$[$0 - 2] + " " + $$[$0 - 1] + " " + $$[$0];
                    break;
            }
        }, "anonymous"),
        table: [
            {
                3: 1,
                4: [
                    1,
                    2
                ]
            },
            {
                1: [
                    3
                ]
            },
            o($V0, [
                2,
                2
            ], {
                5: 3
            }),
            {
                6: [
                    1,
                    4
                ],
                7: 5,
                8: [
                    1,
                    6
                ],
                9: 7,
                10: [
                    1,
                    8
                ],
                11: 17,
                12: $V1,
                13: $V2,
                14: $V3,
                15: $V4,
                16: $V5,
                17: $V6,
                18: $V7,
                19: 18,
                20: $V8,
                21: $V9,
                22: $Va,
                23: $Vb,
                24: $Vc,
                25: $Vd,
                26: $Ve,
                27: $Vf,
                28: $Vg,
                29: $Vh,
                30: $Vi,
                31: $Vj,
                33: $Vk,
                35: $Vl,
                36: $Vm,
                37: 24,
                38: $Vn,
                40: $Vo
            },
            o($V0, [
                2,
                7
            ], {
                1: [
                    2,
                    1
                ]
            }),
            o($V0, [
                2,
                3
            ]),
            {
                9: 36,
                11: 17,
                12: $V1,
                13: $V2,
                14: $V3,
                15: $V4,
                16: $V5,
                17: $V6,
                18: $V7,
                19: 18,
                20: $V8,
                21: $V9,
                22: $Va,
                23: $Vb,
                24: $Vc,
                25: $Vd,
                26: $Ve,
                27: $Vf,
                28: $Vg,
                29: $Vh,
                30: $Vi,
                31: $Vj,
                33: $Vk,
                35: $Vl,
                36: $Vm,
                37: 24,
                38: $Vn,
                40: $Vo
            },
            o($V0, [
                2,
                5
            ]),
            o($V0, [
                2,
                6
            ]),
            o($V0, [
                2,
                17
            ]),
            o($V0, [
                2,
                18
            ]),
            o($V0, [
                2,
                19
            ]),
            o($V0, [
                2,
                20
            ]),
            o($V0, [
                2,
                21
            ]),
            o($V0, [
                2,
                22
            ]),
            o($V0, [
                2,
                23
            ]),
            o($V0, [
                2,
                24
            ]),
            o($V0, [
                2,
                25
            ]),
            o($V0, [
                2,
                26
            ]),
            o($V0, [
                2,
                27
            ]),
            {
                32: [
                    1,
                    37
                ]
            },
            {
                34: [
                    1,
                    38
                ]
            },
            o($V0, [
                2,
                30
            ]),
            o($V0, [
                2,
                31
            ]),
            o($V0, [
                2,
                32
            ]),
            {
                39: [
                    1,
                    39
                ]
            },
            o($V0, [
                2,
                8
            ]),
            o($V0, [
                2,
                9
            ]),
            o($V0, [
                2,
                10
            ]),
            o($V0, [
                2,
                11
            ]),
            o($V0, [
                2,
                12
            ]),
            o($V0, [
                2,
                13
            ]),
            o($V0, [
                2,
                14
            ]),
            o($V0, [
                2,
                15
            ]),
            o($V0, [
                2,
                16
            ]),
            {
                41: [
                    1,
                    40
                ],
                43: [
                    1,
                    41
                ]
            },
            o($V0, [
                2,
                4
            ]),
            o($V0, [
                2,
                28
            ]),
            o($V0, [
                2,
                29
            ]),
            o($V0, [
                2,
                33
            ]),
            o($V0, [
                2,
                34
            ], {
                42: [
                    1,
                    42
                ],
                43: [
                    1,
                    43
                ]
            }),
            o($V0, [
                2,
                40
            ], {
                41: [
                    1,
                    44
                ]
            }),
            o($V0, [
                2,
                35
            ], {
                43: [
                    1,
                    45
                ]
            }),
            o($V0, [
                2,
                36
            ]),
            o($V0, [
                2,
                38
            ], {
                42: [
                    1,
                    46
                ]
            }),
            o($V0, [
                2,
                37
            ]),
            o($V0, [
                2,
                39
            ])
        ],
        defaultActions: {},
        parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        }, "parseError"),
        parse: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parse(input) {
            var self = this, stack = [
                0
            ], tstack = [], vstack = [
                null
            ], lstack = [], table = this.table, yytext = "", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer2 = Object.create(this.lexer);
            var sharedState = {
                yy: {}
            };
            for(var k in this.yy){
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer2.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer2;
            sharedState.yy.parser = this;
            if (typeof lexer2.yylloc == "undefined") {
                lexer2.yylloc = {};
            }
            var yyloc = lexer2.yylloc;
            lstack.push(yyloc);
            var ranges = lexer2.options && lexer2.options.ranges;
            if (typeof sharedState.yy.parseError === "function") {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function popStack(n) {
                stack.length = stack.length - 2 * n;
                vstack.length = vstack.length - n;
                lstack.length = lstack.length - n;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(popStack, "popStack");
            function lex() {
                var token;
                token = tstack.pop() || lexer2.lex() || EOF;
                if (typeof token !== "number") {
                    if (token instanceof Array) {
                        tstack = token;
                        token = tstack.pop();
                    }
                    token = self.symbols_[token] || token;
                }
                return token;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(lex, "lex");
            var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;
            while(true){
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == "undefined") {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === "undefined" || !action.length || !action[0]) {
                    var errStr = "";
                    expected = [];
                    for(p in table[state]){
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer2.showPosition) {
                        errStr = "Parse error on line " + (yylineno + 1) + ":\n" + lexer2.showPosition() + "\nExpecting " + expected.join(", ") + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = "Parse error on line " + (yylineno + 1) + ": Unexpected " + (symbol == EOF ? "end of input" : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer2.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer2.yylineno,
                        loc: yyloc,
                        expected
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error("Parse Error: multiple actions possible at state: " + state + ", token: " + symbol);
                }
                switch(action[0]){
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer2.yytext);
                        lstack.push(lexer2.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        if (!preErrorSymbol) {
                            yyleng = lexer2.yyleng;
                            yytext = lexer2.yytext;
                            yylineno = lexer2.yylineno;
                            yyloc = lexer2.yylloc;
                            if (recovering > 0) {
                                recovering--;
                            }
                        } else {
                            symbol = preErrorSymbol;
                            preErrorSymbol = null;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column
                        };
                        if (ranges) {
                            yyval._$.range = [
                                lstack[lstack.length - (len || 1)].range[0],
                                lstack[lstack.length - 1].range[1]
                            ];
                        }
                        r = this.performAction.apply(yyval, [
                            yytext,
                            yyleng,
                            yylineno,
                            sharedState.yy,
                            action[1],
                            vstack,
                            lstack
                        ].concat(args));
                        if (typeof r !== "undefined") {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        }, "parse")
    };
    var lexer = /* @__PURE__ */ function() {
        var lexer2 = {
            EOF: 1,
            parseError: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            }, "parseError"),
            // resets the lexer, sets new input
            setInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = "";
                this.conditionStack = [
                    "INITIAL"
                ];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        0,
                        0
                    ];
                }
                this.offset = 0;
                return this;
            }, "setInput"),
            // consumes and returns one char from the input
            input: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }
                this._input = this._input.slice(1);
                return ch;
            }, "input"),
            // unshifts one char (or a string) into the input
            unput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);
                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);
                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;
                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len
                };
                if (this.options.ranges) {
                    this.yylloc.range = [
                        r[0],
                        r[0] + this.yyleng - len
                    ];
                }
                this.yyleng = this.yytext.length;
                return this;
            }, "unput"),
            // When called from action, caches matched text and appends it on next action
            more: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                this._more = true;
                return this;
            }, "more"),
            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
                return this;
            }, "reject"),
            // retain first n characters of the match
            less: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(n) {
                this.unput(this.match.slice(n));
            }, "less"),
            // displays already matched input, i.e. for error messages
            pastInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? "..." : "") + past.substr(-20).replace(/\n/g, "");
            }, "pastInput"),
            // displays upcoming input, i.e. for error messages
            upcomingInput: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? "..." : "")).replace(/\n/g, "");
            }, "upcomingInput"),
            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join("-");
                return pre + this.upcomingInput() + "\n" + c + "^";
            }, "showPosition"),
            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(match, indexed_rule) {
                var token, lines, backup;
                if (this.options.backtrack_lexer) {
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }
                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [
                        this.offset,
                        this.offset += this.yyleng
                    ];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    for(var k in backup){
                        this[k] = backup[k];
                    }
                    return false;
                }
                return false;
            }, "test_match"),
            // return next match in input
            next: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }
                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = "";
                    this.match = "";
                }
                var rules = this._currentRules();
                for(var i = 0; i < rules.length; i++){
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue;
                            } else {
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    return false;
                }
                if (this._input === "") {
                    return this.EOF;
                } else {
                    return this.parseError("Lexical error on line " + (this.yylineno + 1) + ". Unrecognized text.\n" + this.showPosition(), {
                        text: "",
                        token: null,
                        line: this.yylineno
                    });
                }
            }, "next"),
            // return next match that has a token
            lex: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            }, "lex"),
            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function begin(condition) {
                this.conditionStack.push(condition);
            }, "begin"),
            // pop the previously active lexer condition state off the condition stack
            popState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            }, "popState"),
            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions["INITIAL"].rules;
                }
            }, "_currentRules"),
            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return "INITIAL";
                }
            }, "topState"),
            // alias for begin(condition)
            pushState: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function pushState(condition) {
                this.begin(condition);
            }, "pushState"),
            // return the number of states currently on the stack
            stateStackSize: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function stateStackSize() {
                return this.conditionStack.length;
            }, "stateStackSize"),
            options: {
                "case-insensitive": true
            },
            performAction: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                var YYSTATE = YY_START;
                switch($avoiding_name_collisions){
                    case 0:
                        this.begin("open_directive");
                        return "open_directive";
                        //TURBOPACK unreachable
                        ;
                    case 1:
                        this.begin("acc_title");
                        return 31;
                        //TURBOPACK unreachable
                        ;
                    case 2:
                        this.popState();
                        return "acc_title_value";
                        //TURBOPACK unreachable
                        ;
                    case 3:
                        this.begin("acc_descr");
                        return 33;
                        //TURBOPACK unreachable
                        ;
                    case 4:
                        this.popState();
                        return "acc_descr_value";
                        //TURBOPACK unreachable
                        ;
                    case 5:
                        this.begin("acc_descr_multiline");
                        break;
                    case 6:
                        this.popState();
                        break;
                    case 7:
                        return "acc_descr_multiline_value";
                        //TURBOPACK unreachable
                        ;
                    case 8:
                        break;
                    case 9:
                        break;
                    case 10:
                        break;
                    case 11:
                        return 10;
                        //TURBOPACK unreachable
                        ;
                    case 12:
                        break;
                    case 13:
                        break;
                    case 14:
                        this.begin("href");
                        break;
                    case 15:
                        this.popState();
                        break;
                    case 16:
                        return 43;
                        //TURBOPACK unreachable
                        ;
                    case 17:
                        this.begin("callbackname");
                        break;
                    case 18:
                        this.popState();
                        break;
                    case 19:
                        this.popState();
                        this.begin("callbackargs");
                        break;
                    case 20:
                        return 41;
                        //TURBOPACK unreachable
                        ;
                    case 21:
                        this.popState();
                        break;
                    case 22:
                        return 42;
                        //TURBOPACK unreachable
                        ;
                    case 23:
                        this.begin("click");
                        break;
                    case 24:
                        this.popState();
                        break;
                    case 25:
                        return 40;
                        //TURBOPACK unreachable
                        ;
                    case 26:
                        return 4;
                        //TURBOPACK unreachable
                        ;
                    case 27:
                        return 22;
                        //TURBOPACK unreachable
                        ;
                    case 28:
                        return 23;
                        //TURBOPACK unreachable
                        ;
                    case 29:
                        return 24;
                        //TURBOPACK unreachable
                        ;
                    case 30:
                        return 25;
                        //TURBOPACK unreachable
                        ;
                    case 31:
                        return 26;
                        //TURBOPACK unreachable
                        ;
                    case 32:
                        return 28;
                        //TURBOPACK unreachable
                        ;
                    case 33:
                        return 27;
                        //TURBOPACK unreachable
                        ;
                    case 34:
                        return 29;
                        //TURBOPACK unreachable
                        ;
                    case 35:
                        return 12;
                        //TURBOPACK unreachable
                        ;
                    case 36:
                        return 13;
                        //TURBOPACK unreachable
                        ;
                    case 37:
                        return 14;
                        //TURBOPACK unreachable
                        ;
                    case 38:
                        return 15;
                        //TURBOPACK unreachable
                        ;
                    case 39:
                        return 16;
                        //TURBOPACK unreachable
                        ;
                    case 40:
                        return 17;
                        //TURBOPACK unreachable
                        ;
                    case 41:
                        return 18;
                        //TURBOPACK unreachable
                        ;
                    case 42:
                        return 20;
                        //TURBOPACK unreachable
                        ;
                    case 43:
                        return 21;
                        //TURBOPACK unreachable
                        ;
                    case 44:
                        return "date";
                        //TURBOPACK unreachable
                        ;
                    case 45:
                        return 30;
                        //TURBOPACK unreachable
                        ;
                    case 46:
                        return "accDescription";
                        //TURBOPACK unreachable
                        ;
                    case 47:
                        return 36;
                        //TURBOPACK unreachable
                        ;
                    case 48:
                        return 38;
                        //TURBOPACK unreachable
                        ;
                    case 49:
                        return 39;
                        //TURBOPACK unreachable
                        ;
                    case 50:
                        return ":";
                        //TURBOPACK unreachable
                        ;
                    case 51:
                        return 6;
                        //TURBOPACK unreachable
                        ;
                    case 52:
                        return "INVALID";
                        //TURBOPACK unreachable
                        ;
                }
            }, "anonymous"),
            rules: [
                /^(?:%%\{)/i,
                /^(?:accTitle\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*:\s*)/i,
                /^(?:(?!\n||)*[^\n]*)/i,
                /^(?:accDescr\s*\{\s*)/i,
                /^(?:[\}])/i,
                /^(?:[^\}]*)/i,
                /^(?:%%(?!\{)*[^\n]*)/i,
                /^(?:[^\}]%%*[^\n]*)/i,
                /^(?:%%*[^\n]*[\n]*)/i,
                /^(?:[\n]+)/i,
                /^(?:\s+)/i,
                /^(?:%[^\n]*)/i,
                /^(?:href[\s]+["])/i,
                /^(?:["])/i,
                /^(?:[^"]*)/i,
                /^(?:call[\s]+)/i,
                /^(?:\([\s]*\))/i,
                /^(?:\()/i,
                /^(?:[^(]*)/i,
                /^(?:\))/i,
                /^(?:[^)]*)/i,
                /^(?:click[\s]+)/i,
                /^(?:[\s\n])/i,
                /^(?:[^\s\n]*)/i,
                /^(?:gantt\b)/i,
                /^(?:dateFormat\s[^#\n;]+)/i,
                /^(?:inclusiveEndDates\b)/i,
                /^(?:topAxis\b)/i,
                /^(?:axisFormat\s[^#\n;]+)/i,
                /^(?:tickInterval\s[^#\n;]+)/i,
                /^(?:includes\s[^#\n;]+)/i,
                /^(?:excludes\s[^#\n;]+)/i,
                /^(?:todayMarker\s[^\n;]+)/i,
                /^(?:weekday\s+monday\b)/i,
                /^(?:weekday\s+tuesday\b)/i,
                /^(?:weekday\s+wednesday\b)/i,
                /^(?:weekday\s+thursday\b)/i,
                /^(?:weekday\s+friday\b)/i,
                /^(?:weekday\s+saturday\b)/i,
                /^(?:weekday\s+sunday\b)/i,
                /^(?:weekend\s+friday\b)/i,
                /^(?:weekend\s+saturday\b)/i,
                /^(?:\d\d\d\d-\d\d-\d\d\b)/i,
                /^(?:title\s[^\n]+)/i,
                /^(?:accDescription\s[^#\n;]+)/i,
                /^(?:section\s[^\n]+)/i,
                /^(?:[^:\n]+)/i,
                /^(?::[^#\n;]+)/i,
                /^(?::)/i,
                /^(?:$)/i,
                /^(?:.)/i
            ],
            conditions: {
                "acc_descr_multiline": {
                    "rules": [
                        6,
                        7
                    ],
                    "inclusive": false
                },
                "acc_descr": {
                    "rules": [
                        4
                    ],
                    "inclusive": false
                },
                "acc_title": {
                    "rules": [
                        2
                    ],
                    "inclusive": false
                },
                "callbackargs": {
                    "rules": [
                        21,
                        22
                    ],
                    "inclusive": false
                },
                "callbackname": {
                    "rules": [
                        18,
                        19,
                        20
                    ],
                    "inclusive": false
                },
                "href": {
                    "rules": [
                        15,
                        16
                    ],
                    "inclusive": false
                },
                "click": {
                    "rules": [
                        24,
                        25
                    ],
                    "inclusive": false
                },
                "INITIAL": {
                    "rules": [
                        0,
                        1,
                        3,
                        5,
                        8,
                        9,
                        10,
                        11,
                        12,
                        13,
                        14,
                        17,
                        23,
                        26,
                        27,
                        28,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45,
                        46,
                        47,
                        48,
                        49,
                        50,
                        51,
                        52
                    ],
                    "inclusive": true
                }
            }
        };
        return lexer2;
    }();
    parser2.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(Parser, "Parser");
    Parser.prototype = parser2;
    parser2.Parser = Parser;
    return new Parser();
}();
parser.parser = parser;
var gantt_default = parser;
;
;
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].extend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$isoWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].extend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$customParseFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].extend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$advancedFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
var WEEKEND_START_DAY = {
    friday: 5,
    saturday: 6
};
var dateFormat = "";
var axisFormat = "";
var tickInterval = void 0;
var todayMarker = "";
var includes = [];
var excludes = [];
var links = /* @__PURE__ */ new Map();
var sections = [];
var tasks = [];
var currentSection = "";
var displayMode = "";
var tags = [
    "active",
    "done",
    "crit",
    "milestone",
    "vert"
];
var funs = [];
var inclusiveEndDates = false;
var topAxis = false;
var weekday = "sunday";
var weekend = "saturday";
var lastOrder = 0;
var clear2 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    sections = [];
    tasks = [];
    currentSection = "";
    funs = [];
    taskCnt = 0;
    lastTask = void 0;
    lastTaskID = void 0;
    rawTasks = [];
    dateFormat = "";
    axisFormat = "";
    displayMode = "";
    tickInterval = void 0;
    todayMarker = "";
    includes = [];
    excludes = [];
    inclusiveEndDates = false;
    topAxis = false;
    lastOrder = 0;
    links = /* @__PURE__ */ new Map();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clear"])();
    weekday = "sunday";
    weekend = "saturday";
}, "clear");
var setAxisFormat = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    axisFormat = txt;
}, "setAxisFormat");
var getAxisFormat = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return axisFormat;
}, "getAxisFormat");
var setTickInterval = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    tickInterval = txt;
}, "setTickInterval");
var getTickInterval = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return tickInterval;
}, "getTickInterval");
var setTodayMarker = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    todayMarker = txt;
}, "setTodayMarker");
var getTodayMarker = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return todayMarker;
}, "getTodayMarker");
var setDateFormat = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    dateFormat = txt;
}, "setDateFormat");
var enableInclusiveEndDates = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    inclusiveEndDates = true;
}, "enableInclusiveEndDates");
var endDatesAreInclusive = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return inclusiveEndDates;
}, "endDatesAreInclusive");
var enableTopAxis = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    topAxis = true;
}, "enableTopAxis");
var topAxisEnabled = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return topAxis;
}, "topAxisEnabled");
var setDisplayMode = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    displayMode = txt;
}, "setDisplayMode");
var getDisplayMode = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return displayMode;
}, "getDisplayMode");
var getDateFormat = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return dateFormat;
}, "getDateFormat");
var setIncludes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    includes = txt.toLowerCase().split(/[\s,]+/);
}, "setIncludes");
var getIncludes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return includes;
}, "getIncludes");
var setExcludes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    excludes = txt.toLowerCase().split(/[\s,]+/);
}, "setExcludes");
var getExcludes = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return excludes;
}, "getExcludes");
var getLinks = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return links;
}, "getLinks");
var addSection = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    currentSection = txt;
    sections.push(txt);
}, "addSection");
var getSections = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return sections;
}, "getSections");
var getTasks = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    let allItemsProcessed = compileTasks();
    const maxDepth = 10;
    let iterationCount = 0;
    while(!allItemsProcessed && iterationCount < maxDepth){
        allItemsProcessed = compileTasks();
        iterationCount++;
    }
    tasks = rawTasks;
    return tasks;
}, "getTasks");
var isInvalidDate = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(date, dateFormat2, excludes2, includes2) {
    if (includes2.includes(date.format(dateFormat2.trim()))) {
        return false;
    }
    if (excludes2.includes("weekends") && (date.isoWeekday() === WEEKEND_START_DAY[weekend] || date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)) {
        return true;
    }
    if (excludes2.includes(date.format("dddd").toLowerCase())) {
        return true;
    }
    return excludes2.includes(date.format(dateFormat2.trim()));
}, "isInvalidDate");
var setWeekday = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(txt) {
    weekday = txt;
}, "setWeekday");
var getWeekday = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    return weekday;
}, "getWeekday");
var setWeekend = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(startDay) {
    weekend = startDay;
}, "setWeekend");
var checkTaskDates = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(task, dateFormat2, excludes2, includes2) {
    if (!excludes2.length || task.manualEndTime) {
        return;
    }
    let startTime;
    if (task.startTime instanceof Date) {
        startTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(task.startTime);
    } else {
        startTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(task.startTime, dateFormat2, true);
    }
    startTime = startTime.add(1, "d");
    let originalEndTime;
    if (task.endTime instanceof Date) {
        originalEndTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(task.endTime);
    } else {
        originalEndTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(task.endTime, dateFormat2, true);
    }
    const [fixedEndTime, renderEndTime] = fixTaskDates(startTime, originalEndTime, dateFormat2, excludes2, includes2);
    task.endTime = fixedEndTime.toDate();
    task.renderEndTime = renderEndTime;
}, "checkTaskDates");
var fixTaskDates = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(startTime, endTime, dateFormat2, excludes2, includes2) {
    let invalid = false;
    let renderEndTime = null;
    while(startTime <= endTime){
        if (!invalid) {
            renderEndTime = endTime.toDate();
        }
        invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);
        if (invalid) {
            endTime = endTime.add(1, "d");
        }
        startTime = startTime.add(1, "d");
    }
    return [
        endTime,
        renderEndTime
    ];
}, "fixTaskDates");
var getStartDate = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(prevTime, dateFormat2, str) {
    str = str.trim();
    const afterRePattern = RegExp("^after\\s+(?<ids>[\\d\\w- ]+)");
    const afterStatement = afterRePattern.exec(str);
    if (afterStatement !== null) {
        let latestTask = null;
        for (const id of afterStatement.groups.ids.split(" ")){
            let task = findTaskById(id);
            if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {
                latestTask = task;
            }
        }
        if (latestTask) {
            return latestTask.endTime;
        }
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
    }
    let mDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(str, dateFormat2.trim(), true);
    if (mDate.isValid()) {
        return mDate.toDate();
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("Invalid date:" + str);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("With date format:" + dateFormat2.trim());
        const d = new Date(str);
        if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously
        // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.
        // This can cause virtually infinite loops while rendering, so for the
        // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as
        // invalid.
        d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {
            throw new Error("Invalid date:" + str);
        }
        return d;
    }
}, "getStartDate");
var parseDuration = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(str) {
    const statement = /^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());
    if (statement !== null) {
        return [
            Number.parseFloat(statement[1]),
            statement[2]
        ];
    }
    return [
        NaN,
        "ms"
    ];
}, "parseDuration");
var getEndDate = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(prevTime, dateFormat2, str) {
    let inclusive = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
    str = str.trim();
    const untilRePattern = RegExp("^until\\s+(?<ids>[\\d\\w- ]+)");
    const untilStatement = untilRePattern.exec(str);
    if (untilStatement !== null) {
        let earliestTask = null;
        for (const id of untilStatement.groups.ids.split(" ")){
            let task = findTaskById(id);
            if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {
                earliestTask = task;
            }
        }
        if (earliestTask) {
            return earliestTask.startTime;
        }
        const today = /* @__PURE__ */ new Date();
        today.setHours(0, 0, 0, 0);
        return today;
    }
    let parsedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(str, dateFormat2.trim(), true);
    if (parsedDate.isValid()) {
        if (inclusive) {
            parsedDate = parsedDate.add(1, "d");
        }
        return parsedDate.toDate();
    }
    let endTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prevTime);
    const [durationValue, durationUnit] = parseDuration(str);
    if (!Number.isNaN(durationValue)) {
        const newEndTime = endTime.add(durationValue, durationUnit);
        if (newEndTime.isValid()) {
            endTime = newEndTime;
        }
    }
    return endTime.toDate();
}, "getEndDate");
var taskCnt = 0;
var parseId = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(idStr) {
    if (idStr === void 0) {
        taskCnt = taskCnt + 1;
        return "task" + taskCnt;
    }
    return idStr;
}, "parseId");
var compileData = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(prevTask, dataStr) {
    let ds;
    if (dataStr.substr(0, 1) === ":") {
        ds = dataStr.substr(1, dataStr.length);
    } else {
        ds = dataStr;
    }
    const data = ds.split(",");
    const task = {};
    getTaskTags(data, task, tags);
    for(let i = 0; i < data.length; i++){
        data[i] = data[i].trim();
    }
    let endTimeData = "";
    switch(data.length){
        case 1:
            task.id = parseId();
            task.startTime = prevTask.endTime;
            endTimeData = data[0];
            break;
        case 2:
            task.id = parseId();
            task.startTime = getStartDate(void 0, dateFormat, data[0]);
            endTimeData = data[1];
            break;
        case 3:
            task.id = parseId(data[0]);
            task.startTime = getStartDate(void 0, dateFormat, data[1]);
            endTimeData = data[2];
            break;
        default:
    }
    if (endTimeData) {
        task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);
        task.manualEndTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(endTimeData, "YYYY-MM-DD", true).isValid();
        checkTaskDates(task, dateFormat, excludes, includes);
    }
    return task;
}, "compileData");
var parseData = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(prevTaskId, dataStr) {
    let ds;
    if (dataStr.substr(0, 1) === ":") {
        ds = dataStr.substr(1, dataStr.length);
    } else {
        ds = dataStr;
    }
    const data = ds.split(",");
    const task = {};
    getTaskTags(data, task, tags);
    for(let i = 0; i < data.length; i++){
        data[i] = data[i].trim();
    }
    switch(data.length){
        case 1:
            task.id = parseId();
            task.startTime = {
                type: "prevTaskEnd",
                id: prevTaskId
            };
            task.endTime = {
                data: data[0]
            };
            break;
        case 2:
            task.id = parseId();
            task.startTime = {
                type: "getStartDate",
                startData: data[0]
            };
            task.endTime = {
                data: data[1]
            };
            break;
        case 3:
            task.id = parseId(data[0]);
            task.startTime = {
                type: "getStartDate",
                startData: data[1]
            };
            task.endTime = {
                data: data[2]
            };
            break;
        default:
    }
    return task;
}, "parseData");
var lastTask;
var lastTaskID;
var rawTasks = [];
var taskDb = {};
var addTask = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(descr, data) {
    const rawTask = {
        section: currentSection,
        type: currentSection,
        processed: false,
        manualEndTime: false,
        renderEndTime: null,
        raw: {
            data
        },
        task: descr,
        classes: []
    };
    const taskInfo = parseData(lastTaskID, data);
    rawTask.raw.startTime = taskInfo.startTime;
    rawTask.raw.endTime = taskInfo.endTime;
    rawTask.id = taskInfo.id;
    rawTask.prevTaskId = lastTaskID;
    rawTask.active = taskInfo.active;
    rawTask.done = taskInfo.done;
    rawTask.crit = taskInfo.crit;
    rawTask.milestone = taskInfo.milestone;
    rawTask.vert = taskInfo.vert;
    rawTask.order = lastOrder;
    lastOrder++;
    const pos = rawTasks.push(rawTask);
    lastTaskID = rawTask.id;
    taskDb[rawTask.id] = pos - 1;
}, "addTask");
var findTaskById = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(id) {
    const pos = taskDb[id];
    return rawTasks[pos];
}, "findTaskById");
var addTaskOrg = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(descr, data) {
    const newTask = {
        section: currentSection,
        type: currentSection,
        description: descr,
        task: descr,
        classes: []
    };
    const taskInfo = compileData(lastTask, data);
    newTask.startTime = taskInfo.startTime;
    newTask.endTime = taskInfo.endTime;
    newTask.id = taskInfo.id;
    newTask.active = taskInfo.active;
    newTask.done = taskInfo.done;
    newTask.crit = taskInfo.crit;
    newTask.milestone = taskInfo.milestone;
    newTask.vert = taskInfo.vert;
    lastTask = newTask;
    tasks.push(newTask);
}, "addTaskOrg");
var compileTasks = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    const compileTask = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(pos) {
        const task = rawTasks[pos];
        let startTime = "";
        switch(rawTasks[pos].raw.startTime.type){
            case "prevTaskEnd":
                {
                    const prevTask = findTaskById(task.prevTaskId);
                    task.startTime = prevTask.endTime;
                    break;
                }
            case "getStartDate":
                startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);
                if (startTime) {
                    rawTasks[pos].startTime = startTime;
                }
                break;
        }
        if (rawTasks[pos].startTime) {
            rawTasks[pos].endTime = getEndDate(rawTasks[pos].startTime, dateFormat, rawTasks[pos].raw.endTime.data, inclusiveEndDates);
            if (rawTasks[pos].endTime) {
                rawTasks[pos].processed = true;
                rawTasks[pos].manualEndTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rawTasks[pos].raw.endTime.data, "YYYY-MM-DD", true).isValid();
                checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);
            }
        }
        return rawTasks[pos].processed;
    }, "compileTask");
    let allProcessed = true;
    for (const [i, rawTask] of rawTasks.entries()){
        compileTask(i);
        allProcessed = allProcessed && rawTask.processed;
    }
    return allProcessed;
}, "compileTasks");
var setLink = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ids, _linkStr) {
    let linkStr = _linkStr;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().securityLevel !== "loose") {
        linkStr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$braintree$2f$sanitize$2d$url$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sanitizeUrl"])(_linkStr);
    }
    ids.split(",").forEach(function(id) {
        let rawTask = findTaskById(id);
        if (rawTask !== void 0) {
            pushFun(id, ()=>{
                window.open(linkStr, "_self");
            });
            links.set(id, linkStr);
        }
    });
    setClass(ids, "clickable");
}, "setLink");
var setClass = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ids, className) {
    ids.split(",").forEach(function(id) {
        let rawTask = findTaskById(id);
        if (rawTask !== void 0) {
            rawTask.classes.push(className);
        }
    });
}, "setClass");
var setClickFun = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(id, functionName, functionArgs) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().securityLevel !== "loose") {
        return;
    }
    if (functionName === void 0) {
        return;
    }
    let argList = [];
    if (typeof functionArgs === "string") {
        argList = functionArgs.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);
        for(let i = 0; i < argList.length; i++){
            let item = argList[i].trim();
            if (item.startsWith('"') && item.endsWith('"')) {
                item = item.substr(1, item.length - 2);
            }
            argList[i] = item;
        }
    }
    if (argList.length === 0) {
        argList.push(id);
    }
    let rawTask = findTaskById(id);
    if (rawTask !== void 0) {
        pushFun(id, ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$55PJQP7W$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utils_default"].runFunc(functionName, ...argList);
        });
    }
}, "setClickFun");
var pushFun = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(id, callbackFunction) {
    funs.push(function() {
        const elem = document.querySelector('[id="'.concat(id, '"]'));
        if (elem !== null) {
            elem.addEventListener("click", function() {
                callbackFunction();
            });
        }
    }, function() {
        const elem = document.querySelector('[id="'.concat(id, '-text"]'));
        if (elem !== null) {
            elem.addEventListener("click", function() {
                callbackFunction();
            });
        }
    });
}, "pushFun");
var setClickEvent = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(ids, functionName, functionArgs) {
    ids.split(",").forEach(function(id) {
        setClickFun(id, functionName, functionArgs);
    });
    setClass(ids, "clickable");
}, "setClickEvent");
var bindFunctions = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(element) {
    funs.forEach(function(fun) {
        fun(element);
    });
}, "bindFunctions");
var ganttDb_default = {
    getConfig: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().gantt, "getConfig"),
    clear: clear2,
    setDateFormat,
    getDateFormat,
    enableInclusiveEndDates,
    endDatesAreInclusive,
    enableTopAxis,
    topAxisEnabled,
    setAxisFormat,
    getAxisFormat,
    setTickInterval,
    getTickInterval,
    setTodayMarker,
    getTodayMarker,
    setAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccTitle"],
    getAccTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccTitle"],
    setDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setDiagramTitle"],
    getDiagramTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDiagramTitle"],
    setDisplayMode,
    getDisplayMode,
    setAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAccDescription"],
    getAccDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAccDescription"],
    addSection,
    getSections,
    getTasks,
    addTask,
    findTaskById,
    addTaskOrg,
    setIncludes,
    getIncludes,
    setExcludes,
    getExcludes,
    setClickEvent,
    setLink,
    getLinks,
    bindFunctions,
    parseDuration,
    isInvalidDate,
    setWeekday,
    getWeekday,
    setWeekend
};
function getTaskTags(data, task, tags2) {
    let matchFound = true;
    while(matchFound){
        matchFound = false;
        tags2.forEach(function(t) {
            const pattern = "^\\s*" + t + "\\s*$";
            const regex = new RegExp(pattern);
            if (data[0].match(regex)) {
                task[t] = true;
                data.shift(1);
                matchFound = true;
            }
        });
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(getTaskTags, "getTaskTags");
;
;
var setConf = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function() {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].debug("Something is calling, setConf, remove the call");
}, "setConf");
var mapWeekdayToTimeFunction = {
    monday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonday"],
    tuesday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeTuesday"],
    wednesday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeWednesday"],
    thursday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeThursday"],
    friday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeFriday"],
    saturday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeSaturday"],
    sunday: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$week$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeSunday"]
};
var getMaxIntersections = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((tasks2, orderOffset)=>{
    let timeline = [
        ...tasks2
    ].map(()=>-Infinity);
    let sorted = [
        ...tasks2
    ].sort((a, b)=>a.startTime - b.startTime || a.order - b.order);
    let maxIntersections = 0;
    for (const element of sorted){
        for(let j = 0; j < timeline.length; j++){
            if (element.startTime >= timeline[j]) {
                timeline[j] = element.endTime;
                element.order = j + orderOffset;
                if (j > maxIntersections) {
                    maxIntersections = j;
                }
                break;
            }
        }
    }
    return maxIntersections;
}, "getMaxIntersections");
var w;
var draw = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(function(text, id, version, diagObj) {
    const conf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().gantt;
    const securityLevel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().securityLevel;
    let sandboxElement;
    if (securityLevel === "sandbox") {
        sandboxElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
    }
    const root = securityLevel === "sandbox" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(sandboxElement.nodes()[0].contentDocument.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("body");
    const doc = securityLevel === "sandbox" ? sandboxElement.nodes()[0].contentDocument : document;
    const elem = doc.getElementById(id);
    w = elem.parentElement.offsetWidth;
    if (w === void 0) {
        w = 1200;
    }
    if (conf.useWidth !== void 0) {
        w = conf.useWidth;
    }
    const taskArray = diagObj.db.getTasks();
    let categories = [];
    for (const element of taskArray){
        categories.push(element.type);
    }
    categories = checkUnique(categories);
    const categoryHeights = {};
    let h = 2 * conf.topPadding;
    if (diagObj.db.getDisplayMode() === "compact" || conf.displayMode === "compact") {
        const categoryElements = {};
        for (const element of taskArray){
            if (categoryElements[element.section] === void 0) {
                categoryElements[element.section] = [
                    element
                ];
            } else {
                categoryElements[element.section].push(element);
            }
        }
        let intersections = 0;
        for (const category of Object.keys(categoryElements)){
            const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;
            intersections += categoryHeight;
            h += categoryHeight * (conf.barHeight + conf.barGap);
            categoryHeights[category] = categoryHeight;
        }
    } else {
        h += taskArray.length * (conf.barHeight + conf.barGap);
        for (const category of categories){
            categoryHeights[category] = taskArray.filter((task)=>task.type === category).length;
        }
    }
    elem.setAttribute("viewBox", "0 0 " + w + " " + h);
    const svg = root.select('[id="'.concat(id, '"]'));
    const timeScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$time$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleTime$3e$__["scaleTime"])().domain([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__min$3e$__["min"])(taskArray, function(d) {
            return d.startTime;
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$array$2f$src$2f$max$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__max$3e$__["max"])(taskArray, function(d) {
            return d.endTime;
        })
    ]).rangeRound([
        0,
        w - conf.leftPadding - conf.rightPadding
    ]);
    function taskCompare(a, b) {
        const taskA = a.startTime;
        const taskB = b.startTime;
        let result = 0;
        if (taskA > taskB) {
            result = 1;
        } else if (taskA < taskB) {
            result = -1;
        }
        return result;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(taskCompare, "taskCompare");
    taskArray.sort(taskCompare);
    makeGantt(taskArray, w, h);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["configureSvgSize"])(svg, h, w, conf.useMaxWidth);
    svg.append("text").text(diagObj.db.getDiagramTitle()).attr("x", w / 2).attr("y", conf.titleTopMargin).attr("class", "titleText");
    function makeGantt(tasks2, pageWidth, pageHeight) {
        const barHeight = conf.barHeight;
        const gap = barHeight + conf.barGap;
        const topPadding = conf.topPadding;
        const leftPadding = conf.leftPadding;
        const colorScale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$scale$2f$src$2f$linear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__scaleLinear$3e$__["scaleLinear"])().domain([
            0,
            categories.length
        ]).range([
            "#00B9FA",
            "#F95002"
        ]).interpolate(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$interpolate$2f$src$2f$hcl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__interpolateHcl$3e$__["interpolateHcl"]);
        drawExcludeDays(gap, topPadding, leftPadding, pageWidth, pageHeight, tasks2, diagObj.db.getExcludes(), diagObj.db.getIncludes());
        makeGrid(leftPadding, topPadding, pageWidth, pageHeight);
        drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);
        vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);
        drawToday(leftPadding, topPadding, pageWidth, pageHeight);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(makeGantt, "makeGantt");
    function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {
        theArray.sort((a, b)=>a.vert === b.vert ? 0 : a.vert ? 1 : -1);
        const uniqueTaskOrderIds = [
            ...new Set(theArray.map((item)=>item.order))
        ];
        const uniqueTasks = uniqueTaskOrderIds.map((id2)=>theArray.find((item)=>item.order === id2));
        svg.append("g").selectAll("rect").data(uniqueTasks).enter().append("rect").attr("x", 0).attr("y", function(d, i) {
            i = d.order;
            return i * theGap + theTopPad - 2;
        }).attr("width", function() {
            return w2 - conf.rightPadding / 2;
        }).attr("height", theGap).attr("class", function(d) {
            for (const [i, category] of categories.entries()){
                if (d.type === category) {
                    return "section section" + i % conf.numberSectionStyles;
                }
            }
            return "section section0";
        }).enter();
        const rectangles = svg.append("g").selectAll("rect").data(theArray).enter();
        const links2 = diagObj.db.getLinks();
        rectangles.append("rect").attr("id", function(d) {
            return d.id;
        }).attr("rx", 3).attr("ry", 3).attr("x", function(d) {
            if (d.milestone) {
                return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;
            }
            return timeScale(d.startTime) + theSidePad;
        }).attr("y", function(d, i) {
            i = d.order;
            if (d.vert) {
                return conf.gridLineStartPadding;
            }
            return i * theGap + theTopPad;
        }).attr("width", function(d) {
            if (d.milestone) {
                return theBarHeight;
            }
            if (d.vert) {
                return 0.08 * theBarHeight;
            }
            return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);
        }).attr("height", function(d) {
            if (d.vert) {
                return taskArray.length * (conf.barHeight + conf.barGap) + conf.barHeight * 2;
            }
            return theBarHeight;
        }).attr("transform-origin", function(d, i) {
            i = d.order;
            return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + "px " + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + "px";
        }).attr("class", function(d) {
            const res = "task";
            let classStr = "";
            if (d.classes.length > 0) {
                classStr = d.classes.join(" ");
            }
            let secNum = 0;
            for (const [i, category] of categories.entries()){
                if (d.type === category) {
                    secNum = i % conf.numberSectionStyles;
                }
            }
            let taskClass = "";
            if (d.active) {
                if (d.crit) {
                    taskClass += " activeCrit";
                } else {
                    taskClass = " active";
                }
            } else if (d.done) {
                if (d.crit) {
                    taskClass = " doneCrit";
                } else {
                    taskClass = " done";
                }
            } else {
                if (d.crit) {
                    taskClass += " crit";
                }
            }
            if (taskClass.length === 0) {
                taskClass = " task";
            }
            if (d.milestone) {
                taskClass = " milestone " + taskClass;
            }
            if (d.vert) {
                taskClass = " vert " + taskClass;
            }
            taskClass += secNum;
            taskClass += " " + classStr;
            return res + taskClass;
        });
        rectangles.append("text").attr("id", function(d) {
            return d.id + "-text";
        }).text(function(d) {
            return d.task;
        }).attr("font-size", conf.fontSize).attr("x", function(d) {
            let startX = timeScale(d.startTime);
            let endX = timeScale(d.renderEndTime || d.endTime);
            if (d.milestone) {
                startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;
                endX = startX + theBarHeight;
            }
            if (d.vert) {
                return timeScale(d.startTime) + theSidePad;
            }
            const textWidth = this.getBBox().width;
            if (textWidth > endX - startX) {
                if (endX + textWidth + 1.5 * conf.leftPadding > w2) {
                    return startX + theSidePad - 5;
                } else {
                    return endX + theSidePad + 5;
                }
            } else {
                return (endX - startX) / 2 + startX + theSidePad;
            }
        }).attr("y", function(d, i) {
            if (d.vert) {
                return conf.gridLineStartPadding + taskArray.length * (conf.barHeight + conf.barGap) + 60;
            }
            i = d.order;
            return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;
        }).attr("text-height", theBarHeight).attr("class", function(d) {
            const startX = timeScale(d.startTime);
            let endX = timeScale(d.endTime);
            if (d.milestone) {
                endX = startX + theBarHeight;
            }
            const textWidth = this.getBBox().width;
            let classStr = "";
            if (d.classes.length > 0) {
                classStr = d.classes.join(" ");
            }
            let secNum = 0;
            for (const [i, category] of categories.entries()){
                if (d.type === category) {
                    secNum = i % conf.numberSectionStyles;
                }
            }
            let taskType = "";
            if (d.active) {
                if (d.crit) {
                    taskType = "activeCritText" + secNum;
                } else {
                    taskType = "activeText" + secNum;
                }
            }
            if (d.done) {
                if (d.crit) {
                    taskType = taskType + " doneCritText" + secNum;
                } else {
                    taskType = taskType + " doneText" + secNum;
                }
            } else {
                if (d.crit) {
                    taskType = taskType + " critText" + secNum;
                }
            }
            if (d.milestone) {
                taskType += " milestoneText";
            }
            if (d.vert) {
                taskType += " vertText";
            }
            if (textWidth > endX - startX) {
                if (endX + textWidth + 1.5 * conf.leftPadding > w2) {
                    return classStr + " taskTextOutsideLeft taskTextOutside" + secNum + " " + taskType;
                } else {
                    return classStr + " taskTextOutsideRight taskTextOutside" + secNum + " " + taskType + " width-" + textWidth;
                }
            } else {
                return classStr + " taskText taskText" + secNum + " " + taskType + " width-" + textWidth;
            }
        });
        const securityLevel2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig2"])().securityLevel;
        if (securityLevel2 === "sandbox") {
            let sandboxElement2;
            sandboxElement2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])("#i" + id);
            const doc2 = sandboxElement2.nodes()[0].contentDocument;
            rectangles.filter(function(d) {
                return links2.has(d.id);
            }).each(function(o) {
                var taskRect = doc2.querySelector("#" + o.id);
                var taskText = doc2.querySelector("#" + o.id + "-text");
                const oldParent = taskRect.parentNode;
                var Link = doc2.createElement("a");
                Link.setAttribute("xlink:href", links2.get(o.id));
                Link.setAttribute("target", "_top");
                oldParent.appendChild(Link);
                Link.appendChild(taskRect);
                Link.appendChild(taskText);
            });
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawRects, "drawRects");
    function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {
        if (excludes2.length === 0 && includes2.length === 0) {
            return;
        }
        let minTime;
        let maxTime;
        for (const { startTime, endTime } of tasks2){
            if (minTime === void 0 || startTime < minTime) {
                minTime = startTime;
            }
            if (maxTime === void 0 || endTime > maxTime) {
                maxTime = endTime;
            }
        }
        if (!minTime || !maxTime) {
            return;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(maxTime).diff((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(minTime), "year") > 5) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["log"].warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");
            return;
        }
        const dateFormat2 = diagObj.db.getDateFormat();
        const excludeRanges = [];
        let range = null;
        let d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(minTime);
        while(d.valueOf() <= maxTime){
            if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {
                if (!range) {
                    range = {
                        start: d,
                        end: d
                    };
                } else {
                    range.end = d;
                }
            } else {
                if (range) {
                    excludeRanges.push(range);
                    range = null;
                }
            }
            d = d.add(1, "d");
        }
        const rectangles = svg.append("g").selectAll("rect").data(excludeRanges).enter();
        rectangles.append("rect").attr("id", function(d2) {
            return "exclude-" + d2.start.format("YYYY-MM-DD");
        }).attr("x", function(d2) {
            return timeScale(d2.start) + theSidePad;
        }).attr("y", conf.gridLineStartPadding).attr("width", function(d2) {
            const renderEnd = d2.end.add(1, "day");
            return timeScale(renderEnd) - timeScale(d2.start);
        }).attr("height", h2 - theTopPad - conf.gridLineStartPadding).attr("transform-origin", function(d2, i) {
            return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + "px " + (i * theGap + 0.5 * h2).toString() + "px";
        }).attr("class", "exclude-range");
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawExcludeDays, "drawExcludeDays");
    function makeGrid(theSidePad, theTopPad, w2, h2) {
        let bottomXAxis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$axis$2f$src$2f$axis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axisBottom"])(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeFormat"])(diagObj.db.getAxisFormat() || conf.axisFormat || "%Y-%m-%d"));
        const reTickInterval = /^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/;
        const resultTickInterval = reTickInterval.exec(diagObj.db.getTickInterval() || conf.tickInterval);
        if (resultTickInterval !== null) {
            const every = resultTickInterval[1];
            const interval = resultTickInterval[2];
            const weekday2 = diagObj.db.getWeekday() || conf.weekday;
            switch(interval){
                case "millisecond":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__millisecond__as__timeMillisecond$3e$__["timeMillisecond"].every(every));
                    break;
                case "second":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__second__as__timeSecond$3e$__["timeSecond"].every(every));
                    break;
                case "minute":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMinute"].every(every));
                    break;
                case "hour":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeHour"].every(every));
                    break;
                case "day":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"].every(every));
                    break;
                case "week":
                    bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));
                    break;
                case "month":
                    bottomXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonth"].every(every));
                    break;
            }
        }
        svg.append("g").attr("class", "grid").attr("transform", "translate(" + theSidePad + ", " + (h2 - 50) + ")").call(bottomXAxis).selectAll("text").style("text-anchor", "middle").attr("fill", "#000").attr("stroke", "none").attr("font-size", 10).attr("dy", "1em");
        if (diagObj.db.topAxisEnabled() || conf.topAxis) {
            let topXAxis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$axis$2f$src$2f$axis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axisTop"])(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2d$format$2f$src$2f$defaultLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeFormat"])(diagObj.db.getAxisFormat() || conf.axisFormat || "%Y-%m-%d"));
            if (resultTickInterval !== null) {
                const every = resultTickInterval[1];
                const interval = resultTickInterval[2];
                const weekday2 = diagObj.db.getWeekday() || conf.weekday;
                switch(interval){
                    case "millisecond":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$millisecond$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__millisecond__as__timeMillisecond$3e$__["timeMillisecond"].every(every));
                        break;
                    case "second":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$second$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__second__as__timeSecond$3e$__["timeSecond"].every(every));
                        break;
                    case "minute":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$minute$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMinute"].every(every));
                        break;
                    case "hour":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$hour$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeHour"].every(every));
                        break;
                    case "day":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$day$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeDay"].every(every));
                        break;
                    case "week":
                        topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));
                        break;
                    case "month":
                        topXAxis.ticks(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$time$2f$src$2f$month$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeMonth"].every(every));
                        break;
                }
            }
            svg.append("g").attr("class", "grid").attr("transform", "translate(" + theSidePad + ", " + theTopPad + ")").call(topXAxis).selectAll("text").style("text-anchor", "middle").attr("fill", "#000").attr("stroke", "none").attr("font-size", 10);
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(makeGrid, "makeGrid");
    function vertLabels(theGap, theTopPad) {
        let prevGap = 0;
        const numOccurrences = Object.keys(categoryHeights).map((d)=>[
                d,
                categoryHeights[d]
            ]);
        svg.append("g").selectAll("text").data(numOccurrences).enter().append(function(d) {
            const rows = d[0].split(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["common_default"].lineBreakRegex);
            const dy = -(rows.length - 1) / 2;
            const svgLabel = doc.createElementNS("http://www.w3.org/2000/svg", "text");
            svgLabel.setAttribute("dy", dy + "em");
            for (const [j, row] of rows.entries()){
                const tspan = doc.createElementNS("http://www.w3.org/2000/svg", "tspan");
                tspan.setAttribute("alignment-baseline", "central");
                tspan.setAttribute("x", "10");
                if (j > 0) {
                    tspan.setAttribute("dy", "1em");
                }
                tspan.textContent = row;
                svgLabel.appendChild(tspan);
            }
            return svgLabel;
        }).attr("x", 10).attr("y", function(d, i) {
            if (i > 0) {
                for(let j = 0; j < i; j++){
                    prevGap += numOccurrences[i - 1][1];
                    return d[1] * theGap / 2 + prevGap * theGap + theTopPad;
                }
            } else {
                return d[1] * theGap / 2 + theTopPad;
            }
        }).attr("font-size", conf.sectionFontSize).attr("class", function(d) {
            for (const [i, category] of categories.entries()){
                if (d[0] === category) {
                    return "sectionTitle sectionTitle" + i % conf.numberSectionStyles;
                }
            }
            return "sectionTitle";
        });
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(vertLabels, "vertLabels");
    function drawToday(theSidePad, theTopPad, w2, h2) {
        const todayMarker2 = diagObj.db.getTodayMarker();
        if (todayMarker2 === "off") {
            return;
        }
        const todayG = svg.append("g").attr("class", "today");
        const today = /* @__PURE__ */ new Date();
        const todayLine = todayG.append("line");
        todayLine.attr("x1", timeScale(today) + theSidePad).attr("x2", timeScale(today) + theSidePad).attr("y1", conf.titleTopMargin).attr("y2", h2 - conf.titleTopMargin).attr("class", "today");
        if (todayMarker2 !== "") {
            todayLine.attr("style", todayMarker2.replace(/,/g, ";"));
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(drawToday, "drawToday");
    function checkUnique(arr) {
        const hash = {};
        const result = [];
        for(let i = 0, l = arr.length; i < l; ++i){
            if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {
                hash[arr[i]] = true;
                result.push(arr[i]);
            }
        }
        return result;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])(checkUnique, "checkUnique");
}, "draw");
var ganttRenderer_default = {
    setConf,
    draw
};
// src/diagrams/gantt/styles.js
var getStyles = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mermaid$2f$dist$2f$chunks$2f$mermaid$2e$core$2f$chunk$2d$3XYRH5AP$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__name"])((options)=>"\n  .mermaid-main-font {\n        font-family: ".concat(options.fontFamily, ";\n  }\n\n  .exclude-range {\n    fill: ").concat(options.excludeBkgColor, ";\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ").concat(options.sectionBkgColor, ";\n  }\n\n  .section2 {\n    fill: ").concat(options.sectionBkgColor2, ";\n  }\n\n  .section1,\n  .section3 {\n    fill: ").concat(options.altSectionBkgColor, ";\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ").concat(options.titleColor, ";\n  }\n\n  .sectionTitle1 {\n    fill: ").concat(options.titleColor, ";\n  }\n\n  .sectionTitle2 {\n    fill: ").concat(options.titleColor, ";\n  }\n\n  .sectionTitle3 {\n    fill: ").concat(options.titleColor, ";\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ").concat(options.fontFamily, ";\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ").concat(options.gridColor, ";\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ").concat(options.fontFamily, ";\n    fill: ").concat(options.textColor, ";\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ").concat(options.todayLineColor, ";\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ").concat(options.fontFamily, ";\n  }\n\n  .taskTextOutsideRight {\n    fill: ").concat(options.taskTextDarkColor, ";\n    text-anchor: start;\n    font-family: ").concat(options.fontFamily, ";\n  }\n\n  .taskTextOutsideLeft {\n    fill: ").concat(options.taskTextDarkColor, ";\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ").concat(options.taskTextClickableColor, " !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ").concat(options.taskTextClickableColor, " !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ").concat(options.taskTextClickableColor, " !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ").concat(options.taskTextColor, ";\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ").concat(options.taskBkgColor, ";\n    stroke: ").concat(options.taskBorderColor, ";\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ").concat(options.taskTextOutsideColor, ";\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ").concat(options.taskTextOutsideColor, ";\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ").concat(options.activeTaskBkgColor, ";\n    stroke: ").concat(options.activeTaskBorderColor, ";\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ").concat(options.taskTextDarkColor, " !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ").concat(options.doneTaskBorderColor, ";\n    fill: ").concat(options.doneTaskBkgColor, ";\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ").concat(options.taskTextDarkColor, " !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ").concat(options.critBorderColor, ";\n    fill: ").concat(options.critBkgColor, ";\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ").concat(options.critBorderColor, ";\n    fill: ").concat(options.activeTaskBkgColor, ";\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ").concat(options.critBorderColor, ";\n    fill: ").concat(options.doneTaskBkgColor, ";\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ").concat(options.taskTextDarkColor, " !important;\n  }\n\n  .vert {\n    stroke: ").concat(options.vertLineColor, ";\n  }\n\n  .vertText {\n    font-size: 15px;\n    text-anchor: middle;\n    fill: ").concat(options.vertLineColor, " !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ").concat(options.taskTextDarkColor, " !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ").concat(options.titleColor || options.textColor, ";\n    font-family: ").concat(options.fontFamily, ";\n  }\n"), "getStyles");
var styles_default = getStyles;
// src/diagrams/gantt/ganttDiagram.ts
var diagram = {
    parser: gantt_default,
    db: ganttDb_default,
    renderer: ganttRenderer_default,
    styles: styles_default
};
;
}),
}]);

//# sourceMappingURL=node_modules_a594356d._.js.map